{"logs": [{"outputFile": "com.taskmanager.app-mergeDebugResources-49:/values-si/values-si.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7e8a26d2d8a7fd613a757452c477b1bb\\transformed\\material3-release\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,288,399,516,614,711,825,954,1074,1213,1297,1403,1494,1591,1705,1833,1944,2072,2198,2330,2503,2627,2744,2864,2985,3077,3172,3291,3412,3513,3616,3720,3851,3987,4094,4191,4267,4363,4461,4547,4636,4730,4813,4896,4995,5095,5187,5288,5376,5487,5589,5701,5822,5904,6012", "endColumns": "115,116,110,116,97,96,113,128,119,138,83,105,90,96,113,127,110,127,125,131,172,123,116,119,120,91,94,118,120,100,102,103,130,135,106,96,75,95,97,85,88,93,82,82,98,99,91,100,87,110,101,111,120,81,107,98", "endOffsets": "166,283,394,511,609,706,820,949,1069,1208,1292,1398,1489,1586,1700,1828,1939,2067,2193,2325,2498,2622,2739,2859,2980,3072,3167,3286,3407,3508,3611,3715,3846,3982,4089,4186,4262,4358,4456,4542,4631,4725,4808,4891,4990,5090,5182,5283,5371,5482,5584,5696,5817,5899,6007,6106"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1465,1581,1698,1809,1926,2024,2121,2235,2364,2484,2623,2707,2813,2904,3001,3115,3243,3354,3482,3608,3740,3913,4037,4154,4274,4395,4487,4582,4701,4822,4923,5026,5130,5261,5397,5504,5601,5677,5773,5871,5957,6046,6140,6223,6306,6405,6505,6597,6698,6786,6897,6999,7111,7232,7314,7422", "endColumns": "115,116,110,116,97,96,113,128,119,138,83,105,90,96,113,127,110,127,125,131,172,123,116,119,120,91,94,118,120,100,102,103,130,135,106,96,75,95,97,85,88,93,82,82,98,99,91,100,87,110,101,111,120,81,107,98", "endOffsets": "1576,1693,1804,1921,2019,2116,2230,2359,2479,2618,2702,2808,2899,2996,3110,3238,3349,3477,3603,3735,3908,4032,4149,4269,4390,4482,4577,4696,4817,4918,5021,5125,5256,5392,5499,5596,5672,5768,5866,5952,6041,6135,6218,6301,6400,6500,6592,6693,6781,6892,6994,7106,7227,7309,7417,7516"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\dc5a69b4df93eea844116bde22da938c\\transformed\\foundation-release\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,92", "endOffsets": "140,233"}, "to": {"startLines": "83,84", "startColumns": "4,4", "startOffsets": "8440,8530", "endColumns": "89,92", "endOffsets": "8525,8618"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4f2cb26be0b26f5fa07889a18e1f2782\\transformed\\core-1.12.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,365,470,569,673,787", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "152,255,360,465,564,668,782,883"}, "to": {"startLines": "2,3,4,5,6,7,8,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,207,310,415,520,619,723,8073", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "202,305,410,515,614,718,832,8169"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2094e2c8f8f000297a612ce5427e3eff\\transformed\\ui-release\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,194,277,376,475,557,642,733,819,899,976,1051,1130,1212,1285,1366,1433", "endColumns": "88,82,98,98,81,84,90,85,79,76,74,78,81,72,80,66,117", "endOffsets": "189,272,371,470,552,637,728,814,894,971,1046,1125,1207,1280,1361,1428,1546"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "837,926,1009,1108,1207,1289,1374,7521,7607,7687,7764,7839,7918,8000,8174,8255,8322", "endColumns": "88,82,98,98,81,84,90,85,79,76,74,78,81,72,80,66,117", "endOffsets": "921,1004,1103,1202,1284,1369,1460,7602,7682,7759,7834,7913,7995,8068,8250,8317,8435"}}]}]}