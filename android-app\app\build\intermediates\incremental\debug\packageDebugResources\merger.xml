<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\res"><file name="ic_launcher_background" path="C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file path="C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="md_theme_light_primary">#4FACFE</color><color name="md_theme_light_onPrimary">#FFFFFF</color><color name="md_theme_light_primaryContainer">#E3F2FD</color><color name="md_theme_light_onPrimaryContainer">#0D47A1</color><color name="md_theme_light_secondary">#667EEA</color><color name="md_theme_light_onSecondary">#FFFFFF</color><color name="md_theme_light_secondaryContainer">#E8EAF6</color><color name="md_theme_light_onSecondaryContainer">#3F51B5</color><color name="md_theme_light_tertiary">#764BA2</color><color name="md_theme_light_onTertiary">#FFFFFF</color><color name="md_theme_light_tertiaryContainer">#F3E5F5</color><color name="md_theme_light_onTertiaryContainer">#4A148C</color><color name="md_theme_light_error">#BA1A1A</color><color name="md_theme_light_onError">#FFFFFF</color><color name="md_theme_light_errorContainer">#FFDAD6</color><color name="md_theme_light_onErrorContainer">#410002</color><color name="md_theme_light_outline">#79747E</color><color name="md_theme_light_outlineVariant">#CAC4D0</color><color name="md_theme_light_surface">#FFFBFE</color><color name="md_theme_light_onSurface">#1C1B1F</color><color name="md_theme_light_surfaceVariant">#E7E0EC</color><color name="md_theme_light_onSurfaceVariant">#49454F</color><color name="md_theme_light_inverseSurface">#313033</color><color name="md_theme_light_inverseOnSurface">#F4EFF4</color><color name="md_theme_light_inversePrimary">#B3E5FC</color></file><file path="C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">智能任务清单</string><string name="day_view">日视图</string><string name="week_view">周视图</string><string name="add_task">添加任务</string><string name="edit_task">编辑任务</string><string name="delete_task">删除任务</string><string name="copy_tasks">复制任务</string><string name="save_task">保存任务</string><string name="cancel">取消</string><string name="task_title">任务标题</string><string name="task_description">任务描述</string><string name="task_date">日期</string><string name="task_time">时间</string><string name="task_duration">持续时间</string><string name="task_priority">优先级</string><string name="task_category">分类</string><string name="priority_high">高</string><string name="priority_medium">中</string><string name="priority_low">低</string><string name="category_work">工作</string><string name="category_personal">个人</string><string name="category_study">学习</string><string name="category_health">健康</string><string name="category_other">其他</string><string name="today">今天</string><string name="previous_day">前一天</string><string name="next_day">后一天</string><string name="previous_week">上一周</string><string name="next_week">下一周</string><string name="back_to_today">回到今天</string><string name="back_to_this_week">回到本周</string><string name="no_tasks_today">今天还没有任务</string><string name="no_tasks_this_week">本周还没有任务</string><string name="add_task_hint">点击上方按钮添加任务</string><string name="task_saved">任务已保存</string><string name="task_deleted">任务已删除</string><string name="tasks_copied">任务已复制</string><string name="error_save_task">保存任务失败</string><string name="error_delete_task">删除任务失败</string><string name="error_copy_tasks">复制任务失败</string><string name="error_load_tasks">加载任务失败</string><string name="confirm_delete_task">确定要删除这个任务吗？</string><string name="confirm_copy_tasks">确定要复制所有任务到选定日期吗？</string><string name="error_title_required">请输入任务标题</string><string name="error_date_required">请选择日期</string><string name="error_time_required">请选择时间</string><string name="error_duration_invalid">持续时间必须大于0</string></file><file path="C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.TaskManager" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/md_theme_light_primary</item>
        <item name="colorOnPrimary">@color/md_theme_light_onPrimary</item>
        <item name="colorPrimaryContainer">@color/md_theme_light_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_light_onPrimaryContainer</item>
        <item name="colorSecondary">@color/md_theme_light_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_light_onSecondary</item>
        <item name="colorSecondaryContainer">@color/md_theme_light_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/md_theme_light_onSecondaryContainer</item>
        <item name="colorTertiary">@color/md_theme_light_tertiary</item>
        <item name="colorOnTertiary">@color/md_theme_light_onTertiary</item>
        <item name="colorTertiaryContainer">@color/md_theme_light_tertiaryContainer</item>
        <item name="colorOnTertiaryContainer">@color/md_theme_light_onTertiaryContainer</item>
        <item name="colorError">@color/md_theme_light_error</item>
        <item name="colorOnError">@color/md_theme_light_onError</item>
        <item name="colorErrorContainer">@color/md_theme_light_errorContainer</item>
        <item name="colorOnErrorContainer">@color/md_theme_light_onErrorContainer</item>
        <item name="colorOutline">@color/md_theme_light_outline</item>
        <item name="colorOutlineVariant">@color/md_theme_light_outlineVariant</item>
        <item name="colorSurface">@color/md_theme_light_surface</item>
        <item name="colorOnSurface">@color/md_theme_light_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_light_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_light_onSurfaceVariant</item>
        <item name="colorSurfaceInverse">@color/md_theme_light_inverseSurface</item>
        <item name="colorOnSurfaceInverse">@color/md_theme_light_inverseOnSurface</item>
        <item name="colorPrimaryInverse">@color/md_theme_light_inversePrimary</item>
    </style></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>