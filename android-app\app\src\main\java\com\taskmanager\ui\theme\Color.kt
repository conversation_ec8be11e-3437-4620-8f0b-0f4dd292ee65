package com.taskmanager.ui.theme

import androidx.compose.ui.graphics.Color

// 主色调 - 基于原Web应用的渐变色
val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)

val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)

// 自定义颜色 - 对应Web应用的渐变色
val PrimaryBlue = Color(0xFF4FACFE)
val PrimaryBlueEnd = Color(0xFF00F2FE)
val BackgroundGradientStart = Color(0xFF667EEA)
val BackgroundGradientEnd = Color(0xFF764BA2)

// 任务优先级颜色
val HighPriorityColor = Color(0xFFFF5252)
val MediumPriorityColor = Color(0xFFFF9800)
val LowPriorityColor = Color(0xFF4CAF50)

// 任务分类颜色
val WorkCategoryColor = Color(0xFF2196F3)
val PersonalCategoryColor = Color(0xFF9C27B0)
val StudyCategoryColor = Color(0xFF4CAF50)
val HealthCategoryColor = Color(0xFFFF5722)
val OtherCategoryColor = Color(0xFF607D8B)

// 其他UI颜色
val CardBackground = Color(0xFFFAFAFA)
val DividerColor = Color(0xFFE0E0E0)
val TextSecondary = Color(0xFF757575)
val SuccessColor = Color(0xFF4CAF50)
val ErrorColor = Color(0xFFF44336)
val WarningColor = Color(0xFFFF9800)
