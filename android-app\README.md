# 智能任务清单 - 安卓版

基于原Web版本重新开发的原生安卓应用，采用现代化的Android开发技术栈。

## 功能特性

### 🎯 核心功能
- **任务管理**：添加、编辑、删除任务
- **时间设置**：为每个任务设置具体的日期和时间
- **持续时间**：设置任务的预计完成时间
- **优先级**：高、中、低三个优先级别
- **分类管理**：工作、个人、学习、健康、其他

### 📅 双视图模式
- **日视图**：显示单日的所有任务，按时间轴排列
- **周视图**：显示一周的任务概览，网格布局

### 🔄 任务复制
- 可以将一天的所有任务复制到未来的任何一天
- 支持批量复制，提高效率

### 🎨 设计特色
- **Material Design 3**：采用最新的Material Design设计规范
- **现代化UI**：渐变色彩和圆角设计
- **响应式布局**：适配不同屏幕尺寸
- **原生体验**：流畅的动画和交互

## 技术栈

- **开发语言**: Kotlin
- **UI框架**: Jetpack Compose
- **架构模式**: MVVM + Repository Pattern
- **数据库**: Room (SQLite)
- **依赖注入**: Hilt
- **异步处理**: Coroutines + Flow
- **导航**: Navigation Compose

## 编译和安装

### 方法一：使用Android Studio（推荐）

1. **安装Android Studio**
   - 下载最新版本的Android Studio
   - 确保安装了Android SDK API 34

2. **导入项目**
   ```bash
   # 克隆或下载项目
   cd android-app
   ```

3. **打开项目**
   - 启动Android Studio
   - 选择 "Open an existing project"
   - 选择 `android-app` 文件夹

4. **连接设备并运行**
   - 连接Android设备或启动模拟器
   - 点击运行按钮（绿色三角形）

### 方法二：生成APK文件

1. **在Android Studio中生成APK**
   ```
   Build → Build Bundle(s) / APK(s) → Build APK(s)
   ```

2. **APK文件位置**
   ```
   app/build/outputs/apk/debug/app-debug.apk
   ```

3. **安装到手机**
   - 将APK文件传输到手机
   - 开启"允许安装未知来源应用"
   - 点击APK文件进行安装

### 方法三：命令行编译

```bash
# 进入项目目录
cd android-app

# 编译Debug版本
./gradlew assembleDebug

# 安装到连接的设备
./gradlew installDebug
```

## 系统要求

- **最低Android版本**: Android 7.0 (API 24)
- **推荐Android版本**: Android 10+ (API 29+)
- **存储空间**: 约50MB

## 项目结构

```
android-app/
├── app/
│   ├── src/main/java/com/taskmanager/
│   │   ├── data/                 # 数据层
│   │   │   ├── database/         # Room数据库
│   │   │   ├── model/            # 数据模型
│   │   │   └── repository/       # 数据仓库
│   │   ├── ui/                   # UI层
│   │   │   ├── components/       # 可复用组件
│   │   │   ├── screens/          # 界面
│   │   │   ├── theme/            # 主题样式
│   │   │   └── navigation/       # 导航
│   │   ├── viewmodel/            # ViewModel
│   │   ├── di/                   # 依赖注入
│   │   └── MainActivity.kt       # 主Activity
│   └── src/main/res/             # 资源文件
├── build.gradle.kts              # 项目配置
└── README.md                     # 说明文档
```

## 开发状态

- ✅ 基础架构搭建
- ✅ 数据库设计
- ✅ 任务管理功能
- ✅ 日视图界面
- ✅ 周视图界面
- ✅ 任务添加/编辑
- ✅ 任务复制功能
- ✅ Material Design 3主题
- 🚧 日期时间选择器（使用系统默认）
- 🚧 任务提醒功能
- 🚧 数据导出/导入

## 与Web版本对比

| 特性 | Web版本 | 安卓版本 |
|------|---------|----------|
| 技术栈 | HTML+CSS+JS | Kotlin+Compose |
| 数据存储 | localStorage | Room数据库 |
| 界面风格 | 自定义CSS | Material Design 3 |
| 性能 | 依赖浏览器 | 原生性能 |
| 离线使用 | ✅ | ✅ |
| 推送通知 | ❌ | 🚧 计划中 |

## 许可证

MIT License - 可自由使用和修改

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！
