{"logs": [{"outputFile": "com.taskmanager.app-mergeDebugResources-42:/values-zh-rTW/values-zh-rTW.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1c27dd2d2d266e151b930b964e47cb67\\transformed\\ui-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,182,258,345,436,514,588,665,743,818,883,948,1021,1096,1164,1238,1306", "endColumns": "76,75,86,90,77,73,76,77,74,64,64,72,74,67,73,67,115", "endOffsets": "177,253,340,431,509,583,660,738,813,878,943,1016,1091,1159,1233,1301,1417"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1163,1240,4411,4498,4659,4808,4882,4959,5037,5112,5177,5242,5315,5469,5801,5875,5943", "endColumns": "76,75,86,90,77,73,76,77,74,64,64,72,74,67,73,67,115", "endOffsets": "1235,1311,4493,4584,4732,4877,4954,5032,5107,5172,5237,5310,5385,5532,5870,5938,6054"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2cf887c674b6fe54b07f45a262ba7369\\transformed\\material3-1.1.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,155,254,354,452,524,608,709,810,911,1023,1101,1193,1273,1357,1458,1567,1664,1770,1874,1978,2090,2191,2288,2389,2491,2572,2663,2764,2869,2955,3053,3147,3253,3365,3461,3547,3617,3688,3767,3845,3930,4006,4084,4177,4267,4356,4445,4525,4617,4709,4799,4903,4979,5069", "endColumns": "99,98,99,97,71,83,100,100,100,111,77,91,79,83,100,108,96,105,103,103,111,100,96,100,101,80,90,100,104,85,97,93,105,111,95,85,69,70,78,77,84,75,77,92,89,88,88,79,91,91,89,103,75,89,87", "endOffsets": "150,249,349,447,519,603,704,805,906,1018,1096,1188,1268,1352,1453,1562,1659,1765,1869,1973,2085,2186,2283,2384,2486,2567,2658,2759,2864,2950,3048,3142,3248,3360,3456,3542,3612,3683,3762,3840,3925,4001,4079,4172,4262,4351,4440,4520,4612,4704,4794,4898,4974,5064,5152"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,304,404,1316,1388,1472,1573,1674,1775,1887,1965,2057,2137,2221,2322,2431,2528,2634,2738,2842,2954,3055,3152,3253,3355,3436,3527,3628,3733,3819,3917,4011,4117,4229,4325,4589,4737,5390,5537,5716,6059,6135,6213,6306,6396,6485,6574,6654,6746,6838,6928,7032,7108,7198", "endColumns": "99,98,99,97,71,83,100,100,100,111,77,91,79,83,100,108,96,105,103,103,111,100,96,100,101,80,90,100,104,85,97,93,105,111,95,85,69,70,78,77,84,75,77,92,89,88,88,79,91,91,89,103,75,89,87", "endOffsets": "200,299,399,497,1383,1467,1568,1669,1770,1882,1960,2052,2132,2216,2317,2426,2523,2629,2733,2837,2949,3050,3147,3248,3350,3431,3522,3623,3728,3814,3912,4006,4112,4224,4320,4406,4654,4803,5464,5610,5796,6130,6208,6301,6391,6480,6569,6649,6741,6833,6923,7027,7103,7193,7281"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e84e839e6aad51a26b803212332dad05\\transformed\\core-1.10.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "502,594,693,787,881,974,1067,5615", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "589,688,782,876,969,1062,1158,5711"}}]}]}