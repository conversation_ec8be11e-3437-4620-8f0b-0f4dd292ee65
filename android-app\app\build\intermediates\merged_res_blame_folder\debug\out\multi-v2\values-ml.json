{"logs": [{"outputFile": "com.taskmanager.app-mergeDebugResources-42:/values-ml/values-ml.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2cf887c674b6fe54b07f45a262ba7369\\transformed\\material3-1.1.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,293,415,534,614,704,812,960,1074,1227,1308,1409,1505,1603,1722,1851,1957,2089,2227,2354,2554,2684,2804,2931,3062,3159,3253,3367,3494,3589,3687,3796,3933,4075,4185,4289,4361,4443,4526,4619,4723,4799,4883,4977,5086,5177,5284,5370,5481,5579,5687,5815,5891,5994", "endColumns": "115,121,121,118,79,89,107,147,113,152,80,100,95,97,118,128,105,131,137,126,199,129,119,126,130,96,93,113,126,94,97,108,136,141,109,103,71,81,82,92,103,75,83,93,108,90,106,85,110,97,107,127,75,102,92", "endOffsets": "166,288,410,529,609,699,807,955,1069,1222,1303,1404,1500,1598,1717,1846,1952,2084,2222,2349,2549,2679,2799,2926,3057,3154,3248,3362,3489,3584,3682,3791,3928,4070,4180,4284,4356,4438,4521,4614,4718,4794,4878,4972,5081,5172,5279,5365,5476,5574,5682,5810,5886,5989,6082"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,343,465,1503,1583,1673,1781,1929,2043,2196,2277,2378,2474,2572,2691,2820,2926,3058,3196,3323,3523,3653,3773,3900,4031,4128,4222,4336,4463,4558,4656,4765,4902,5044,5154,5461,5623,6374,6535,6729,7095,7171,7255,7349,7458,7549,7656,7742,7853,7951,8059,8187,8263,8366", "endColumns": "115,121,121,118,79,89,107,147,113,152,80,100,95,97,118,128,105,131,137,126,199,129,119,126,130,96,93,113,126,94,97,108,136,141,109,103,71,81,82,92,103,75,83,93,108,90,106,85,110,97,107,127,75,102,92", "endOffsets": "216,338,460,579,1578,1668,1776,1924,2038,2191,2272,2373,2469,2567,2686,2815,2921,3053,3191,3318,3518,3648,3768,3895,4026,4123,4217,4331,4458,4553,4651,4760,4897,5039,5149,5253,5528,5700,6452,6623,6828,7166,7250,7344,7453,7544,7651,7737,7848,7946,8054,8182,8258,8361,8454"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e84e839e6aad51a26b803212332dad05\\transformed\\core-1.10.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,362,466,569,670,792", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "152,255,357,461,564,665,787,888"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "584,686,789,891,995,1098,1199,6628", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "681,784,886,990,1093,1194,1316,6724"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1c27dd2d2d266e151b930b964e47cb67\\transformed\\ui-release\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,287,386,490,580,666,767,854,942,1009,1076,1162,1249,1327,1403,1470", "endColumns": "94,86,98,103,89,85,100,86,87,66,66,85,86,77,75,66,118", "endOffsets": "195,282,381,485,575,661,762,849,937,1004,1071,1157,1244,1322,1398,1465,1584"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1321,1416,5258,5357,5533,5705,5791,5892,5979,6067,6134,6201,6287,6457,6833,6909,6976", "endColumns": "94,86,98,103,89,85,100,86,87,66,66,85,86,77,75,66,118", "endOffsets": "1411,1498,5352,5456,5618,5786,5887,5974,6062,6129,6196,6282,6369,6530,6904,6971,7090"}}]}]}