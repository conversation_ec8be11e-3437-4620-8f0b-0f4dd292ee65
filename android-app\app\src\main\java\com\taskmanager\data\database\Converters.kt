package com.taskmanager.data.database

import androidx.room.TypeConverter
import com.taskmanager.data.model.TaskCategory
import com.taskmanager.data.model.TaskPriority
import java.time.LocalDate
import java.time.LocalTime

class Converters {
    
    @TypeConverter
    fun fromLocalDate(date: LocalDate?): String? {
        return date?.toString()
    }
    
    @TypeConverter
    fun toLocalDate(dateString: String?): LocalDate? {
        return dateString?.let { LocalDate.parse(it) }
    }
    
    @TypeConverter
    fun fromLocalTime(time: LocalTime?): String? {
        return time?.toString()
    }
    
    @TypeConverter
    fun toLocalTime(timeString: String?): LocalTime? {
        return timeString?.let { LocalTime.parse(it) }
    }
    
    @TypeConverter
    fun fromTaskPriority(priority: TaskPriority): String {
        return priority.value
    }
    
    @TypeConverter
    fun toTaskPriority(priorityString: String): TaskPriority {
        return TaskPriority.fromValue(priorityString)
    }
    
    @TypeConverter
    fun fromTaskCategory(category: TaskCategory): String {
        return category.value
    }
    
    @TypeConverter
    fun toTaskCategory(categoryString: String): TaskCategory {
        return TaskCategory.fromValue(categoryString)
    }
}
