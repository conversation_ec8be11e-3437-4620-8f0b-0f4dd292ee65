package com.taskmanager.data.model;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0015\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0010 \n\u0002\b>\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0000\b\u0087\b\u0018\u00002\u00020\u0001B\u00b9\u0001\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0007\u001a\u00020\u0003\u0012\b\b\u0002\u0010\b\u001a\u00020\u0003\u0012\b\b\u0002\u0010\t\u001a\u00020\u0003\u0012\b\b\u0002\u0010\n\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u000b\u001a\u00020\u0003\u0012\b\b\u0002\u0010\f\u001a\u00020\u0003\u0012\b\b\u0002\u0010\r\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u000e\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u000f\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0010\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0011\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0012\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0013\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0014\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0015J\t\u0010Q\u001a\u00020\u0003H\u00c6\u0003J\t\u0010R\u001a\u00020\u0003H\u00c6\u0003J\t\u0010S\u001a\u00020\u0003H\u00c6\u0003J\t\u0010T\u001a\u00020\u0003H\u00c6\u0003J\t\u0010U\u001a\u00020\u0003H\u00c6\u0003J\t\u0010V\u001a\u00020\u0003H\u00c6\u0003J\t\u0010W\u001a\u00020\u0003H\u00c6\u0003J\t\u0010X\u001a\u00020\u0003H\u00c6\u0003J\t\u0010Y\u001a\u00020\u0003H\u00c6\u0003J\t\u0010Z\u001a\u00020\u0003H\u00c6\u0003J\t\u0010[\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\\\u001a\u00020\u0003H\u00c6\u0003J\t\u0010]\u001a\u00020\u0003H\u00c6\u0003J\t\u0010^\u001a\u00020\u0003H\u00c6\u0003J\t\u0010_\u001a\u00020\u0003H\u00c6\u0003J\t\u0010`\u001a\u00020\u0003H\u00c6\u0003J\t\u0010a\u001a\u00020\u0003H\u00c6\u0003J\t\u0010b\u001a\u00020\u0003H\u00c6\u0003J\u00bd\u0001\u0010c\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00032\b\b\u0002\u0010\u0007\u001a\u00020\u00032\b\b\u0002\u0010\b\u001a\u00020\u00032\b\b\u0002\u0010\t\u001a\u00020\u00032\b\b\u0002\u0010\n\u001a\u00020\u00032\b\b\u0002\u0010\u000b\u001a\u00020\u00032\b\b\u0002\u0010\f\u001a\u00020\u00032\b\b\u0002\u0010\r\u001a\u00020\u00032\b\b\u0002\u0010\u000e\u001a\u00020\u00032\b\b\u0002\u0010\u000f\u001a\u00020\u00032\b\b\u0002\u0010\u0010\u001a\u00020\u00032\b\b\u0002\u0010\u0011\u001a\u00020\u00032\b\b\u0002\u0010\u0012\u001a\u00020\u00032\b\b\u0002\u0010\u0013\u001a\u00020\u00032\b\b\u0002\u0010\u0014\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010d\u001a\u00020e2\b\u0010f\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\u001b\u0010g\u001a\u00020\u00192\u0006\u0010h\u001a\u00020i\u00f8\u0001\u0001\u00f8\u0001\u0000\u00a2\u0006\u0004\bj\u0010kJ\u001b\u0010l\u001a\u00020\u00192\u0006\u0010m\u001a\u00020n\u00f8\u0001\u0001\u00f8\u0001\u0000\u00a2\u0006\u0004\bo\u0010pJ\t\u0010q\u001a\u00020\u0003H\u00d6\u0001J\t\u0010r\u001a\u00020sH\u00d6\u0001R\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0017\u0010\u0018\u001a\u00020\u00198F\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0006\u001a\u0004\b\u001a\u0010\u001bR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0017R\u0017\u0010\u001d\u001a\u00020\u00198F\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0006\u001a\u0004\b\u001e\u0010\u001bR\u0011\u0010\u0011\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u0017R\u0017\u0010 \u001a\u00020\u00198F\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0006\u001a\u0004\b!\u0010\u001bR\u0011\u0010\u0012\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010\u0017R\u0017\u0010#\u001a\u00020\u00198F\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0006\u001a\u0004\b$\u0010\u001bR\u0017\u0010%\u001a\b\u0012\u0004\u0012\u00020\u00190&8F\u00a2\u0006\u0006\u001a\u0004\b\'\u0010(R\u0011\u0010\t\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b)\u0010\u0017R\u0017\u0010*\u001a\u00020\u00198F\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0006\u001a\u0004\b+\u0010\u001bR\u0011\u0010\u000b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b,\u0010\u0017R\u0017\u0010-\u001a\u00020\u00198F\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0006\u001a\u0004\b.\u0010\u001bR\u0016\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b/\u0010\u0017R\u0011\u0010\r\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b0\u0010\u0017R\u0017\u00101\u001a\u00020\u00198F\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0006\u001a\u0004\b2\u0010\u001bR\u0011\u0010\f\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b3\u0010\u0017R\u0017\u00104\u001a\u00020\u00198F\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0006\u001a\u0004\b5\u0010\u001bR\u0011\u0010\u0010\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b6\u0010\u0017R\u0017\u00107\u001a\u00020\u00198F\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0006\u001a\u0004\b8\u0010\u001bR\u0011\u0010\n\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b9\u0010\u0017R\u0017\u0010:\u001a\u00020\u00198F\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0006\u001a\u0004\b;\u0010\u001bR\u0011\u0010\u0007\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b<\u0010\u0017R\u0017\u0010=\u001a\u00020\u00198F\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0006\u001a\u0004\b>\u0010\u001bR\u0011\u0010\u0013\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b?\u0010\u0017R\u0017\u0010@\u001a\u00020\u00198F\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0006\u001a\u0004\bA\u0010\u001bR\u0011\u0010\u000e\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bB\u0010\u0017R\u0017\u0010C\u001a\u00020\u00198F\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0006\u001a\u0004\bD\u0010\u001bR\u0011\u0010\u0014\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bE\u0010\u0017R\u0017\u0010F\u001a\u00020\u00198F\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0006\u001a\u0004\bG\u0010\u001bR\u0011\u0010\u000f\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bH\u0010\u0017R\u0017\u0010I\u001a\u00020\u00198F\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0006\u001a\u0004\bJ\u0010\u001bR\u0011\u0010\b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bK\u0010\u0017R\u0017\u0010L\u001a\u00020\u00198F\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0006\u001a\u0004\bM\u0010\u001bR\u0011\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bN\u0010\u0017R\u0017\u0010O\u001a\u00020\u00198F\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0006\u001a\u0004\bP\u0010\u001b\u0082\u0002\u000b\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b!\u00a8\u0006t"}, d2 = {"Lcom/taskmanager/data/model/ThemeSettings;", "", "id", "", "backgroundStartColor", "backgroundEndColor", "workCategoryColor", "personalCategoryColor", "studyCategoryColor", "healthCategoryColor", "otherCategoryColor", "highPriorityColor", "mediumPriorityColor", "lowPriorityColor", "primaryTextColor", "secondaryTextColor", "onTaskTextColor", "cardBackgroundColor", "cardBackgroundDarkColor", "primaryColor", "primaryVariantColor", "(IIIIIIIIIIIIIIIIII)V", "getBackgroundEndColor", "()I", "backgroundEndColorCompose", "Landroidx/compose/ui/graphics/Color;", "getBackgroundEndColorCompose-0d7_KjU", "()J", "getBackgroundStartColor", "backgroundStartColorCompose", "getBackgroundStartColorCompose-0d7_KjU", "getCardBackgroundColor", "cardBackgroundColorCompose", "getCardBackgroundColorCompose-0d7_KjU", "getCardBackgroundDarkColor", "cardBackgroundDarkColorCompose", "getCardBackgroundDarkColorCompose-0d7_KjU", "gradientColors", "", "getGradientColors", "()Ljava/util/List;", "getHealthCategoryColor", "healthCategoryColorCompose", "getHealthCategoryColorCompose-0d7_KjU", "getHighPriorityColor", "highPriorityColorCompose", "getHighPriorityColorCompose-0d7_KjU", "getId", "getLowPriorityColor", "lowPriorityColorCompose", "getLowPriorityColorCompose-0d7_KjU", "getMediumPriorityColor", "mediumPriorityColorCompose", "getMediumPriorityColorCompose-0d7_KjU", "getOnTaskTextColor", "onTaskTextColorCompose", "getOnTaskTextColorCompose-0d7_KjU", "getOtherCategoryColor", "otherCategoryColorCompose", "getOtherCategoryColorCompose-0d7_KjU", "getPersonalCategoryColor", "personalCategoryColorCompose", "getPersonalCategoryColorCompose-0d7_KjU", "getPrimaryColor", "primaryColorCompose", "getPrimaryColorCompose-0d7_KjU", "getPrimaryTextColor", "primaryTextColorCompose", "getPrimaryTextColorCompose-0d7_KjU", "getPrimaryVariantColor", "primaryVariantColorCompose", "getPrimaryVariantColorCompose-0d7_KjU", "getSecondaryTextColor", "secondaryTextColorCompose", "getSecondaryTextColorCompose-0d7_KjU", "getStudyCategoryColor", "studyCategoryColorCompose", "getStudyCategoryColorCompose-0d7_KjU", "getWorkCategoryColor", "workCategoryColorCompose", "getWorkCategoryColorCompose-0d7_KjU", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "", "other", "getCategoryColor", "category", "Lcom/taskmanager/data/model/TaskCategory;", "getCategoryColor-vNxB06k", "(Lcom/taskmanager/data/model/TaskCategory;)J", "getPriorityColor", "priority", "Lcom/taskmanager/data/model/TaskPriority;", "getPriorityColor-vNxB06k", "(Lcom/taskmanager/data/model/TaskPriority;)J", "hashCode", "toString", "", "app_debug"})
@androidx.room.Entity(tableName = "theme_settings")
public final class ThemeSettings {
    @androidx.room.PrimaryKey()
    private final int id = 0;
    private final int backgroundStartColor = 0;
    private final int backgroundEndColor = 0;
    private final int workCategoryColor = 0;
    private final int personalCategoryColor = 0;
    private final int studyCategoryColor = 0;
    private final int healthCategoryColor = 0;
    private final int otherCategoryColor = 0;
    private final int highPriorityColor = 0;
    private final int mediumPriorityColor = 0;
    private final int lowPriorityColor = 0;
    private final int primaryTextColor = 0;
    private final int secondaryTextColor = 0;
    private final int onTaskTextColor = 0;
    private final int cardBackgroundColor = 0;
    private final int cardBackgroundDarkColor = 0;
    private final int primaryColor = 0;
    private final int primaryVariantColor = 0;
    
    public ThemeSettings(int id, int backgroundStartColor, int backgroundEndColor, int workCategoryColor, int personalCategoryColor, int studyCategoryColor, int healthCategoryColor, int otherCategoryColor, int highPriorityColor, int mediumPriorityColor, int lowPriorityColor, int primaryTextColor, int secondaryTextColor, int onTaskTextColor, int cardBackgroundColor, int cardBackgroundDarkColor, int primaryColor, int primaryVariantColor) {
        super();
    }
    
    public final int getId() {
        return 0;
    }
    
    public final int getBackgroundStartColor() {
        return 0;
    }
    
    public final int getBackgroundEndColor() {
        return 0;
    }
    
    public final int getWorkCategoryColor() {
        return 0;
    }
    
    public final int getPersonalCategoryColor() {
        return 0;
    }
    
    public final int getStudyCategoryColor() {
        return 0;
    }
    
    public final int getHealthCategoryColor() {
        return 0;
    }
    
    public final int getOtherCategoryColor() {
        return 0;
    }
    
    public final int getHighPriorityColor() {
        return 0;
    }
    
    public final int getMediumPriorityColor() {
        return 0;
    }
    
    public final int getLowPriorityColor() {
        return 0;
    }
    
    public final int getPrimaryTextColor() {
        return 0;
    }
    
    public final int getSecondaryTextColor() {
        return 0;
    }
    
    public final int getOnTaskTextColor() {
        return 0;
    }
    
    public final int getCardBackgroundColor() {
        return 0;
    }
    
    public final int getCardBackgroundDarkColor() {
        return 0;
    }
    
    public final int getPrimaryColor() {
        return 0;
    }
    
    public final int getPrimaryVariantColor() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<androidx.compose.ui.graphics.Color> getGradientColors() {
        return null;
    }
    
    public ThemeSettings() {
        super();
    }
    
    public final int component1() {
        return 0;
    }
    
    public final int component10() {
        return 0;
    }
    
    public final int component11() {
        return 0;
    }
    
    public final int component12() {
        return 0;
    }
    
    public final int component13() {
        return 0;
    }
    
    public final int component14() {
        return 0;
    }
    
    public final int component15() {
        return 0;
    }
    
    public final int component16() {
        return 0;
    }
    
    public final int component17() {
        return 0;
    }
    
    public final int component18() {
        return 0;
    }
    
    public final int component2() {
        return 0;
    }
    
    public final int component3() {
        return 0;
    }
    
    public final int component4() {
        return 0;
    }
    
    public final int component5() {
        return 0;
    }
    
    public final int component6() {
        return 0;
    }
    
    public final int component7() {
        return 0;
    }
    
    public final int component8() {
        return 0;
    }
    
    public final int component9() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.taskmanager.data.model.ThemeSettings copy(int id, int backgroundStartColor, int backgroundEndColor, int workCategoryColor, int personalCategoryColor, int studyCategoryColor, int healthCategoryColor, int otherCategoryColor, int highPriorityColor, int mediumPriorityColor, int lowPriorityColor, int primaryTextColor, int secondaryTextColor, int onTaskTextColor, int cardBackgroundColor, int cardBackgroundDarkColor, int primaryColor, int primaryVariantColor) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}