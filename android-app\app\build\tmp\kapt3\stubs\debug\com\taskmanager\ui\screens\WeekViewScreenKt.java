package com.taskmanager.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u00008\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\u001aP\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u00052\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00010\f2\u0012\u0010\r\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u000eH\u0003\u001a&\u0010\u000f\u001a\u00020\u00012\u0006\u0010\u0010\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\n2\f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00010\fH\u0003\u001a\u0012\u0010\u0012\u001a\u00020\u00012\b\b\u0002\u0010\u0013\u001a\u00020\u0014H\u0007\u00a8\u0006\u0015"}, d2 = {"WeekDayCard", "", "date", "Ljava/time/LocalDate;", "tasks", "", "Lcom/taskmanager/data/model/Task;", "isToday", "", "themeSettings", "Lcom/taskmanager/data/model/ThemeSettings;", "onDateClick", "Lkotlin/Function0;", "onTaskClick", "Lkotlin/Function1;", "WeekTaskItem", "task", "onClick", "WeekViewScreen", "viewModel", "Lcom/taskmanager/viewmodel/TaskViewModel;", "app_debug"})
public final class WeekViewScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void WeekViewScreen(@org.jetbrains.annotations.NotNull()
    com.taskmanager.viewmodel.TaskViewModel viewModel) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    private static final void WeekDayCard(java.time.LocalDate date, java.util.List<com.taskmanager.data.model.Task> tasks, boolean isToday, com.taskmanager.data.model.ThemeSettings themeSettings, kotlin.jvm.functions.Function0<kotlin.Unit> onDateClick, kotlin.jvm.functions.Function1<? super com.taskmanager.data.model.Task, kotlin.Unit> onTaskClick) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void WeekTaskItem(com.taskmanager.data.model.Task task, com.taskmanager.data.model.ThemeSettings themeSettings, kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
}