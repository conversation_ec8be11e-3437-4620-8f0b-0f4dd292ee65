package com.taskmanager.ui.theme

import android.content.Context
import androidx.compose.runtime.*
import androidx.compose.ui.graphics.Color
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.taskmanager.data.database.TaskDatabase
import com.taskmanager.data.model.PresetThemes
import com.taskmanager.data.model.ThemeSettings
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

class ThemeManager(context: Context) : ViewModel() {
    
    private val themeDao = TaskDatabase.getDatabase(context).themeDao()
    
    private val _currentTheme = MutableStateFlow(PresetThemes.defaultTheme)
    val currentTheme: StateFlow<ThemeSettings> = _currentTheme.asStateFlow()
    
    init {
        // 加载保存的主题设置
        viewModelScope.launch {
            themeDao.getThemeSettings().collect { savedTheme ->
                _currentTheme.value = savedTheme ?: PresetThemes.defaultTheme
            }
        }
    }
    
    fun updateTheme(newTheme: ThemeSettings) {
        viewModelScope.launch {
            themeDao.insertThemeSettings(newTheme)
            _currentTheme.value = newTheme
        }
    }
    
    fun updateBackgroundColors(startColor: Color, endColor: Color) {
        val updatedTheme = _currentTheme.value.copy(
            backgroundStartColor = startColor.value.toInt(),
            backgroundEndColor = endColor.value.toInt()
        )
        updateTheme(updatedTheme)
    }
    
    fun updateCategoryColor(category: com.taskmanager.data.model.TaskCategory, color: Color) {
        val updatedTheme = when (category) {
            com.taskmanager.data.model.TaskCategory.WORK -> 
                _currentTheme.value.copy(workCategoryColor = color.value.toInt())
            com.taskmanager.data.model.TaskCategory.PERSONAL -> 
                _currentTheme.value.copy(personalCategoryColor = color.value.toInt())
            com.taskmanager.data.model.TaskCategory.STUDY -> 
                _currentTheme.value.copy(studyCategoryColor = color.value.toInt())
            com.taskmanager.data.model.TaskCategory.HEALTH -> 
                _currentTheme.value.copy(healthCategoryColor = color.value.toInt())
            com.taskmanager.data.model.TaskCategory.OTHER -> 
                _currentTheme.value.copy(otherCategoryColor = color.value.toInt())
        }
        updateTheme(updatedTheme)
    }
    
    fun updatePriorityColor(priority: com.taskmanager.data.model.TaskPriority, color: Color) {
        val updatedTheme = when (priority) {
            com.taskmanager.data.model.TaskPriority.HIGH -> 
                _currentTheme.value.copy(highPriorityColor = color.value.toInt())
            com.taskmanager.data.model.TaskPriority.MEDIUM -> 
                _currentTheme.value.copy(mediumPriorityColor = color.value.toInt())
            com.taskmanager.data.model.TaskPriority.LOW -> 
                _currentTheme.value.copy(lowPriorityColor = color.value.toInt())
        }
        updateTheme(updatedTheme)
    }
    
    fun updateTextColors(primaryColor: Color, secondaryColor: Color, onTaskColor: Color) {
        val updatedTheme = _currentTheme.value.copy(
            primaryTextColor = primaryColor.value.toInt(),
            secondaryTextColor = secondaryColor.value.toInt(),
            onTaskTextColor = onTaskColor.value.toInt()
        )
        updateTheme(updatedTheme)
    }
    
    fun applyPresetTheme(presetTheme: ThemeSettings) {
        updateTheme(presetTheme)
    }
    
    fun resetToDefault() {
        updateTheme(PresetThemes.defaultTheme)
    }
}

// Composable函数用于提供主题
@Composable
fun ProvideCustomTheme(
    themeManager: ThemeManager,
    content: @Composable (ThemeSettings) -> Unit
) {
    val currentTheme by themeManager.currentTheme.collectAsState()
    content(currentTheme)
}

// 全局主题状态
object GlobalTheme {
    private var _themeManager: ThemeManager? = null
    
    fun initialize(context: Context) {
        if (_themeManager == null) {
            _themeManager = ThemeManager(context)
        }
    }
    
    fun getThemeManager(): ThemeManager {
        return _themeManager ?: throw IllegalStateException("ThemeManager not initialized")
    }
}
