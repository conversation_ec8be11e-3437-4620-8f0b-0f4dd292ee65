package com.taskmanager.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000<\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a4\u0010\u0000\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0012\u0010\u0005\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010\u00062\b\b\u0002\u0010\u0007\u001a\u00020\bH\u0007\u001a2\u0010\t\u001a\u00020\u00012\u0006\u0010\n\u001a\u00020\u000b2\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0012\u0010\u0005\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010\u0006H\u0003\u001a&\u0010\f\u001a\u00020\u00012\u0006\u0010\r\u001a\u00020\u00042\u0006\u0010\n\u001a\u00020\u000b2\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00010\u000fH\u0003\u001a\u0015\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u0013H\u0002\u00a2\u0006\u0002\u0010\u0014\u00a8\u0006\u0015"}, d2 = {"DayScheduleView", "", "tasks", "", "Lcom/taskmanager/data/model/Task;", "onTaskClick", "Lkotlin/Function1;", "modifier", "Landroidx/compose/ui/Modifier;", "HourSlot", "hour", "", "TaskTimeSlot", "task", "onClick", "Lkotlin/Function0;", "getCategoryColor", "Landroidx/compose/ui/graphics/Color;", "category", "Lcom/taskmanager/data/model/TaskCategory;", "(Lcom/taskmanager/data/model/TaskCategory;)J", "app_debug"})
public final class DayScheduleViewKt {
    
    @androidx.compose.runtime.Composable()
    public static final void DayScheduleView(@org.jetbrains.annotations.NotNull()
    java.util.List<com.taskmanager.data.model.Task> tasks, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.taskmanager.data.model.Task, kotlin.Unit> onTaskClick, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void HourSlot(int hour, java.util.List<com.taskmanager.data.model.Task> tasks, kotlin.jvm.functions.Function1<? super com.taskmanager.data.model.Task, kotlin.Unit> onTaskClick) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void TaskTimeSlot(com.taskmanager.data.model.Task task, int hour, kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    private static final long getCategoryColor(com.taskmanager.data.model.TaskCategory category) {
        return 0L;
    }
}