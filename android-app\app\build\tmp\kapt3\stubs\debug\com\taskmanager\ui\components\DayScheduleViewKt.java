package com.taskmanager.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000B\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a@\u0010\u0000\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0012\u0010\u0005\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010\u00062\b\b\u0002\u0010\u0007\u001a\u00020\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\nH\u0007\u001a>\u0010\u000b\u001a\u00020\u00012\u0006\u0010\f\u001a\u00020\r2\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n2\u0012\u0010\u0005\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010\u0006H\u0003\u001a2\u0010\u000e\u001a\u00020\u00012\u0006\u0010\u000f\u001a\u00020\u00042\u0006\u0010\f\u001a\u00020\r2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n2\f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00010\u0011H\u0003\u001a\u0015\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u0015H\u0002\u00a2\u0006\u0002\u0010\u0016\u00a8\u0006\u0017"}, d2 = {"DayScheduleView", "", "tasks", "", "Lcom/taskmanager/data/model/Task;", "onTaskClick", "Lkotlin/Function1;", "modifier", "Landroidx/compose/ui/Modifier;", "themeSettings", "Lcom/taskmanager/data/model/ThemeSettings;", "HourSlot", "hour", "", "TaskTimeSlot", "task", "onClick", "Lkotlin/Function0;", "getCategoryColor", "Landroidx/compose/ui/graphics/Color;", "category", "Lcom/taskmanager/data/model/TaskCategory;", "(Lcom/taskmanager/data/model/TaskCategory;)J", "app_debug"})
public final class DayScheduleViewKt {
    
    @androidx.compose.runtime.Composable()
    public static final void DayScheduleView(@org.jetbrains.annotations.NotNull()
    java.util.List<com.taskmanager.data.model.Task> tasks, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.taskmanager.data.model.Task, kotlin.Unit> onTaskClick, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.Nullable()
    com.taskmanager.data.model.ThemeSettings themeSettings) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void HourSlot(int hour, java.util.List<com.taskmanager.data.model.Task> tasks, com.taskmanager.data.model.ThemeSettings themeSettings, kotlin.jvm.functions.Function1<? super com.taskmanager.data.model.Task, kotlin.Unit> onTaskClick) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void TaskTimeSlot(com.taskmanager.data.model.Task task, int hour, com.taskmanager.data.model.ThemeSettings themeSettings, kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    private static final long getCategoryColor(com.taskmanager.data.model.TaskCategory category) {
        return 0L;
    }
}