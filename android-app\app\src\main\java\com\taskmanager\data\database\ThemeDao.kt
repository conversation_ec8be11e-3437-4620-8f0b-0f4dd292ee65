package com.taskmanager.data.database

import androidx.room.*
import com.taskmanager.data.model.ThemeSettings
import kotlinx.coroutines.flow.Flow

@Dao
interface ThemeDao {
    
    @Query("SELECT * FROM theme_settings WHERE id = 1")
    fun getThemeSettings(): Flow<ThemeSettings?>
    
    @Query("SELECT * FROM theme_settings WHERE id = 1")
    suspend fun getThemeSettingsSync(): ThemeSettings?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertThemeSettings(themeSettings: ThemeSettings)
    
    @Update
    suspend fun updateThemeSettings(themeSettings: ThemeSettings)
    
    @Query("DELETE FROM theme_settings")
    suspend fun deleteAllThemeSettings()
}
