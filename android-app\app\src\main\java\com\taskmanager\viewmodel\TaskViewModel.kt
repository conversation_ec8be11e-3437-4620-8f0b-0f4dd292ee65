package com.taskmanager.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.taskmanager.data.database.TaskDatabase
import com.taskmanager.data.model.Task
import com.taskmanager.data.repository.TaskRepository
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.time.LocalDate

class TaskViewModel(application: Application) : AndroidViewModel(application) {
    
    private val repository: TaskRepository
    
    init {
        val database = TaskDatabase.getDatabase(application)
        repository = TaskRepository(database.taskDao())
    }
    
    private val _currentDate = MutableStateFlow(LocalDate.now())
    val currentDate: StateFlow<LocalDate> = _currentDate.asStateFlow()
    
    private val _selectedTask = MutableStateFlow<Task?>(null)
    val selectedTask: StateFlow<Task?> = _selectedTask.asStateFlow()
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()
    
    // 获取当前日期的任务
    val currentDayTasks: StateFlow<List<Task>> = currentDate
        .flatMapLatest { date ->
            repository.getTasksByDate(date)
        }
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )
    
    // 获取当前周的任务
    val currentWeekTasks: StateFlow<List<Task>> = currentDate
        .flatMapLatest { date ->
            val weekStart = getWeekStart(date)
            val weekEnd = weekStart.plusDays(6)
            repository.getTasksByDateRange(weekStart, weekEnd)
        }
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )
    
    fun setCurrentDate(date: LocalDate) {
        _currentDate.value = date
    }
    
    fun navigateDate(days: Long) {
        _currentDate.value = _currentDate.value.plusDays(days)
    }
    
    fun navigateWeek(weeks: Long) {
        _currentDate.value = _currentDate.value.plusWeeks(weeks)
    }
    
    fun goToToday() {
        _currentDate.value = LocalDate.now()
    }
    
    fun selectTask(task: Task?) {
        _selectedTask.value = task
    }
    
    suspend fun getTaskById(id: String): Task? {
        return repository.getTaskById(id)
    }
    
    fun insertTask(task: Task) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                repository.insertTask(task)
                clearError()
            } catch (e: Exception) {
                _errorMessage.value = "保存任务失败: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun updateTask(task: Task) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                repository.updateTask(task)
                clearError()
            } catch (e: Exception) {
                _errorMessage.value = "更新任务失败: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun deleteTask(task: Task) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                repository.deleteTask(task)
                clearError()
            } catch (e: Exception) {
                _errorMessage.value = "删除任务失败: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun toggleTaskCompletion(taskId: String, completed: Boolean) {
        viewModelScope.launch {
            try {
                repository.updateTaskCompletion(taskId, completed)
                clearError()
            } catch (e: Exception) {
                _errorMessage.value = "更新任务状态失败: ${e.message}"
            }
        }
    }
    
    fun copyTasksToDate(sourceDate: LocalDate, targetDate: LocalDate, onResult: (Int) -> Unit) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val copiedCount = repository.copyTasksToDate(sourceDate, targetDate)
                onResult(copiedCount)
                clearError()
            } catch (e: Exception) {
                _errorMessage.value = "复制任务失败: ${e.message}"
                onResult(0)
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun clearError() {
        _errorMessage.value = null
    }
    
    private fun getWeekStart(date: LocalDate): LocalDate {
        return date.minusDays(date.dayOfWeek.value - 1L)
    }
}
