package com.taskmanager.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u000e\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u001a\u0012\u0010\u0000\u001a\u00020\u00012\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u0007\u00a8\u0006\u0004"}, d2 = {"DayViewScreen", "", "viewModel", "Lcom/taskmanager/viewmodel/TaskViewModel;", "app_debug"})
public final class DayViewScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void DayViewScreen(@org.jetbrains.annotations.NotNull()
    com.taskmanager.viewmodel.TaskViewModel viewModel) {
    }
}