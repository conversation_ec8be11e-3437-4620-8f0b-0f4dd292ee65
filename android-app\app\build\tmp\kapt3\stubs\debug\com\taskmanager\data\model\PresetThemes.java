package com.taskmanager.data.model;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u000b\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u0011\u0010\u0003\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006R\u0011\u0010\u0007\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u0006R\u0011\u0010\t\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u0006R\u0011\u0010\u000b\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\u0006R\u0011\u0010\r\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u0006\u00a8\u0006\u000f"}, d2 = {"Lcom/taskmanager/data/model/PresetThemes;", "", "()V", "darkTheme", "Lcom/taskmanager/data/model/ThemeSettings;", "getDarkTheme", "()Lcom/taskmanager/data/model/ThemeSettings;", "defaultTheme", "getDefaultTheme", "greenTheme", "getGreenTheme", "orangeTheme", "getOrangeTheme", "purpleTheme", "getPurpleTheme", "app_debug"})
public final class PresetThemes {
    @org.jetbrains.annotations.NotNull()
    private static final com.taskmanager.data.model.ThemeSettings defaultTheme = null;
    @org.jetbrains.annotations.NotNull()
    private static final com.taskmanager.data.model.ThemeSettings darkTheme = null;
    @org.jetbrains.annotations.NotNull()
    private static final com.taskmanager.data.model.ThemeSettings greenTheme = null;
    @org.jetbrains.annotations.NotNull()
    private static final com.taskmanager.data.model.ThemeSettings purpleTheme = null;
    @org.jetbrains.annotations.NotNull()
    private static final com.taskmanager.data.model.ThemeSettings orangeTheme = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.taskmanager.data.model.PresetThemes INSTANCE = null;
    
    private PresetThemes() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.taskmanager.data.model.ThemeSettings getDefaultTheme() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.taskmanager.data.model.ThemeSettings getDarkTheme() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.taskmanager.data.model.ThemeSettings getGreenTheme() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.taskmanager.data.model.ThemeSettings getPurpleTheme() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.taskmanager.data.model.ThemeSettings getOrangeTheme() {
        return null;
    }
}