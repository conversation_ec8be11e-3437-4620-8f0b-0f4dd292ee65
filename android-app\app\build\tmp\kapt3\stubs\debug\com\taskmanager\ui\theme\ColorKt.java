package com.taskmanager.ui.theme;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u0012\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000e\n\u0002\u0010 \n\u0002\b)\"\u0013\u0010\u0000\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0002\u0010\u0003\"\u0013\u0010\u0005\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0006\u0010\u0003\"\u0013\u0010\u0007\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\b\u0010\u0003\"\u0013\u0010\t\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\n\u0010\u0003\"\u0013\u0010\u000b\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\f\u0010\u0003\"\u0013\u0010\r\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u000e\u0010\u0003\"\u0017\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00010\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012\"\u0013\u0010\u0013\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0014\u0010\u0003\"\u0013\u0010\u0015\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0016\u0010\u0003\"\u0013\u0010\u0017\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0018\u0010\u0003\"\u0013\u0010\u0019\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u001a\u0010\u0003\"\u0013\u0010\u001b\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u001c\u0010\u0003\"\u0013\u0010\u001d\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u001e\u0010\u0003\"\u0013\u0010\u001f\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b \u0010\u0003\"\u0013\u0010!\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\"\u0010\u0003\"\u0013\u0010#\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b$\u0010\u0003\"\u0013\u0010%\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b&\u0010\u0003\"\u0013\u0010\'\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b(\u0010\u0003\"\u0013\u0010)\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b*\u0010\u0003\"\u0013\u0010+\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b,\u0010\u0003\"\u0013\u0010-\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b.\u0010\u0003\"\u0013\u0010/\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b0\u0010\u0003\"\u0013\u00101\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b2\u0010\u0003\"\u0013\u00103\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b4\u0010\u0003\"\u0013\u00105\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b6\u0010\u0003\"\u0013\u00107\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b8\u0010\u0003\u00a8\u00069"}, d2 = {"BackgroundGradientEnd", "Landroidx/compose/ui/graphics/Color;", "getBackgroundGradientEnd", "()J", "J", "BackgroundGradientStart", "getBackgroundGradientStart", "CardBackground", "getCardBackground", "CardBackgroundDark", "getCardBackgroundDark", "DividerColor", "getDividerColor", "ErrorColor", "getErrorColor", "GradientColors", "", "getGradientColors", "()Ljava/util/List;", "HealthCategoryColor", "getHealthCategoryColor", "HighPriorityColor", "getHighPriorityColor", "LowPriorityColor", "getLowPriorityColor", "MediumPriorityColor", "getMediumPriorityColor", "OtherCategoryColor", "getOtherCategoryColor", "PersonalCategoryColor", "getPersonalCategoryColor", "Pink40", "getPink40", "Pink80", "getPink80", "PrimaryBlue", "getPrimaryBlue", "PrimaryBlueEnd", "getPrimaryBlueEnd", "Purple40", "getPurple40", "Purple80", "getPurple80", "PurpleGrey40", "getPurpleGrey40", "PurpleGrey80", "getPurpleGrey80", "StudyCategoryColor", "getStudyCategoryColor", "SuccessColor", "getSuccessColor", "TextSecondary", "getTextSecondary", "WarningColor", "getWarningColor", "WorkCategoryColor", "getWorkCategoryColor", "app_debug"})
public final class ColorKt {
    private static final long PrimaryBlue = 0L;
    private static final long PrimaryBlueEnd = 0L;
    private static final long BackgroundGradientStart = 0L;
    private static final long BackgroundGradientEnd = 0L;
    private static final long Purple80 = 0L;
    private static final long PurpleGrey80 = 0L;
    private static final long Pink80 = 0L;
    private static final long Purple40 = 0L;
    private static final long PurpleGrey40 = 0L;
    private static final long Pink40 = 0L;
    private static final long HighPriorityColor = 0L;
    private static final long MediumPriorityColor = 0L;
    private static final long LowPriorityColor = 0L;
    private static final long WorkCategoryColor = 0L;
    private static final long PersonalCategoryColor = 0L;
    private static final long StudyCategoryColor = 0L;
    private static final long HealthCategoryColor = 0L;
    private static final long OtherCategoryColor = 0L;
    private static final long CardBackground = 0L;
    private static final long CardBackgroundDark = 0L;
    private static final long DividerColor = 0L;
    private static final long TextSecondary = 0L;
    private static final long SuccessColor = 0L;
    private static final long ErrorColor = 0L;
    private static final long WarningColor = 0L;
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<androidx.compose.ui.graphics.Color> GradientColors = null;
    
    public static final long getPrimaryBlue() {
        return 0L;
    }
    
    public static final long getPrimaryBlueEnd() {
        return 0L;
    }
    
    public static final long getBackgroundGradientStart() {
        return 0L;
    }
    
    public static final long getBackgroundGradientEnd() {
        return 0L;
    }
    
    public static final long getPurple80() {
        return 0L;
    }
    
    public static final long getPurpleGrey80() {
        return 0L;
    }
    
    public static final long getPink80() {
        return 0L;
    }
    
    public static final long getPurple40() {
        return 0L;
    }
    
    public static final long getPurpleGrey40() {
        return 0L;
    }
    
    public static final long getPink40() {
        return 0L;
    }
    
    public static final long getHighPriorityColor() {
        return 0L;
    }
    
    public static final long getMediumPriorityColor() {
        return 0L;
    }
    
    public static final long getLowPriorityColor() {
        return 0L;
    }
    
    public static final long getWorkCategoryColor() {
        return 0L;
    }
    
    public static final long getPersonalCategoryColor() {
        return 0L;
    }
    
    public static final long getStudyCategoryColor() {
        return 0L;
    }
    
    public static final long getHealthCategoryColor() {
        return 0L;
    }
    
    public static final long getOtherCategoryColor() {
        return 0L;
    }
    
    public static final long getCardBackground() {
        return 0L;
    }
    
    public static final long getCardBackgroundDark() {
        return 0L;
    }
    
    public static final long getDividerColor() {
        return 0L;
    }
    
    public static final long getTextSecondary() {
        return 0L;
    }
    
    public static final long getSuccessColor() {
        return 0L;
    }
    
    public static final long getErrorColor() {
        return 0L;
    }
    
    public static final long getWarningColor() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.util.List<androidx.compose.ui.graphics.Color> getGradientColors() {
        return null;
    }
}