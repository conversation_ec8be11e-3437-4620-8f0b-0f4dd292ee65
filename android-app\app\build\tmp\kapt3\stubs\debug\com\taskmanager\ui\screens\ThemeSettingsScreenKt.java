package com.taskmanager.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000V\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a,\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00010\u0005H\u0003\u001a*\u0010\u0007\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0018\u0010\b\u001a\u0014\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u00010\tH\u0003\u001a:\u0010\f\u001a\u00020\u00012\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u000b2\f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0011\u001a\u00020\u0012H\u0003\u00f8\u0001\u0000\u00a2\u0006\u0004\b\u0013\u0010\u0014\u001a.\u0010\u0015\u001a\u00020\u00012\u0006\u0010\u0016\u001a\u00020\u000e2\u0006\u0010\u0017\u001a\u00020\u00032\u0006\u0010\u0018\u001a\u00020\u00192\f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00010\u0005H\u0003\u001a$\u0010\u001a\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u001b\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u001cH\u0003\u001a*\u0010\u001d\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0018\u0010\u001e\u001a\u0014\u0012\u0004\u0012\u00020\u001f\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u00010\tH\u0003\u001a\u001e\u0010 \u001a\u00020\u00012\u0006\u0010!\u001a\u00020\"2\f\u0010#\u001a\b\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006$"}, d2 = {"BackgroundColorSection", "", "currentTheme", "Lcom/taskmanager/data/model/ThemeSettings;", "onStartColorClick", "Lkotlin/Function0;", "onEndColorClick", "CategoryColorSection", "onCategoryColorClick", "Lkotlin/Function2;", "Lcom/taskmanager/data/model/TaskCategory;", "Landroidx/compose/ui/graphics/Color;", "ColorSettingItem", "title", "", "color", "onClick", "modifier", "Landroidx/compose/ui/Modifier;", "ColorSettingItem-RPmYEkk", "(Ljava/lang/String;JLkotlin/jvm/functions/Function0;Landroidx/compose/ui/Modifier;)V", "PresetThemeItem", "name", "theme", "isSelected", "", "PresetThemeSection", "onThemeSelected", "Lkotlin/Function1;", "PriorityColorSection", "onPriorityColorClick", "Lcom/taskmanager/data/model/TaskPriority;", "ThemeSettingsScreen", "themeManager", "Lcom/taskmanager/ui/theme/ThemeManager;", "onBackClick", "app_debug"})
public final class ThemeSettingsScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void ThemeSettingsScreen(@org.jetbrains.annotations.NotNull()
    com.taskmanager.ui.theme.ThemeManager themeManager, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onBackClick) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void PresetThemeSection(com.taskmanager.data.model.ThemeSettings currentTheme, kotlin.jvm.functions.Function1<? super com.taskmanager.data.model.ThemeSettings, kotlin.Unit> onThemeSelected) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void PresetThemeItem(java.lang.String name, com.taskmanager.data.model.ThemeSettings theme, boolean isSelected, kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void BackgroundColorSection(com.taskmanager.data.model.ThemeSettings currentTheme, kotlin.jvm.functions.Function0<kotlin.Unit> onStartColorClick, kotlin.jvm.functions.Function0<kotlin.Unit> onEndColorClick) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void CategoryColorSection(com.taskmanager.data.model.ThemeSettings currentTheme, kotlin.jvm.functions.Function2<? super com.taskmanager.data.model.TaskCategory, ? super androidx.compose.ui.graphics.Color, kotlin.Unit> onCategoryColorClick) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void PriorityColorSection(com.taskmanager.data.model.ThemeSettings currentTheme, kotlin.jvm.functions.Function2<? super com.taskmanager.data.model.TaskPriority, ? super androidx.compose.ui.graphics.Color, kotlin.Unit> onPriorityColorClick) {
    }
}