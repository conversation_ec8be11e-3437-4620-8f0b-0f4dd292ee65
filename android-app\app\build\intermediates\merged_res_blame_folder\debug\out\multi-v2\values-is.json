{"logs": [{"outputFile": "com.taskmanager.app-mergeDebugResources-42:/values-is/values-is.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1c27dd2d2d266e151b930b964e47cb67\\transformed\\ui-release\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,196,277,376,475,560,640,735,824,906,974,1042,1120,1203,1273,1350,1418", "endColumns": "90,80,98,98,84,79,94,88,81,67,67,77,82,69,76,67,119", "endOffsets": "191,272,371,470,555,635,730,819,901,969,1037,1115,1198,1268,1345,1413,1533"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1251,1342,4995,5094,5265,5425,5505,5600,5689,5771,5839,5907,5985,6149,6506,6583,6651", "endColumns": "90,80,98,98,84,79,94,88,81,67,67,77,82,69,76,67,119", "endOffsets": "1337,1418,5089,5188,5345,5500,5595,5684,5766,5834,5902,5980,6063,6214,6578,6646,6766"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e84e839e6aad51a26b803212332dad05\\transformed\\core-1.10.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,257,354,454,557,661,772", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "145,252,349,449,552,656,767,868"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "534,629,736,833,933,1036,1140,6300", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "624,731,828,928,1031,1135,1246,6396"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2cf887c674b6fe54b07f45a262ba7369\\transformed\\material3-1.1.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,165,273,376,484,560,655,768,905,1028,1159,1245,1342,1435,1531,1642,1764,1866,1987,2107,2245,2412,2531,2643,2759,2878,2972,3066,3171,3289,3393,3497,3596,3721,3852,3956,4056,4128,4203,4284,4365,4470,4546,4633,4730,4827,4918,5022,5106,5207,5304,5405,5521,5597,5695", "endColumns": "109,107,102,107,75,94,112,136,122,130,85,96,92,95,110,121,101,120,119,137,166,118,111,115,118,93,93,104,117,103,103,98,124,130,103,99,71,74,80,80,104,75,86,96,96,90,103,83,100,96,100,115,75,97,91", "endOffsets": "160,268,371,479,555,650,763,900,1023,1154,1240,1337,1430,1526,1637,1759,1861,1982,2102,2240,2407,2526,2638,2754,2873,2967,3061,3166,3284,3388,3492,3591,3716,3847,3951,4051,4123,4198,4279,4360,4465,4541,4628,4725,4822,4913,5017,5101,5202,5299,5400,5516,5592,5690,5782"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,323,426,1423,1499,1594,1707,1844,1967,2098,2184,2281,2374,2470,2581,2703,2805,2926,3046,3184,3351,3470,3582,3698,3817,3911,4005,4110,4228,4332,4436,4535,4660,4791,4895,5193,5350,6068,6219,6401,6771,6847,6934,7031,7128,7219,7323,7407,7508,7605,7706,7822,7898,7996", "endColumns": "109,107,102,107,75,94,112,136,122,130,85,96,92,95,110,121,101,120,119,137,166,118,111,115,118,93,93,104,117,103,103,98,124,130,103,99,71,74,80,80,104,75,86,96,96,90,103,83,100,96,100,115,75,97,91", "endOffsets": "210,318,421,529,1494,1589,1702,1839,1962,2093,2179,2276,2369,2465,2576,2698,2800,2921,3041,3179,3346,3465,3577,3693,3812,3906,4000,4105,4223,4327,4431,4530,4655,4786,4890,4990,5260,5420,6144,6295,6501,6842,6929,7026,7123,7214,7318,7402,7503,7600,7701,7817,7893,7991,8083"}}]}]}