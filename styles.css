/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.app-container {
    max-width: 1400px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.95);
    min-height: 100vh;
    box-shadow: 0 0 50px rgba(0, 0, 0, 0.1);
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 1rem 2rem;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.app-title {
    font-size: 1.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.view-controls {
    display: flex;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.25rem;
    border-radius: 12px;
}

.view-btn {
    padding: 0.5rem 1rem;
    border: none;
    background: transparent;
    color: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.view-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.view-btn.active {
    background: rgba(255, 255, 255, 0.3);
    font-weight: 600;
}

.date-navigation {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.nav-btn {
    padding: 0.5rem;
    border: none;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    height: 40px;
}

.nav-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.today-btn {
    padding: 0.5rem 1rem;
    font-weight: 600;
}

.current-date {
    font-size: 1.1rem;
    font-weight: 600;
    min-width: 200px;
    text-align: center;
}

/* 主要内容区域 */
.main-content {
    padding: 2rem;
}

.view-container {
    display: none;
}

.view-container.active {
    display: block;
}

/* 日视图样式 */
.day-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.day-title {
    font-size: 1.5rem;
    color: #2c3e50;
    font-weight: 600;
}

.day-actions {
    display: flex;
    gap: 1rem;
}

.action-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.action-btn:not(.secondary) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.action-btn.secondary {
    background: #f8f9fa;
    color: #6c757d;
    border: 2px solid #e9ecef;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* 时间轴样式 */
.timeline-container {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.timeline {
    position: relative;
    padding: 1rem;
}

.time-slot {
    display: flex;
    align-items: stretch;
    min-height: 60px;
    border-bottom: 1px solid #f0f0f0;
    position: relative;
}

.time-slot:last-child {
    border-bottom: none;
}

.time-label {
    width: 80px;
    padding: 1rem 0.5rem;
    font-size: 0.9rem;
    color: #666;
    font-weight: 500;
    display: flex;
    align-items: center;
    border-right: 2px solid #f0f0f0;
}

.time-content {
    flex: 1;
    padding: 0.5rem;
    position: relative;
}

/* 任务卡片样式 */
.task-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.75rem;
    border-radius: 8px;
    margin: 0.25rem 0;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.task-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.task-card.priority-high {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.task-card.priority-medium {
    background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
}

.task-card.priority-low {
    background: linear-gradient(135deg, #48dbfb 0%, #0abde3 100%);
}

.task-card.category-work {
    background: linear-gradient(135deg, #3742fa 0%, #2f3542 100%);
}

.task-card.category-personal {
    background: linear-gradient(135deg, #2ed573 0%, #1e90ff 100%);
}

.task-card.category-study {
    background: linear-gradient(135deg, #ffa502 0%, #ff6348 100%);
}

.task-card.category-health {
    background: linear-gradient(135deg, #ff3838 0%, #ff9500 100%);
}

.task-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
}

.task-time {
    font-size: 0.8rem;
    opacity: 0.9;
    margin-bottom: 0.25rem;
}

.task-description {
    font-size: 0.8rem;
    opacity: 0.8;
    line-height: 1.3;
}

.task-actions {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    display: flex;
    gap: 0.25rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.task-card:hover .task-actions {
    opacity: 1;
}

.task-action-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 0.25rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.7rem;
    transition: background 0.3s ease;
}

.task-action-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* 周视图样式 */
.week-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.week-title {
    font-size: 1.5rem;
    color: #2c3e50;
    font-weight: 600;
}

.week-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1rem;
    background: white;
    padding: 1rem;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.week-day {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1rem;
    min-height: 400px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.week-day:hover {
    border-color: #667eea;
    transform: translateY(-2px);
}

.week-day.today {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.week-day-header {
    text-align: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid rgba(0, 0, 0, 0.1);
}

.week-day.today .week-day-header {
    border-bottom-color: rgba(255, 255, 255, 0.3);
}

.week-day-name {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.week-day-date {
    font-size: 1.2rem;
    font-weight: 700;
}

.week-tasks {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.week-task {
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    padding: 0.5rem;
    border-radius: 6px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border-left: 4px solid #667eea;
}

.week-day.today .week-task {
    background: rgba(255, 255, 255, 0.95);
}

.week-task:hover {
    transform: translateX(2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.week-task.priority-high {
    border-left-color: #ff6b6b;
}

.week-task.priority-medium {
    border-left-color: #feca57;
}

.week-task.priority-low {
    border-left-color: #48dbfb;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 16px;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #f0f0f0;
}

.modal-header h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: #666;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: #f0f0f0;
    color: #333;
}

/* 表单样式 */
.task-form {
    padding: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #f0f0f0;
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.btn.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn.secondary {
    background: #f8f9fa;
    color: #6c757d;
    border: 2px solid #e9ecef;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 复制模态框样式 */
.copy-content {
    padding: 1.5rem;
}

.date-selector {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.5rem;
    margin: 1rem 0;
}

.date-option {
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.date-option:hover {
    border-color: #667eea;
    background: #f8f9ff;
}

.date-option.selected {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: #667eea;
}

.copy-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        align-items: stretch;
    }
    
    .view-controls {
        justify-content: center;
    }
    
    .date-navigation {
        justify-content: center;
    }
    
    .day-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .week-grid {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .main-content {
        padding: 1rem;
    }
    
    .time-label {
        width: 60px;
        font-size: 0.8rem;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}
