{"logs": [{"outputFile": "com.taskmanager.app-mergeDebugResources-49:/values-te/values-te.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\dc5a69b4df93eea844116bde22da938c\\transformed\\foundation-release\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,94", "endOffsets": "138,233"}, "to": {"startLines": "83,84", "startColumns": "4,4", "startOffsets": "8785,8873", "endColumns": "87,94", "endOffsets": "8868,8963"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2094e2c8f8f000297a612ce5427e3eff\\transformed\\ui-release\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,288,385,485,574,663,759,847,931,1004,1077,1161,1251,1328,1405,1474", "endColumns": "95,86,96,99,88,88,95,87,83,72,72,83,89,76,76,68,116", "endOffsets": "196,283,380,480,569,658,754,842,926,999,1072,1156,1246,1323,1400,1469,1586"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "855,951,1038,1135,1235,1324,1413,7852,7940,8024,8097,8170,8254,8344,8522,8599,8668", "endColumns": "95,86,96,99,88,88,95,87,83,72,72,83,89,76,76,68,116", "endOffsets": "946,1033,1130,1230,1319,1408,1504,7935,8019,8092,8165,8249,8339,8416,8594,8663,8780"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4f2cb26be0b26f5fa07889a18e1f2782\\transformed\\core-1.12.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,265,367,468,574,681,805", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "152,260,362,463,569,676,800,901"}, "to": {"startLines": "2,3,4,5,6,7,8,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,207,315,417,518,624,731,8421", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "202,310,412,513,619,726,850,8517"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7e8a26d2d8a7fd613a757452c477b1bb\\transformed\\material3-release\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,184,315,429,559,663,762,878,1019,1131,1274,1358,1461,1557,1655,1771,1901,2009,2158,2305,2438,2634,2762,2878,2999,3136,3233,3330,3455,3583,3689,3795,3901,4044,4194,4302,4406,4482,4581,4682,4776,4868,4975,5055,5138,5239,5367,5461,5573,5661,5772,5874,5991,6114,6194,6301", "endColumns": "128,130,113,129,103,98,115,140,111,142,83,102,95,97,115,129,107,148,146,132,195,127,115,120,136,96,96,124,127,105,105,105,142,149,107,103,75,98,100,93,91,106,79,82,100,127,93,111,87,110,101,116,122,79,106,96", "endOffsets": "179,310,424,554,658,757,873,1014,1126,1269,1353,1456,1552,1650,1766,1896,2004,2153,2300,2433,2629,2757,2873,2994,3131,3228,3325,3450,3578,3684,3790,3896,4039,4189,4297,4401,4477,4576,4677,4771,4863,4970,5050,5133,5234,5362,5456,5568,5656,5767,5869,5986,6109,6189,6296,6393"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1509,1638,1769,1883,2013,2117,2216,2332,2473,2585,2728,2812,2915,3011,3109,3225,3355,3463,3612,3759,3892,4088,4216,4332,4453,4590,4687,4784,4909,5037,5143,5249,5355,5498,5648,5756,5860,5936,6035,6136,6230,6322,6429,6509,6592,6693,6821,6915,7027,7115,7226,7328,7445,7568,7648,7755", "endColumns": "128,130,113,129,103,98,115,140,111,142,83,102,95,97,115,129,107,148,146,132,195,127,115,120,136,96,96,124,127,105,105,105,142,149,107,103,75,98,100,93,91,106,79,82,100,127,93,111,87,110,101,116,122,79,106,96", "endOffsets": "1633,1764,1878,2008,2112,2211,2327,2468,2580,2723,2807,2910,3006,3104,3220,3350,3458,3607,3754,3887,4083,4211,4327,4448,4585,4682,4779,4904,5032,5138,5244,5350,5493,5643,5751,5855,5931,6030,6131,6225,6317,6424,6504,6587,6688,6816,6910,7022,7110,7221,7323,7440,7563,7643,7750,7847"}}]}]}