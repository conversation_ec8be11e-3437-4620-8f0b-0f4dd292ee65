{"logs": [{"outputFile": "com.taskmanager.app-mergeDebugResources-42:/values-sq/values-sq.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2cf887c674b6fe54b07f45a262ba7369\\transformed\\material3-1.1.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,282,397,511,587,678,787,921,1033,1175,1255,1351,1439,1534,1648,1768,1869,2002,2132,2272,2457,2591,2710,2831,2954,3043,3135,3256,3393,3484,3587,3692,3826,3967,4074,4168,4241,4318,4400,4479,4580,4656,4735,4830,4927,5018,5112,5196,5301,5397,5495,5619,5695,5805", "endColumns": "114,111,114,113,75,90,108,133,111,141,79,95,87,94,113,119,100,132,129,139,184,133,118,120,122,88,91,120,136,90,102,104,133,140,106,93,72,76,81,78,100,75,78,94,96,90,93,83,104,95,97,123,75,109,102", "endOffsets": "165,277,392,506,582,673,782,916,1028,1170,1250,1346,1434,1529,1643,1763,1864,1997,2127,2267,2452,2586,2705,2826,2949,3038,3130,3251,3388,3479,3582,3687,3821,3962,4069,4163,4236,4313,4395,4474,4575,4651,4730,4825,4922,5013,5107,5191,5296,5392,5490,5614,5690,5800,5903"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,332,447,1476,1552,1643,1752,1886,1998,2140,2220,2316,2404,2499,2613,2733,2834,2967,3097,3237,3422,3556,3675,3796,3919,4008,4100,4221,4358,4449,4552,4657,4791,4932,5039,5334,5503,6250,6405,6585,6950,7026,7105,7200,7297,7388,7482,7566,7671,7767,7865,7989,8065,8175", "endColumns": "114,111,114,113,75,90,108,133,111,141,79,95,87,94,113,119,100,132,129,139,184,133,118,120,122,88,91,120,136,90,102,104,133,140,106,93,72,76,81,78,100,75,78,94,96,90,93,83,104,95,97,123,75,109,102", "endOffsets": "215,327,442,556,1547,1638,1747,1881,1993,2135,2215,2311,2399,2494,2608,2728,2829,2962,3092,3232,3417,3551,3670,3791,3914,4003,4095,4216,4353,4444,4547,4652,4786,4927,5034,5128,5402,5575,6327,6479,6681,7021,7100,7195,7292,7383,7477,7561,7666,7762,7860,7984,8060,8170,8273"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1c27dd2d2d266e151b930b964e47cb67\\transformed\\ui-release\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,283,382,484,580,661,754,846,936,1005,1072,1159,1250,1323,1400,1466", "endColumns": "94,82,98,101,95,80,92,91,89,68,66,86,90,72,76,65,120", "endOffsets": "195,278,377,479,575,656,749,841,931,1000,1067,1154,1245,1318,1395,1461,1582"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1298,1393,5133,5232,5407,5580,5661,5754,5846,5936,6005,6072,6159,6332,6686,6763,6829", "endColumns": "94,82,98,101,95,80,92,91,89,68,66,86,90,72,76,65,120", "endOffsets": "1388,1471,5227,5329,5498,5656,5749,5841,5931,6000,6067,6154,6245,6400,6758,6824,6945"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e84e839e6aad51a26b803212332dad05\\transformed\\core-1.10.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,354,451,559,670,792", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "149,251,349,446,554,665,787,888"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "561,660,762,860,957,1065,1176,6484", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "655,757,855,952,1060,1171,1293,6580"}}]}]}