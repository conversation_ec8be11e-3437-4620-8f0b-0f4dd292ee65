  Activity android.app  Application android.app  Bundle android.app.Activity  Context android.content  Bundle android.content.Context  Bundle android.content.ContextWrapper  Build 
android.os  Bundle 
android.os  
Parcelable 
android.os  Bundle  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  Bundle #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  
background androidx.compose.foundation  	clickable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  rememberScrollState androidx.compose.foundation  verticalScroll androidx.compose.foundation  
Composable "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  androidx "androidx.compose.foundation.layout  com "androidx.compose.foundation.layout  
LazyColumn  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  RoundedCornerShape !androidx.compose.foundation.shape  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  
CalendarToday ,androidx.compose.material.icons.Icons.Filled  CalendarViewWeek ,androidx.compose.material.icons.Icons.Filled  Add &androidx.compose.material.icons.filled  
CalendarToday &androidx.compose.material.icons.filled  CalendarViewWeek &androidx.compose.material.icons.filled  Check &androidx.compose.material.icons.filled  ContentCopy &androidx.compose.material.icons.filled  Delete &androidx.compose.material.icons.filled  Edit &androidx.compose.material.icons.filled  KeyboardArrowLeft &androidx.compose.material.icons.filled  KeyboardArrowRight &androidx.compose.material.icons.filled  ColorScheme androidx.compose.material3  
Composable androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  
MaterialTheme androidx.compose.material3  
Typography androidx.compose.material3  androidx androidx.compose.material3  com androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  lightColorScheme androidx.compose.material3  
Composable androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  
SideEffect androidx.compose.runtime  androidx androidx.compose.runtime  com androidx.compose.runtime  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  clip androidx.compose.ui.draw  Brush androidx.compose.ui.graphics  Color androidx.compose.ui.graphics  toArgb androidx.compose.ui.graphics  invoke ,androidx.compose.ui.graphics.Color.Companion  ImageVector #androidx.compose.ui.graphics.vector  LocalContext androidx.compose.ui.platform  	LocalView androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  invoke ,androidx.compose.ui.text.TextStyle.Companion  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  SemiBold (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  SemiBold 2androidx.compose.ui.text.font.FontWeight.Companion  	TextAlign androidx.compose.ui.text.style  TextOverflow androidx.compose.ui.text.style  Preview #androidx.compose.ui.tooling.preview  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  sp androidx.compose.ui.unit  Dialog androidx.compose.ui.window  Bundle #androidx.core.app.ComponentActivity  WindowCompat androidx.core.view  AndroidViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  Application #androidx.lifecycle.AndroidViewModel  Boolean #androidx.lifecycle.AndroidViewModel  Int #androidx.lifecycle.AndroidViewModel  List #androidx.lifecycle.AndroidViewModel  	LocalDate #androidx.lifecycle.AndroidViewModel  Long #androidx.lifecycle.AndroidViewModel  MutableStateFlow #androidx.lifecycle.AndroidViewModel  SharingStarted #androidx.lifecycle.AndroidViewModel  	StateFlow #androidx.lifecycle.AndroidViewModel  String #androidx.lifecycle.AndroidViewModel  Task #androidx.lifecycle.AndroidViewModel  TaskRepository #androidx.lifecycle.AndroidViewModel  Unit #androidx.lifecycle.AndroidViewModel  asStateFlow #androidx.lifecycle.AndroidViewModel  	emptyList #androidx.lifecycle.AndroidViewModel  
flatMapLatest #androidx.lifecycle.AndroidViewModel  getWeekStart #androidx.lifecycle.AndroidViewModel  stateIn #androidx.lifecycle.AndroidViewModel  viewModelScope #androidx.lifecycle.AndroidViewModel  Application androidx.lifecycle.ViewModel  Boolean androidx.lifecycle.ViewModel  Int androidx.lifecycle.ViewModel  List androidx.lifecycle.ViewModel  	LocalDate androidx.lifecycle.ViewModel  Long androidx.lifecycle.ViewModel  MutableStateFlow androidx.lifecycle.ViewModel  SharingStarted androidx.lifecycle.ViewModel  	StateFlow androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  Task androidx.lifecycle.ViewModel  TaskRepository androidx.lifecycle.ViewModel  Unit androidx.lifecycle.ViewModel  asStateFlow androidx.lifecycle.ViewModel  	emptyList androidx.lifecycle.ViewModel  
flatMapLatest androidx.lifecycle.ViewModel  getWeekStart androidx.lifecycle.ViewModel  stateIn androidx.lifecycle.ViewModel  viewModelScope androidx.lifecycle.ViewModel  	viewModel $androidx.lifecycle.viewmodel.compose  NavDestination androidx.navigation  NavGraph androidx.navigation  	Companion "androidx.navigation.NavDestination  	hierarchy ,androidx.navigation.NavDestination.Companion  	Companion androidx.navigation.NavGraph  findStartDestination &androidx.navigation.NavGraph.Companion  NavHost androidx.navigation.compose  
composable androidx.navigation.compose  currentBackStackEntryAsState androidx.navigation.compose  rememberNavController androidx.navigation.compose  Dao 
androidx.room  Database 
androidx.room  Delete 
androidx.room  Entity 
androidx.room  Insert 
androidx.room  OnConflictStrategy 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  Room 
androidx.room  RoomDatabase 
androidx.room  
TypeConverter 
androidx.room  TypeConverters 
androidx.room  Update 
androidx.room  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  Context androidx.room.RoomDatabase  TaskDao androidx.room.RoomDatabase  TaskDatabase androidx.room.RoomDatabase  Volatile androidx.room.RoomDatabase  
Composable com.taskmanager  ExperimentalMaterial3Api com.taskmanager  MainActivity com.taskmanager  OptIn com.taskmanager  Screen com.taskmanager  String com.taskmanager  TaskManagerApp com.taskmanager  TaskManagerAppPreview com.taskmanager  androidx com.taskmanager  Bundle com.taskmanager.MainActivity  
CalendarToday com.taskmanager.Screen  CalendarViewWeek com.taskmanager.Screen  Icons com.taskmanager.Screen  ImageVector com.taskmanager.Screen  Screen com.taskmanager.Screen  String com.taskmanager.Screen  
CalendarToday com.taskmanager.Screen.DayView  Icons com.taskmanager.Screen.DayView  CalendarViewWeek com.taskmanager.Screen.WeekView  Icons com.taskmanager.Screen.WeekView  Boolean com.taskmanager.data.database  
Converters com.taskmanager.data.database  Dao com.taskmanager.data.database  Delete com.taskmanager.data.database  Insert com.taskmanager.data.database  Int com.taskmanager.data.database  List com.taskmanager.data.database  OnConflictStrategy com.taskmanager.data.database  Query com.taskmanager.data.database  String com.taskmanager.data.database  Task com.taskmanager.data.database  TaskDao com.taskmanager.data.database  TaskDatabase com.taskmanager.data.database  Update com.taskmanager.data.database  Volatile com.taskmanager.data.database  	LocalDate (com.taskmanager.data.database.Converters  	LocalTime (com.taskmanager.data.database.Converters  String (com.taskmanager.data.database.Converters  TaskCategory (com.taskmanager.data.database.Converters  TaskPriority (com.taskmanager.data.database.Converters  
TypeConverter (com.taskmanager.data.database.Converters  Boolean %com.taskmanager.data.database.TaskDao  Delete %com.taskmanager.data.database.TaskDao  Flow %com.taskmanager.data.database.TaskDao  Insert %com.taskmanager.data.database.TaskDao  Int %com.taskmanager.data.database.TaskDao  List %com.taskmanager.data.database.TaskDao  	LocalDate %com.taskmanager.data.database.TaskDao  OnConflictStrategy %com.taskmanager.data.database.TaskDao  Query %com.taskmanager.data.database.TaskDao  String %com.taskmanager.data.database.TaskDao  Task %com.taskmanager.data.database.TaskDao  Update %com.taskmanager.data.database.TaskDao  
deleteTask %com.taskmanager.data.database.TaskDao  deleteTaskById %com.taskmanager.data.database.TaskDao  
insertTask %com.taskmanager.data.database.TaskDao  insertTasks %com.taskmanager.data.database.TaskDao  
updateTask %com.taskmanager.data.database.TaskDao  updateTaskCompletion %com.taskmanager.data.database.TaskDao  Context *com.taskmanager.data.database.TaskDatabase  TaskDao *com.taskmanager.data.database.TaskDatabase  TaskDatabase *com.taskmanager.data.database.TaskDatabase  Volatile *com.taskmanager.data.database.TaskDatabase  Context 4com.taskmanager.data.database.TaskDatabase.Companion  TaskDao 4com.taskmanager.data.database.TaskDatabase.Companion  TaskDatabase 4com.taskmanager.data.database.TaskDatabase.Companion  Volatile 4com.taskmanager.data.database.TaskDatabase.Companion  Boolean com.taskmanager.data.model  Int com.taskmanager.data.model  String com.taskmanager.data.model  Task com.taskmanager.data.model  TaskCategory com.taskmanager.data.model  TaskPriority com.taskmanager.data.model  Boolean com.taskmanager.data.model.Task  Int com.taskmanager.data.model.Task  	LocalDate com.taskmanager.data.model.Task  	LocalTime com.taskmanager.data.model.Task  
PrimaryKey com.taskmanager.data.model.Task  String com.taskmanager.data.model.Task  TaskCategory com.taskmanager.data.model.Task  TaskPriority com.taskmanager.data.model.Task  String 'com.taskmanager.data.model.TaskCategory  TaskCategory 'com.taskmanager.data.model.TaskCategory  String 1com.taskmanager.data.model.TaskCategory.Companion  TaskCategory 1com.taskmanager.data.model.TaskCategory.Companion  String 'com.taskmanager.data.model.TaskPriority  TaskPriority 'com.taskmanager.data.model.TaskPriority  String 1com.taskmanager.data.model.TaskPriority.Companion  TaskPriority 1com.taskmanager.data.model.TaskPriority.Companion  Boolean com.taskmanager.data.repository  Int com.taskmanager.data.repository  List com.taskmanager.data.repository  String com.taskmanager.data.repository  TaskRepository com.taskmanager.data.repository  Boolean .com.taskmanager.data.repository.TaskRepository  Flow .com.taskmanager.data.repository.TaskRepository  Int .com.taskmanager.data.repository.TaskRepository  List .com.taskmanager.data.repository.TaskRepository  	LocalDate .com.taskmanager.data.repository.TaskRepository  String .com.taskmanager.data.repository.TaskRepository  Task .com.taskmanager.data.repository.TaskRepository  TaskDao .com.taskmanager.data.repository.TaskRepository  getTasksByDate .com.taskmanager.data.repository.TaskRepository  getTasksByDateRange .com.taskmanager.data.repository.TaskRepository  taskDao .com.taskmanager.data.repository.TaskRepository  
AddTaskDialog com.taskmanager.ui.components  Boolean com.taskmanager.ui.components  
Composable com.taskmanager.ui.components  CopyTasksDialog com.taskmanager.ui.components  DateOptionCard com.taskmanager.ui.components  ExperimentalMaterial3Api com.taskmanager.ui.components  Int com.taskmanager.ui.components  OptIn com.taskmanager.ui.components  TaskCard com.taskmanager.ui.components  Unit com.taskmanager.ui.components  getCategoryColor com.taskmanager.ui.components  getPriorityColor com.taskmanager.ui.components  Boolean com.taskmanager.ui.screens  
Composable com.taskmanager.ui.screens  
DayViewScreen com.taskmanager.ui.screens  ExperimentalMaterial3Api com.taskmanager.ui.screens  List com.taskmanager.ui.screens  OptIn com.taskmanager.ui.screens  Unit com.taskmanager.ui.screens  WeekDayCard com.taskmanager.ui.screens  WeekTaskItem com.taskmanager.ui.screens  WeekViewScreen com.taskmanager.ui.screens  androidx com.taskmanager.ui.screens  com com.taskmanager.ui.screens  getCategoryColor com.taskmanager.ui.screens  getPriorityColor com.taskmanager.ui.screens  BackgroundGradientEnd com.taskmanager.ui.theme  BackgroundGradientStart com.taskmanager.ui.theme  Boolean com.taskmanager.ui.theme  CardBackground com.taskmanager.ui.theme  CardBackgroundDark com.taskmanager.ui.theme  
Composable com.taskmanager.ui.theme  DarkColorScheme com.taskmanager.ui.theme  DividerColor com.taskmanager.ui.theme  
ErrorColor com.taskmanager.ui.theme  ExperimentalMaterial3Api com.taskmanager.ui.theme  GradientColors com.taskmanager.ui.theme  HealthCategoryColor com.taskmanager.ui.theme  HighPriorityColor com.taskmanager.ui.theme  LightColorScheme com.taskmanager.ui.theme  LowPriorityColor com.taskmanager.ui.theme  MediumPriorityColor com.taskmanager.ui.theme  OtherCategoryColor com.taskmanager.ui.theme  PersonalCategoryColor com.taskmanager.ui.theme  Pink40 com.taskmanager.ui.theme  Pink80 com.taskmanager.ui.theme  PrimaryBlue com.taskmanager.ui.theme  PrimaryBlueEnd com.taskmanager.ui.theme  Purple40 com.taskmanager.ui.theme  Purple80 com.taskmanager.ui.theme  PurpleGrey40 com.taskmanager.ui.theme  PurpleGrey80 com.taskmanager.ui.theme  StudyCategoryColor com.taskmanager.ui.theme  SuccessColor com.taskmanager.ui.theme  TaskManagerTheme com.taskmanager.ui.theme  
TextSecondary com.taskmanager.ui.theme  
Typography com.taskmanager.ui.theme  Unit com.taskmanager.ui.theme  WarningColor com.taskmanager.ui.theme  WorkCategoryColor com.taskmanager.ui.theme  androidx com.taskmanager.ui.theme  com com.taskmanager.ui.theme  listOf com.taskmanager.ui.theme  Boolean com.taskmanager.viewmodel  Int com.taskmanager.viewmodel  List com.taskmanager.viewmodel  	LocalDate com.taskmanager.viewmodel  Long com.taskmanager.viewmodel  MutableStateFlow com.taskmanager.viewmodel  SharingStarted com.taskmanager.viewmodel  	StateFlow com.taskmanager.viewmodel  String com.taskmanager.viewmodel  
TaskViewModel com.taskmanager.viewmodel  Unit com.taskmanager.viewmodel  asStateFlow com.taskmanager.viewmodel  	emptyList com.taskmanager.viewmodel  
flatMapLatest com.taskmanager.viewmodel  stateIn com.taskmanager.viewmodel  viewModelScope com.taskmanager.viewmodel  Application 'com.taskmanager.viewmodel.TaskViewModel  Boolean 'com.taskmanager.viewmodel.TaskViewModel  Int 'com.taskmanager.viewmodel.TaskViewModel  List 'com.taskmanager.viewmodel.TaskViewModel  	LocalDate 'com.taskmanager.viewmodel.TaskViewModel  Long 'com.taskmanager.viewmodel.TaskViewModel  MutableStateFlow 'com.taskmanager.viewmodel.TaskViewModel  SharingStarted 'com.taskmanager.viewmodel.TaskViewModel  	StateFlow 'com.taskmanager.viewmodel.TaskViewModel  String 'com.taskmanager.viewmodel.TaskViewModel  Task 'com.taskmanager.viewmodel.TaskViewModel  TaskRepository 'com.taskmanager.viewmodel.TaskViewModel  Unit 'com.taskmanager.viewmodel.TaskViewModel  _currentDate 'com.taskmanager.viewmodel.TaskViewModel  
_errorMessage 'com.taskmanager.viewmodel.TaskViewModel  
_isLoading 'com.taskmanager.viewmodel.TaskViewModel  
_selectedTask 'com.taskmanager.viewmodel.TaskViewModel  asStateFlow 'com.taskmanager.viewmodel.TaskViewModel  currentDate 'com.taskmanager.viewmodel.TaskViewModel  	emptyList 'com.taskmanager.viewmodel.TaskViewModel  
flatMapLatest 'com.taskmanager.viewmodel.TaskViewModel  getASStateFlow 'com.taskmanager.viewmodel.TaskViewModel  getAsStateFlow 'com.taskmanager.viewmodel.TaskViewModel  getEMPTYList 'com.taskmanager.viewmodel.TaskViewModel  getEmptyList 'com.taskmanager.viewmodel.TaskViewModel  getFLATMapLatest 'com.taskmanager.viewmodel.TaskViewModel  getFlatMapLatest 'com.taskmanager.viewmodel.TaskViewModel  
getSTATEIn 'com.taskmanager.viewmodel.TaskViewModel  
getStateIn 'com.taskmanager.viewmodel.TaskViewModel  getVIEWModelScope 'com.taskmanager.viewmodel.TaskViewModel  getViewModelScope 'com.taskmanager.viewmodel.TaskViewModel  getWeekStart 'com.taskmanager.viewmodel.TaskViewModel  
repository 'com.taskmanager.viewmodel.TaskViewModel  stateIn 'com.taskmanager.viewmodel.TaskViewModel  viewModelScope 'com.taskmanager.viewmodel.TaskViewModel  
Converters 	java.lang  ExperimentalMaterial3Api 	java.lang  	LocalDate 	java.lang  MutableStateFlow 	java.lang  OnConflictStrategy 	java.lang  SharingStarted 	java.lang  Task 	java.lang  androidx 	java.lang  asStateFlow 	java.lang  com 	java.lang  	emptyList 	java.lang  
flatMapLatest 	java.lang  listOf 	java.lang  stateIn 	java.lang  	LocalDate 	java.time  	LocalTime 	java.time  now java.time.LocalDate  plusDays java.time.LocalDate  DateTimeFormatter java.time.format  	TextStyle java.time.format  
Composable 	java.util  ExperimentalMaterial3Api 	java.util  androidx 	java.util  com 	java.util  Array kotlin  Boolean kotlin  
Converters kotlin  Double kotlin  ExperimentalMaterial3Api kotlin  Int kotlin  	LocalDate kotlin  Long kotlin  MutableStateFlow kotlin  Nothing kotlin  OnConflictStrategy kotlin  OptIn kotlin  SharingStarted kotlin  String kotlin  Task kotlin  Unit kotlin  Volatile kotlin  androidx kotlin  arrayOf kotlin  asStateFlow kotlin  com kotlin  	emptyList kotlin  
flatMapLatest kotlin  listOf kotlin  stateIn kotlin  getSP 
kotlin.Double  getSp 
kotlin.Double  getSP 
kotlin.Int  getSp 
kotlin.Int  
Converters kotlin.annotation  ExperimentalMaterial3Api kotlin.annotation  	LocalDate kotlin.annotation  MutableStateFlow kotlin.annotation  OnConflictStrategy kotlin.annotation  SharingStarted kotlin.annotation  Task kotlin.annotation  Volatile kotlin.annotation  androidx kotlin.annotation  asStateFlow kotlin.annotation  com kotlin.annotation  	emptyList kotlin.annotation  
flatMapLatest kotlin.annotation  listOf kotlin.annotation  stateIn kotlin.annotation  
Converters kotlin.collections  ExperimentalMaterial3Api kotlin.collections  List kotlin.collections  	LocalDate kotlin.collections  MutableStateFlow kotlin.collections  OnConflictStrategy kotlin.collections  SharingStarted kotlin.collections  Task kotlin.collections  Volatile kotlin.collections  androidx kotlin.collections  asStateFlow kotlin.collections  com kotlin.collections  	emptyList kotlin.collections  
flatMapLatest kotlin.collections  listOf kotlin.collections  stateIn kotlin.collections  
Converters kotlin.comparisons  ExperimentalMaterial3Api kotlin.comparisons  	LocalDate kotlin.comparisons  MutableStateFlow kotlin.comparisons  OnConflictStrategy kotlin.comparisons  SharingStarted kotlin.comparisons  Task kotlin.comparisons  Volatile kotlin.comparisons  androidx kotlin.comparisons  asStateFlow kotlin.comparisons  com kotlin.comparisons  	emptyList kotlin.comparisons  
flatMapLatest kotlin.comparisons  listOf kotlin.comparisons  stateIn kotlin.comparisons  SuspendFunction1 kotlin.coroutines  
Converters 	kotlin.io  ExperimentalMaterial3Api 	kotlin.io  	LocalDate 	kotlin.io  MutableStateFlow 	kotlin.io  OnConflictStrategy 	kotlin.io  SharingStarted 	kotlin.io  Task 	kotlin.io  Volatile 	kotlin.io  androidx 	kotlin.io  asStateFlow 	kotlin.io  com 	kotlin.io  	emptyList 	kotlin.io  
flatMapLatest 	kotlin.io  listOf 	kotlin.io  stateIn 	kotlin.io  
Converters 
kotlin.jvm  ExperimentalMaterial3Api 
kotlin.jvm  	LocalDate 
kotlin.jvm  MutableStateFlow 
kotlin.jvm  OnConflictStrategy 
kotlin.jvm  SharingStarted 
kotlin.jvm  Task 
kotlin.jvm  Volatile 
kotlin.jvm  androidx 
kotlin.jvm  asStateFlow 
kotlin.jvm  com 
kotlin.jvm  	emptyList 
kotlin.jvm  
flatMapLatest 
kotlin.jvm  listOf 
kotlin.jvm  stateIn 
kotlin.jvm  
Converters 
kotlin.ranges  ExperimentalMaterial3Api 
kotlin.ranges  	LocalDate 
kotlin.ranges  MutableStateFlow 
kotlin.ranges  OnConflictStrategy 
kotlin.ranges  SharingStarted 
kotlin.ranges  Task 
kotlin.ranges  Volatile 
kotlin.ranges  androidx 
kotlin.ranges  asStateFlow 
kotlin.ranges  com 
kotlin.ranges  	emptyList 
kotlin.ranges  
flatMapLatest 
kotlin.ranges  listOf 
kotlin.ranges  stateIn 
kotlin.ranges  KClass kotlin.reflect  
Converters kotlin.sequences  ExperimentalMaterial3Api kotlin.sequences  	LocalDate kotlin.sequences  MutableStateFlow kotlin.sequences  OnConflictStrategy kotlin.sequences  SharingStarted kotlin.sequences  Task kotlin.sequences  Volatile kotlin.sequences  androidx kotlin.sequences  asStateFlow kotlin.sequences  com kotlin.sequences  	emptyList kotlin.sequences  
flatMapLatest kotlin.sequences  listOf kotlin.sequences  stateIn kotlin.sequences  
Converters kotlin.text  ExperimentalMaterial3Api kotlin.text  	LocalDate kotlin.text  MutableStateFlow kotlin.text  OnConflictStrategy kotlin.text  SharingStarted kotlin.text  Task kotlin.text  Volatile kotlin.text  androidx kotlin.text  asStateFlow kotlin.text  com kotlin.text  	emptyList kotlin.text  
flatMapLatest kotlin.text  listOf kotlin.text  stateIn kotlin.text  CoroutineScope kotlinx.coroutines  launch kotlinx.coroutines  Flow kotlinx.coroutines.flow  	LocalDate kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  SharingStarted kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  	emptyList kotlinx.coroutines.flow  
flatMapLatest kotlinx.coroutines.flow  stateIn kotlinx.coroutines.flow  viewModelScope kotlinx.coroutines.flow  
getSTATEIn kotlinx.coroutines.flow.Flow  
getStateIn kotlinx.coroutines.flow.Flow  stateIn kotlinx.coroutines.flow.Flow  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getASStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getAsStateFlow (kotlinx.coroutines.flow.MutableStateFlow  WhileSubscribed &kotlinx.coroutines.flow.SharingStarted  WhileSubscribed 0kotlinx.coroutines.flow.SharingStarted.Companion  
flatMapLatest !kotlinx.coroutines.flow.StateFlow  getFLATMapLatest !kotlinx.coroutines.flow.StateFlow  getFlatMapLatest !kotlinx.coroutines.flow.StateFlow  	Parcelize kotlinx.parcelize  BorderStroke androidx.compose.foundation  	startTime com.taskmanager.data.model.Task  DayScheduleView com.taskmanager.ui.components  endTime com.taskmanager.data.model.Task  TimePickerDialog com.taskmanager.ui.components  Canvas androidx.compose.foundation  Offset androidx.compose.ui.geometry  java com.taskmanager.data.model  getJAVA com.taskmanager.data.model.Task  getJava com.taskmanager.data.model.Task  java com.taskmanager.data.model.Task  HourSlot com.taskmanager.ui.components  List com.taskmanager.ui.components  TaskTimeSlot com.taskmanager.ui.components  com com.taskmanager.ui.components  java 	java.lang  format java.time.LocalTime  getHOUR java.time.LocalTime  getHour java.time.LocalTime  	getMINUTE java.time.LocalTime  	getMinute java.time.LocalTime  hour java.time.LocalTime  minute java.time.LocalTime  setHour java.time.LocalTime  	setMinute java.time.LocalTime  	ofPattern "java.time.format.DateTimeFormatter  java kotlin  java kotlin.annotation  java kotlin.collections  java kotlin.comparisons  java 	kotlin.io  java 
kotlin.jvm  java 
kotlin.ranges  java kotlin.sequences  java kotlin.text  themeDao *com.taskmanager.data.database.TaskDatabase  ColorPickerDialog com.taskmanager.ui.components  PresetThemes com.taskmanager.data.model  ThemeDao com.taskmanager.data.database  GlobalTheme com.taskmanager.ui.theme  ThemeSettingsScreen com.taskmanager.ui.screens  
ThemeSettings com.taskmanager.data.model  ProvideCustomTheme com.taskmanager.ui.theme  ThemeManager com.taskmanager.ui.theme  border androidx.compose.foundation  LazyRow  androidx.compose.foundation.lazy  	GridCells %androidx.compose.foundation.lazy.grid  LazyVerticalGrid %androidx.compose.foundation.lazy.grid  items %androidx.compose.foundation.lazy.grid  CircleShape !androidx.compose.foundation.shape  Settings ,androidx.compose.material.icons.Icons.Filled  	ArrowBack &androidx.compose.material.icons.filled  Palette &androidx.compose.material.icons.filled  Settings &androidx.compose.material.icons.filled  MutableStateFlow androidx.compose.runtime  PresetThemes androidx.compose.runtime  	StateFlow androidx.compose.runtime  TaskDatabase androidx.compose.runtime  asStateFlow androidx.compose.runtime  	getTOArgb "androidx.compose.ui.graphics.Color  	getToArgb "androidx.compose.ui.graphics.Color  toArgb "androidx.compose.ui.graphics.Color  	ViewModel androidx.lifecycle  Color androidx.lifecycle.ViewModel  Context androidx.lifecycle.ViewModel  PresetThemes androidx.lifecycle.ViewModel  TaskDatabase androidx.lifecycle.ViewModel  
ThemeSettings androidx.lifecycle.ViewModel  com androidx.lifecycle.ViewModel  ThemeDao androidx.room.RoomDatabase  themeDao androidx.room.RoomDatabase  com com.taskmanager  Settings com.taskmanager.Screen  Icons $com.taskmanager.Screen.ThemeSettings  Settings $com.taskmanager.Screen.ThemeSettings  
ThemeSettings com.taskmanager.data.database  ThemeDao *com.taskmanager.data.database.TaskDatabase  getDatabase *com.taskmanager.data.database.TaskDatabase  ThemeDao 4com.taskmanager.data.database.TaskDatabase.Companion  getDatabase 4com.taskmanager.data.database.TaskDatabase.Companion  Flow &com.taskmanager.data.database.ThemeDao  Insert &com.taskmanager.data.database.ThemeDao  OnConflictStrategy &com.taskmanager.data.database.ThemeDao  Query &com.taskmanager.data.database.ThemeDao  
ThemeSettings &com.taskmanager.data.database.ThemeDao  Update &com.taskmanager.data.database.ThemeDao  Color com.taskmanager.data.model  List com.taskmanager.data.model  listOf com.taskmanager.data.model  toArgb com.taskmanager.data.model  Color 'com.taskmanager.data.model.PresetThemes  
ThemeSettings 'com.taskmanager.data.model.PresetThemes  defaultTheme 'com.taskmanager.data.model.PresetThemes  	getTOArgb 'com.taskmanager.data.model.PresetThemes  	getToArgb 'com.taskmanager.data.model.PresetThemes  invoke 'com.taskmanager.data.model.PresetThemes  toArgb 'com.taskmanager.data.model.PresetThemes  Color (com.taskmanager.data.model.ThemeSettings  Int (com.taskmanager.data.model.ThemeSettings  List (com.taskmanager.data.model.ThemeSettings  
PrimaryKey (com.taskmanager.data.model.ThemeSettings  TaskCategory (com.taskmanager.data.model.ThemeSettings  TaskPriority (com.taskmanager.data.model.ThemeSettings  backgroundEndColor (com.taskmanager.data.model.ThemeSettings  backgroundEndColorCompose (com.taskmanager.data.model.ThemeSettings  backgroundStartColor (com.taskmanager.data.model.ThemeSettings  backgroundStartColorCompose (com.taskmanager.data.model.ThemeSettings  cardBackgroundColor (com.taskmanager.data.model.ThemeSettings  cardBackgroundDarkColor (com.taskmanager.data.model.ThemeSettings  	getLISTOf (com.taskmanager.data.model.ThemeSettings  	getListOf (com.taskmanager.data.model.ThemeSettings  healthCategoryColor (com.taskmanager.data.model.ThemeSettings  highPriorityColor (com.taskmanager.data.model.ThemeSettings  invoke (com.taskmanager.data.model.ThemeSettings  listOf (com.taskmanager.data.model.ThemeSettings  lowPriorityColor (com.taskmanager.data.model.ThemeSettings  mediumPriorityColor (com.taskmanager.data.model.ThemeSettings  onTaskTextColor (com.taskmanager.data.model.ThemeSettings  otherCategoryColor (com.taskmanager.data.model.ThemeSettings  personalCategoryColor (com.taskmanager.data.model.ThemeSettings  primaryColor (com.taskmanager.data.model.ThemeSettings  primaryTextColor (com.taskmanager.data.model.ThemeSettings  primaryVariantColor (com.taskmanager.data.model.ThemeSettings  secondaryTextColor (com.taskmanager.data.model.ThemeSettings  studyCategoryColor (com.taskmanager.data.model.ThemeSettings  workCategoryColor (com.taskmanager.data.model.ThemeSettings  	ColorItem com.taskmanager.ui.components  Float com.taskmanager.ui.components  String com.taskmanager.ui.components  	luminance com.taskmanager.ui.components  BackgroundColorSection com.taskmanager.ui.screens  CategoryColorSection com.taskmanager.ui.screens  ColorSettingItem com.taskmanager.ui.screens  PresetThemeItem com.taskmanager.ui.screens  PresetThemeSection com.taskmanager.ui.screens  PriorityColorSection com.taskmanager.ui.screens  String com.taskmanager.ui.screens  MutableStateFlow com.taskmanager.ui.theme  PresetThemes com.taskmanager.ui.theme  	StateFlow com.taskmanager.ui.theme  TaskDatabase com.taskmanager.ui.theme  asStateFlow com.taskmanager.ui.theme  Context $com.taskmanager.ui.theme.GlobalTheme  ThemeManager $com.taskmanager.ui.theme.GlobalTheme  Color %com.taskmanager.ui.theme.ThemeManager  Context %com.taskmanager.ui.theme.ThemeManager  MutableStateFlow %com.taskmanager.ui.theme.ThemeManager  PresetThemes %com.taskmanager.ui.theme.ThemeManager  	StateFlow %com.taskmanager.ui.theme.ThemeManager  TaskDatabase %com.taskmanager.ui.theme.ThemeManager  
ThemeSettings %com.taskmanager.ui.theme.ThemeManager  
_currentTheme %com.taskmanager.ui.theme.ThemeManager  asStateFlow %com.taskmanager.ui.theme.ThemeManager  com %com.taskmanager.ui.theme.ThemeManager  getASStateFlow %com.taskmanager.ui.theme.ThemeManager  getAsStateFlow %com.taskmanager.ui.theme.ThemeManager  Color 	java.lang  PresetThemes 	java.lang  TaskDatabase 	java.lang  
ThemeSettings 	java.lang  toArgb 	java.lang  Color kotlin  Float kotlin  PresetThemes kotlin  TaskDatabase kotlin  
ThemeSettings kotlin  toArgb kotlin  Color kotlin.annotation  PresetThemes kotlin.annotation  TaskDatabase kotlin.annotation  
ThemeSettings kotlin.annotation  toArgb kotlin.annotation  Color kotlin.collections  PresetThemes kotlin.collections  TaskDatabase kotlin.collections  
ThemeSettings kotlin.collections  toArgb kotlin.collections  Color kotlin.comparisons  PresetThemes kotlin.comparisons  TaskDatabase kotlin.comparisons  
ThemeSettings kotlin.comparisons  toArgb kotlin.comparisons  Color 	kotlin.io  PresetThemes 	kotlin.io  TaskDatabase 	kotlin.io  
ThemeSettings 	kotlin.io  toArgb 	kotlin.io  Color 
kotlin.jvm  PresetThemes 
kotlin.jvm  TaskDatabase 
kotlin.jvm  
ThemeSettings 
kotlin.jvm  toArgb 
kotlin.jvm  Color 
kotlin.ranges  PresetThemes 
kotlin.ranges  TaskDatabase 
kotlin.ranges  
ThemeSettings 
kotlin.ranges  toArgb 
kotlin.ranges  Color kotlin.sequences  PresetThemes kotlin.sequences  TaskDatabase kotlin.sequences  
ThemeSettings kotlin.sequences  toArgb kotlin.sequences  Color kotlin.text  PresetThemes kotlin.text  TaskDatabase kotlin.text  
ThemeSettings kotlin.text  toArgb kotlin.text  
Composable kotlinx.coroutines.flow  PresetThemes kotlinx.coroutines.flow  TaskDatabase kotlinx.coroutines.flow  com kotlinx.coroutines.flow  ColorDebugDialog com.taskmanager.ui.components  
ColorTestCard com.taskmanager.ui.components  safeColorFromInt (com.taskmanager.data.model.ThemeSettings                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     