package com.taskmanager.data.database;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.taskmanager.data.model.ThemeSettings;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@SuppressWarnings({"unchecked", "deprecation"})
public final class ThemeDao_Impl implements ThemeDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<ThemeSettings> __insertionAdapterOfThemeSettings;

  private final EntityDeletionOrUpdateAdapter<ThemeSettings> __updateAdapterOfThemeSettings;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllThemeSettings;

  public ThemeDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfThemeSettings = new EntityInsertionAdapter<ThemeSettings>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `theme_settings` (`id`,`backgroundStartColor`,`backgroundEndColor`,`workCategoryColor`,`personalCategoryColor`,`studyCategoryColor`,`healthCategoryColor`,`otherCategoryColor`,`highPriorityColor`,`mediumPriorityColor`,`lowPriorityColor`,`primaryTextColor`,`secondaryTextColor`,`onTaskTextColor`,`cardBackgroundColor`,`cardBackgroundDarkColor`,`primaryColor`,`primaryVariantColor`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final ThemeSettings entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getBackgroundStartColor());
        statement.bindLong(3, entity.getBackgroundEndColor());
        statement.bindLong(4, entity.getWorkCategoryColor());
        statement.bindLong(5, entity.getPersonalCategoryColor());
        statement.bindLong(6, entity.getStudyCategoryColor());
        statement.bindLong(7, entity.getHealthCategoryColor());
        statement.bindLong(8, entity.getOtherCategoryColor());
        statement.bindLong(9, entity.getHighPriorityColor());
        statement.bindLong(10, entity.getMediumPriorityColor());
        statement.bindLong(11, entity.getLowPriorityColor());
        statement.bindLong(12, entity.getPrimaryTextColor());
        statement.bindLong(13, entity.getSecondaryTextColor());
        statement.bindLong(14, entity.getOnTaskTextColor());
        statement.bindLong(15, entity.getCardBackgroundColor());
        statement.bindLong(16, entity.getCardBackgroundDarkColor());
        statement.bindLong(17, entity.getPrimaryColor());
        statement.bindLong(18, entity.getPrimaryVariantColor());
      }
    };
    this.__updateAdapterOfThemeSettings = new EntityDeletionOrUpdateAdapter<ThemeSettings>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `theme_settings` SET `id` = ?,`backgroundStartColor` = ?,`backgroundEndColor` = ?,`workCategoryColor` = ?,`personalCategoryColor` = ?,`studyCategoryColor` = ?,`healthCategoryColor` = ?,`otherCategoryColor` = ?,`highPriorityColor` = ?,`mediumPriorityColor` = ?,`lowPriorityColor` = ?,`primaryTextColor` = ?,`secondaryTextColor` = ?,`onTaskTextColor` = ?,`cardBackgroundColor` = ?,`cardBackgroundDarkColor` = ?,`primaryColor` = ?,`primaryVariantColor` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final ThemeSettings entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getBackgroundStartColor());
        statement.bindLong(3, entity.getBackgroundEndColor());
        statement.bindLong(4, entity.getWorkCategoryColor());
        statement.bindLong(5, entity.getPersonalCategoryColor());
        statement.bindLong(6, entity.getStudyCategoryColor());
        statement.bindLong(7, entity.getHealthCategoryColor());
        statement.bindLong(8, entity.getOtherCategoryColor());
        statement.bindLong(9, entity.getHighPriorityColor());
        statement.bindLong(10, entity.getMediumPriorityColor());
        statement.bindLong(11, entity.getLowPriorityColor());
        statement.bindLong(12, entity.getPrimaryTextColor());
        statement.bindLong(13, entity.getSecondaryTextColor());
        statement.bindLong(14, entity.getOnTaskTextColor());
        statement.bindLong(15, entity.getCardBackgroundColor());
        statement.bindLong(16, entity.getCardBackgroundDarkColor());
        statement.bindLong(17, entity.getPrimaryColor());
        statement.bindLong(18, entity.getPrimaryVariantColor());
        statement.bindLong(19, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteAllThemeSettings = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM theme_settings";
        return _query;
      }
    };
  }

  @Override
  public Object insertThemeSettings(final ThemeSettings themeSettings,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfThemeSettings.insert(themeSettings);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateThemeSettings(final ThemeSettings themeSettings,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfThemeSettings.handle(themeSettings);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAllThemeSettings(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllThemeSettings.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAllThemeSettings.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<ThemeSettings> getThemeSettings() {
    final String _sql = "SELECT * FROM theme_settings WHERE id = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"theme_settings"}, new Callable<ThemeSettings>() {
      @Override
      @Nullable
      public ThemeSettings call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfBackgroundStartColor = CursorUtil.getColumnIndexOrThrow(_cursor, "backgroundStartColor");
          final int _cursorIndexOfBackgroundEndColor = CursorUtil.getColumnIndexOrThrow(_cursor, "backgroundEndColor");
          final int _cursorIndexOfWorkCategoryColor = CursorUtil.getColumnIndexOrThrow(_cursor, "workCategoryColor");
          final int _cursorIndexOfPersonalCategoryColor = CursorUtil.getColumnIndexOrThrow(_cursor, "personalCategoryColor");
          final int _cursorIndexOfStudyCategoryColor = CursorUtil.getColumnIndexOrThrow(_cursor, "studyCategoryColor");
          final int _cursorIndexOfHealthCategoryColor = CursorUtil.getColumnIndexOrThrow(_cursor, "healthCategoryColor");
          final int _cursorIndexOfOtherCategoryColor = CursorUtil.getColumnIndexOrThrow(_cursor, "otherCategoryColor");
          final int _cursorIndexOfHighPriorityColor = CursorUtil.getColumnIndexOrThrow(_cursor, "highPriorityColor");
          final int _cursorIndexOfMediumPriorityColor = CursorUtil.getColumnIndexOrThrow(_cursor, "mediumPriorityColor");
          final int _cursorIndexOfLowPriorityColor = CursorUtil.getColumnIndexOrThrow(_cursor, "lowPriorityColor");
          final int _cursorIndexOfPrimaryTextColor = CursorUtil.getColumnIndexOrThrow(_cursor, "primaryTextColor");
          final int _cursorIndexOfSecondaryTextColor = CursorUtil.getColumnIndexOrThrow(_cursor, "secondaryTextColor");
          final int _cursorIndexOfOnTaskTextColor = CursorUtil.getColumnIndexOrThrow(_cursor, "onTaskTextColor");
          final int _cursorIndexOfCardBackgroundColor = CursorUtil.getColumnIndexOrThrow(_cursor, "cardBackgroundColor");
          final int _cursorIndexOfCardBackgroundDarkColor = CursorUtil.getColumnIndexOrThrow(_cursor, "cardBackgroundDarkColor");
          final int _cursorIndexOfPrimaryColor = CursorUtil.getColumnIndexOrThrow(_cursor, "primaryColor");
          final int _cursorIndexOfPrimaryVariantColor = CursorUtil.getColumnIndexOrThrow(_cursor, "primaryVariantColor");
          final ThemeSettings _result;
          if (_cursor.moveToFirst()) {
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final int _tmpBackgroundStartColor;
            _tmpBackgroundStartColor = _cursor.getInt(_cursorIndexOfBackgroundStartColor);
            final int _tmpBackgroundEndColor;
            _tmpBackgroundEndColor = _cursor.getInt(_cursorIndexOfBackgroundEndColor);
            final int _tmpWorkCategoryColor;
            _tmpWorkCategoryColor = _cursor.getInt(_cursorIndexOfWorkCategoryColor);
            final int _tmpPersonalCategoryColor;
            _tmpPersonalCategoryColor = _cursor.getInt(_cursorIndexOfPersonalCategoryColor);
            final int _tmpStudyCategoryColor;
            _tmpStudyCategoryColor = _cursor.getInt(_cursorIndexOfStudyCategoryColor);
            final int _tmpHealthCategoryColor;
            _tmpHealthCategoryColor = _cursor.getInt(_cursorIndexOfHealthCategoryColor);
            final int _tmpOtherCategoryColor;
            _tmpOtherCategoryColor = _cursor.getInt(_cursorIndexOfOtherCategoryColor);
            final int _tmpHighPriorityColor;
            _tmpHighPriorityColor = _cursor.getInt(_cursorIndexOfHighPriorityColor);
            final int _tmpMediumPriorityColor;
            _tmpMediumPriorityColor = _cursor.getInt(_cursorIndexOfMediumPriorityColor);
            final int _tmpLowPriorityColor;
            _tmpLowPriorityColor = _cursor.getInt(_cursorIndexOfLowPriorityColor);
            final int _tmpPrimaryTextColor;
            _tmpPrimaryTextColor = _cursor.getInt(_cursorIndexOfPrimaryTextColor);
            final int _tmpSecondaryTextColor;
            _tmpSecondaryTextColor = _cursor.getInt(_cursorIndexOfSecondaryTextColor);
            final int _tmpOnTaskTextColor;
            _tmpOnTaskTextColor = _cursor.getInt(_cursorIndexOfOnTaskTextColor);
            final int _tmpCardBackgroundColor;
            _tmpCardBackgroundColor = _cursor.getInt(_cursorIndexOfCardBackgroundColor);
            final int _tmpCardBackgroundDarkColor;
            _tmpCardBackgroundDarkColor = _cursor.getInt(_cursorIndexOfCardBackgroundDarkColor);
            final int _tmpPrimaryColor;
            _tmpPrimaryColor = _cursor.getInt(_cursorIndexOfPrimaryColor);
            final int _tmpPrimaryVariantColor;
            _tmpPrimaryVariantColor = _cursor.getInt(_cursorIndexOfPrimaryVariantColor);
            _result = new ThemeSettings(_tmpId,_tmpBackgroundStartColor,_tmpBackgroundEndColor,_tmpWorkCategoryColor,_tmpPersonalCategoryColor,_tmpStudyCategoryColor,_tmpHealthCategoryColor,_tmpOtherCategoryColor,_tmpHighPriorityColor,_tmpMediumPriorityColor,_tmpLowPriorityColor,_tmpPrimaryTextColor,_tmpSecondaryTextColor,_tmpOnTaskTextColor,_tmpCardBackgroundColor,_tmpCardBackgroundDarkColor,_tmpPrimaryColor,_tmpPrimaryVariantColor);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getThemeSettingsSync(final Continuation<? super ThemeSettings> $completion) {
    final String _sql = "SELECT * FROM theme_settings WHERE id = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<ThemeSettings>() {
      @Override
      @Nullable
      public ThemeSettings call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfBackgroundStartColor = CursorUtil.getColumnIndexOrThrow(_cursor, "backgroundStartColor");
          final int _cursorIndexOfBackgroundEndColor = CursorUtil.getColumnIndexOrThrow(_cursor, "backgroundEndColor");
          final int _cursorIndexOfWorkCategoryColor = CursorUtil.getColumnIndexOrThrow(_cursor, "workCategoryColor");
          final int _cursorIndexOfPersonalCategoryColor = CursorUtil.getColumnIndexOrThrow(_cursor, "personalCategoryColor");
          final int _cursorIndexOfStudyCategoryColor = CursorUtil.getColumnIndexOrThrow(_cursor, "studyCategoryColor");
          final int _cursorIndexOfHealthCategoryColor = CursorUtil.getColumnIndexOrThrow(_cursor, "healthCategoryColor");
          final int _cursorIndexOfOtherCategoryColor = CursorUtil.getColumnIndexOrThrow(_cursor, "otherCategoryColor");
          final int _cursorIndexOfHighPriorityColor = CursorUtil.getColumnIndexOrThrow(_cursor, "highPriorityColor");
          final int _cursorIndexOfMediumPriorityColor = CursorUtil.getColumnIndexOrThrow(_cursor, "mediumPriorityColor");
          final int _cursorIndexOfLowPriorityColor = CursorUtil.getColumnIndexOrThrow(_cursor, "lowPriorityColor");
          final int _cursorIndexOfPrimaryTextColor = CursorUtil.getColumnIndexOrThrow(_cursor, "primaryTextColor");
          final int _cursorIndexOfSecondaryTextColor = CursorUtil.getColumnIndexOrThrow(_cursor, "secondaryTextColor");
          final int _cursorIndexOfOnTaskTextColor = CursorUtil.getColumnIndexOrThrow(_cursor, "onTaskTextColor");
          final int _cursorIndexOfCardBackgroundColor = CursorUtil.getColumnIndexOrThrow(_cursor, "cardBackgroundColor");
          final int _cursorIndexOfCardBackgroundDarkColor = CursorUtil.getColumnIndexOrThrow(_cursor, "cardBackgroundDarkColor");
          final int _cursorIndexOfPrimaryColor = CursorUtil.getColumnIndexOrThrow(_cursor, "primaryColor");
          final int _cursorIndexOfPrimaryVariantColor = CursorUtil.getColumnIndexOrThrow(_cursor, "primaryVariantColor");
          final ThemeSettings _result;
          if (_cursor.moveToFirst()) {
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final int _tmpBackgroundStartColor;
            _tmpBackgroundStartColor = _cursor.getInt(_cursorIndexOfBackgroundStartColor);
            final int _tmpBackgroundEndColor;
            _tmpBackgroundEndColor = _cursor.getInt(_cursorIndexOfBackgroundEndColor);
            final int _tmpWorkCategoryColor;
            _tmpWorkCategoryColor = _cursor.getInt(_cursorIndexOfWorkCategoryColor);
            final int _tmpPersonalCategoryColor;
            _tmpPersonalCategoryColor = _cursor.getInt(_cursorIndexOfPersonalCategoryColor);
            final int _tmpStudyCategoryColor;
            _tmpStudyCategoryColor = _cursor.getInt(_cursorIndexOfStudyCategoryColor);
            final int _tmpHealthCategoryColor;
            _tmpHealthCategoryColor = _cursor.getInt(_cursorIndexOfHealthCategoryColor);
            final int _tmpOtherCategoryColor;
            _tmpOtherCategoryColor = _cursor.getInt(_cursorIndexOfOtherCategoryColor);
            final int _tmpHighPriorityColor;
            _tmpHighPriorityColor = _cursor.getInt(_cursorIndexOfHighPriorityColor);
            final int _tmpMediumPriorityColor;
            _tmpMediumPriorityColor = _cursor.getInt(_cursorIndexOfMediumPriorityColor);
            final int _tmpLowPriorityColor;
            _tmpLowPriorityColor = _cursor.getInt(_cursorIndexOfLowPriorityColor);
            final int _tmpPrimaryTextColor;
            _tmpPrimaryTextColor = _cursor.getInt(_cursorIndexOfPrimaryTextColor);
            final int _tmpSecondaryTextColor;
            _tmpSecondaryTextColor = _cursor.getInt(_cursorIndexOfSecondaryTextColor);
            final int _tmpOnTaskTextColor;
            _tmpOnTaskTextColor = _cursor.getInt(_cursorIndexOfOnTaskTextColor);
            final int _tmpCardBackgroundColor;
            _tmpCardBackgroundColor = _cursor.getInt(_cursorIndexOfCardBackgroundColor);
            final int _tmpCardBackgroundDarkColor;
            _tmpCardBackgroundDarkColor = _cursor.getInt(_cursorIndexOfCardBackgroundDarkColor);
            final int _tmpPrimaryColor;
            _tmpPrimaryColor = _cursor.getInt(_cursorIndexOfPrimaryColor);
            final int _tmpPrimaryVariantColor;
            _tmpPrimaryVariantColor = _cursor.getInt(_cursorIndexOfPrimaryVariantColor);
            _result = new ThemeSettings(_tmpId,_tmpBackgroundStartColor,_tmpBackgroundEndColor,_tmpWorkCategoryColor,_tmpPersonalCategoryColor,_tmpStudyCategoryColor,_tmpHealthCategoryColor,_tmpOtherCategoryColor,_tmpHighPriorityColor,_tmpMediumPriorityColor,_tmpLowPriorityColor,_tmpPrimaryTextColor,_tmpSecondaryTextColor,_tmpOnTaskTextColor,_tmpCardBackgroundColor,_tmpCardBackgroundDarkColor,_tmpPrimaryColor,_tmpPrimaryVariantColor);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
