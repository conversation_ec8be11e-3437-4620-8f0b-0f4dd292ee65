{"logs": [{"outputFile": "com.taskmanager.app-mergeDebugResources-42:/values-zh-rCN/values-zh-rCN.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2cf887c674b6fe54b07f45a262ba7369\\transformed\\material3-1.1.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,155,254,354,452,524,607,707,811,912,1023,1101,1193,1273,1358,1460,1570,1667,1771,1873,1977,2089,2192,2289,2390,2493,2574,2665,2766,2871,2957,3051,3145,3248,3357,3453,3539,3608,3679,3758,3836,3924,4000,4077,4171,4261,4350,4441,4520,4612,4704,4796,4900,4976,5064", "endColumns": "99,98,99,97,71,82,99,103,100,110,77,91,79,84,101,109,96,103,101,103,111,102,96,100,102,80,90,100,104,85,93,93,102,108,95,85,68,70,78,77,87,75,76,93,89,88,90,78,91,91,91,103,75,87,85", "endOffsets": "150,249,349,447,519,602,702,806,907,1018,1096,1188,1268,1353,1455,1565,1662,1766,1868,1972,2084,2187,2284,2385,2488,2569,2660,2761,2866,2952,3046,3140,3243,3352,3448,3534,3603,3674,3753,3831,3919,3995,4072,4166,4256,4345,4436,4515,4607,4699,4791,4895,4971,5059,5145"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,304,404,1319,1391,1474,1574,1678,1779,1890,1968,2060,2140,2225,2327,2437,2534,2638,2740,2844,2956,3059,3156,3257,3360,3441,3532,3633,3738,3824,3918,4012,4115,4224,4320,4582,4728,5381,5528,5707,6050,6126,6203,6297,6387,6476,6567,6646,6738,6830,6922,7026,7102,7190", "endColumns": "99,98,99,97,71,82,99,103,100,110,77,91,79,84,101,109,96,103,101,103,111,102,96,100,102,80,90,100,104,85,93,93,102,108,95,85,68,70,78,77,87,75,76,93,89,88,90,78,91,91,91,103,75,87,85", "endOffsets": "200,299,399,497,1386,1469,1569,1673,1774,1885,1963,2055,2135,2220,2322,2432,2529,2633,2735,2839,2951,3054,3151,3252,3355,3436,3527,3628,3733,3819,3913,4007,4110,4219,4315,4401,4646,4794,5455,5601,5790,6121,6198,6292,6382,6471,6562,6641,6733,6825,6917,7021,7097,7185,7271"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e84e839e6aad51a26b803212332dad05\\transformed\\core-1.10.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,248,342,436,529,623,719", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "142,243,337,431,524,618,714,815"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "502,594,695,789,883,976,1070,5606", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "589,690,784,878,971,1065,1161,5702"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1c27dd2d2d266e151b930b964e47cb67\\transformed\\ui-release\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,182,258,343,434,511,585,662,740,815,880,945,1018,1093,1161,1234,1300", "endColumns": "76,75,84,90,76,73,76,77,74,64,64,72,74,67,72,65,115", "endOffsets": "177,253,338,429,506,580,657,735,810,875,940,1013,1088,1156,1229,1295,1411"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1166,1243,4406,4491,4651,4799,4873,4950,5028,5103,5168,5233,5306,5460,5795,5868,5934", "endColumns": "76,75,84,90,76,73,76,77,74,64,64,72,74,67,72,65,115", "endOffsets": "1238,1314,4486,4577,4723,4868,4945,5023,5098,5163,5228,5301,5376,5523,5863,5929,6045"}}]}]}