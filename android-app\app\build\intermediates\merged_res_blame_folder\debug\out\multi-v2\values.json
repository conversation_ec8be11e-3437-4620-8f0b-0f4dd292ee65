{"logs": [{"outputFile": "com.taskmanager.app-mergeDebugResources-42:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\15a194b0199a52ec8c44368545848737\\transformed\\lifecycle-viewmodel-2.6.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "100", "startColumns": "4", "startOffsets": "6022", "endColumns": "49", "endOffsets": "6067"}}, {"source": "C:\\Users\\<USER>\\Desktop\\schedule\\task-manager\\android-app\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "2,6,7,8,3", "startColumns": "4,4,4,4,4", "startOffsets": "55,164,221,280,97", "endColumns": "41,56,58,57,41", "endOffsets": "92,216,275,333,134"}, "to": {"startLines": "5,8,9,10,13", "startColumns": "4,4,4,4,4", "startOffsets": "370,543,600,659,849", "endColumns": "41,56,58,57,41", "endOffsets": "407,595,654,712,886"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a694a578cd6c11e83bcdf0271469a629\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "105", "startColumns": "4", "startOffsets": "6290", "endColumns": "82", "endOffsets": "6368"}}, {"source": "C:\\Users\\<USER>\\Desktop\\schedule\\task-manager\\android-app\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "139", "endLines": "8", "endColumns": "12", "endOffsets": "523"}, "to": {"startLines": "259", "startColumns": "4", "startOffsets": "15706", "endLines": "264", "endColumns": "12", "endOffsets": "16055"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\20318a4763c515b8e8114db0afb41606\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,143,229,310,394,463,528,611,717,803,923,977,1046,1107,1176,1265,1360,1434,1531,1624,1722,1871,1962,2050,2146,2244,2308,2376,2463,2557,2624,2696,2768,2869,2978,3054,3123,3171,3237,3301,3358,3415,3487,3537,3591,3662,3733,3803,3872,3930,4006,4077,4151,4237,4287,4357", "endLines": "2,3,4,5,6,7,8,9,10,13,14,15,16,17,18,19,20,21,22,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "138,224,305,389,458,523,606,712,798,918,972,1041,1102,1171,1260,1355,1429,1526,1619,1717,1866,1957,2045,2141,2239,2303,2371,2458,2552,2619,2691,2763,2864,2973,3049,3118,3166,3232,3296,3353,3410,3482,3532,3586,3657,3728,3798,3867,3925,4001,4072,4146,4232,4282,4352,4417"}, "to": {"startLines": "143,144,145,146,147,148,149,150,151,152,155,156,157,158,159,160,161,162,163,164,165,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8484,8572,8658,8739,8823,8892,8957,9040,9146,9232,9352,9406,9475,9536,9605,9694,9789,9863,9960,10053,10151,10300,10391,10479,10575,10673,10737,10805,10892,10986,11053,11125,11197,11298,11407,11483,11552,11600,11666,11730,11787,11844,11916,11966,12020,12091,12162,12232,12301,12359,12435,12506,12580,12666,12716,12786", "endLines": "143,144,145,146,147,148,149,150,151,154,155,156,157,158,159,160,161,162,163,164,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "8567,8653,8734,8818,8887,8952,9035,9141,9227,9347,9401,9470,9531,9600,9689,9784,9858,9955,10048,10146,10295,10386,10474,10570,10668,10732,10800,10887,10981,11048,11120,11192,11293,11402,11478,11547,11595,11661,11725,11782,11839,11911,11961,12015,12086,12157,12227,12296,12354,12430,12501,12575,12661,12711,12781,12846"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5458ced068947865f8625aea9d480f0b\\transformed\\lifecycle-runtime-2.6.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "97", "startColumns": "4", "startOffsets": "5865", "endColumns": "42", "endOffsets": "5903"}}, {"source": "C:\\Users\\<USER>\\Desktop\\schedule\\task-manager\\android-app\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "9,49,2,44,43,14,34,35,32,33,31,62,61,12,5,11,10,57,66,56,68,58,55,67,65,40,42,48,47,39,41,26,28,27,13,23,19,51,18,21,22,50,20,17,52,38,6", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "226,1686,55,1508,1461,443,1134,1181,1039,1088,994,2193,2133,356,120,311,268,2005,2337,1952,2445,2057,1901,2391,2280,1332,1419,1631,1580,1287,1373,840,930,884,400,773,598,1784,548,680,727,1739,639,504,1831,1250,161", "endColumns": "41,52,43,50,46,37,46,45,48,45,44,63,59,43,40,44,42,51,53,52,60,51,50,53,56,40,41,54,50,44,45,43,42,45,42,44,40,46,49,46,45,44,40,43,46,36,41", "endOffsets": "263,1734,94,1554,1503,476,1176,1222,1083,1129,1034,2252,2188,395,156,351,306,2052,2386,2000,2501,2104,1947,2440,2332,1368,1456,1681,1626,1327,1414,879,968,925,438,813,634,1826,593,722,768,1779,675,543,1873,1282,198"}, "to": {"startLines": "103,104,106,107,108,116,117,118,119,120,121,124,125,126,127,130,132,133,134,135,136,137,138,139,140,204,205,206,207,211,212,213,214,215,218,223,224,225,226,227,228,229,230,231,232,234,237", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6195,6237,6373,6417,6468,7026,7064,7111,7157,7206,7252,7416,7480,7540,7584,7792,7895,7938,7990,8044,8097,8158,8210,8261,8315,12913,12954,12996,13051,13226,13271,13317,13361,13404,13554,13797,13842,13883,13930,13980,14027,14073,14118,14159,14203,14340,14488", "endColumns": "41,52,43,50,46,37,46,45,48,45,44,63,59,43,40,44,42,51,53,52,60,51,50,53,56,40,41,54,50,44,45,43,42,45,42,44,40,46,49,46,45,44,40,43,46,36,41", "endOffsets": "6232,6285,6412,6463,6510,7059,7106,7152,7201,7247,7292,7475,7535,7579,7620,7832,7933,7985,8039,8092,8153,8205,8256,8310,8367,12949,12991,13046,13097,13266,13312,13356,13399,13445,13592,13837,13878,13925,13975,14022,14068,14113,14154,14198,14245,14372,14525"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\eb41759101879028f49add9e86811a8d\\transformed\\core-1.12.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "2,3,4,6,7,11,12,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,77,78,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,102,109,110,111,112,113,114,115,220,248,249,253,254,258,265,266,267,273,283,316,337,370", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,210,282,412,477,717,786,891,961,1029,1101,1171,1232,1306,1379,1440,1501,1563,1627,1689,1750,1818,1918,1978,2044,2117,2186,2243,2295,2357,2429,2505,4827,4862,5006,5061,5124,5179,5237,5295,5356,5419,5476,5527,5577,5638,5695,5761,5795,5830,6125,6515,6582,6654,6723,6792,6866,6938,13643,14948,15065,15266,15376,15577,16060,16132,16199,16402,16703,18434,19115,19797", "endLines": "2,3,4,6,7,11,12,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,77,78,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,102,109,110,111,112,113,114,115,220,248,252,253,257,258,265,266,272,282,315,336,369,375", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "205,277,365,472,538,781,844,956,1024,1096,1166,1227,1301,1374,1435,1496,1558,1622,1684,1745,1813,1913,1973,2039,2112,2181,2238,2290,2352,2424,2500,2565,4857,4892,5056,5119,5174,5232,5290,5351,5414,5471,5522,5572,5633,5690,5756,5790,5825,5860,6190,6577,6649,6718,6787,6861,6933,7021,13709,15060,15261,15371,15572,15701,16127,16194,16397,16698,18429,19110,19792,19959"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ef66bf7ba03ac8625e6cd79d75b32da9\\transformed\\activity-1.7.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "80,98", "startColumns": "4,4", "startOffsets": "4964,5908", "endColumns": "41,59", "endOffsets": "5001,5963"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\24cc38749a18090be21f81d4377ab5a8\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "99", "startColumns": "4", "startOffsets": "5968", "endColumns": "53", "endOffsets": "6017"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7a56af73b15e252c2ac3b5bc69694ed2\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "235,236", "startColumns": "4,4", "startOffsets": "14377,14433", "endColumns": "55,54", "endOffsets": "14428,14483"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5ed700988212d8d9085a7fcc8af113be\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3014,3048,3098,3152,3198,3245,3281,3371,3483,3594", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,59,62,66", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3009,3043,3093,3147,3193,3240,3276,3366,3478,3589,3784"}, "to": {"startLines": "39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,101,122,123,128,129,131,141,142,203,208,209,210,216,217,219,221,222,233,238,241,244", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2570,2629,2688,2748,2808,2868,2928,2988,3048,3108,3168,3228,3288,3347,3407,3467,3527,3587,3647,3707,3767,3827,3887,3947,4006,4066,4126,4185,4244,4303,4362,4421,4480,4554,4612,4667,4718,6072,7297,7362,7625,7691,7837,8372,8424,12851,13102,13156,13192,13450,13500,13597,13714,13761,14250,14530,14642,14753", "endLines": "39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,101,122,123,128,129,131,141,142,203,208,209,210,216,217,219,221,222,233,240,243,247", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "2624,2683,2743,2803,2863,2923,2983,3043,3103,3163,3223,3283,3342,3402,3462,3522,3582,3642,3702,3762,3822,3882,3942,4001,4061,4121,4180,4239,4298,4357,4416,4475,4549,4607,4662,4713,4768,6120,7357,7411,7686,7787,7890,8419,8479,12908,13151,13187,13221,13495,13549,13638,13756,13792,14335,14637,14748,14943"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\58d78ea86540194b0aac1267880374b6\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "76,79", "startColumns": "4,4", "startOffsets": "4773,4897", "endColumns": "53,66", "endOffsets": "4822,4959"}}]}]}