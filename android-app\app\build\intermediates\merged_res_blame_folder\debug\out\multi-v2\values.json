{"logs": [{"outputFile": "com.taskmanager.app-mergeDebugResources-42:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d8e46d1e1b8b3f4c8c6a75b79667be2b\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "76,79", "startColumns": "4,4", "startOffsets": "4773,4897", "endColumns": "53,66", "endOffsets": "4822,4959"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\11e3015613825d96d065c95dda0d086a\\transformed\\lifecycle-runtime-2.6.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "97", "startColumns": "4", "startOffsets": "5865", "endColumns": "42", "endOffsets": "5903"}}, {"source": "C:\\Users\\<USER>\\Desktop\\schedule\\task-manager\\android-app\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "9,49,2,44,43,14,34,35,32,33,31,62,61,12,5,11,10,57,66,56,68,58,55,67,65,40,42,48,47,39,41,26,28,27,13,23,19,51,18,21,22,50,20,17,52,38,6", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "226,1686,55,1508,1461,443,1134,1181,1039,1088,994,2193,2133,356,120,311,268,2005,2337,1952,2445,2057,1901,2391,2280,1332,1419,1631,1580,1287,1373,840,930,884,400,773,598,1784,548,680,727,1739,639,504,1831,1250,161", "endColumns": "41,52,43,50,46,37,46,45,48,45,44,63,59,43,40,44,42,51,53,52,60,51,50,53,56,40,41,54,50,44,45,43,42,45,42,44,40,46,49,46,45,44,40,43,46,36,41", "endOffsets": "263,1734,94,1554,1503,476,1176,1222,1083,1129,1034,2252,2188,395,156,351,306,2052,2386,2000,2501,2104,1947,2440,2332,1368,1456,1681,1626,1327,1414,879,968,925,438,813,634,1826,593,722,768,1779,675,543,1873,1282,198"}, "to": {"startLines": "103,104,106,107,108,120,121,122,123,124,125,129,130,131,167,170,173,174,175,176,177,178,179,180,181,187,188,189,190,194,195,196,197,198,201,209,210,211,212,213,214,215,216,217,218,232,235", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6195,6237,6373,6417,6468,7349,7387,7434,7480,7529,7575,7787,7851,7911,10491,10699,10846,10889,10941,10995,11048,11109,11161,11212,11266,11612,11653,11695,11750,11925,11970,12016,12060,12103,12253,12670,12715,12756,12803,12853,12900,12946,12991,13032,13076,13965,14129", "endColumns": "41,52,43,50,46,37,46,45,48,45,44,63,59,43,40,44,42,51,53,52,60,51,50,53,56,40,41,54,50,44,45,43,42,45,42,44,40,46,49,46,45,44,40,43,46,36,41", "endOffsets": "6232,6285,6412,6463,6510,7382,7429,7475,7524,7570,7615,7846,7906,7950,10527,10739,10884,10936,10990,11043,11104,11156,11207,11261,11318,11648,11690,11745,11796,11965,12011,12055,12098,12144,12291,12710,12751,12798,12848,12895,12941,12986,13027,13071,13118,13997,14166"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e84e839e6aad51a26b803212332dad05\\transformed\\core-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "2,3,4,6,7,11,12,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,77,78,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,102,113,114,115,116,117,118,119,205,246,247,251,252,256,263,264,265,271,281,314,335,368", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,210,282,412,477,717,786,891,961,1029,1101,1171,1232,1306,1379,1440,1501,1563,1627,1689,1750,1818,1918,1978,2044,2117,2186,2243,2295,2357,2429,2505,4827,4862,5006,5061,5124,5179,5237,5295,5356,5419,5476,5527,5577,5638,5695,5761,5795,5830,6125,6838,6905,6977,7046,7115,7189,7261,12448,14589,14706,14907,15017,15218,15701,15773,15840,16043,16344,18075,18756,19438", "endLines": "2,3,4,6,7,11,12,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,77,78,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,102,113,114,115,116,117,118,119,205,246,250,251,255,256,263,264,270,280,313,334,367,373", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "205,277,365,472,538,781,844,956,1024,1096,1166,1227,1301,1374,1435,1496,1558,1622,1684,1745,1813,1913,1973,2039,2112,2181,2238,2290,2352,2424,2500,2565,4857,4892,5056,5119,5174,5232,5290,5351,5414,5471,5522,5572,5633,5690,5756,5790,5825,5860,6190,6900,6972,7041,7110,7184,7256,7344,12514,14701,14902,15012,15213,15342,15768,15835,16038,16339,18070,18751,19433,19600"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\142eb087cc88d530f10cb0e8eabfbea5\\transformed\\lifecycle-viewmodel-2.6.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "100", "startColumns": "4", "startOffsets": "6022", "endColumns": "49", "endOffsets": "6067"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\9a1d63a29c4f62158660f7187af857ff\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "99", "startColumns": "4", "startOffsets": "5968", "endColumns": "53", "endOffsets": "6017"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d06be7f65ce94d00ab916dfd7b7892a3\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "105", "startColumns": "4", "startOffsets": "6290", "endColumns": "82", "endOffsets": "6368"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1c27dd2d2d266e151b930b964e47cb67\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3014,3048,3098,3152,3198,3245,3281,3371,3483,3594", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,59,62,66", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3009,3043,3093,3147,3193,3240,3276,3366,3478,3589,3784"}, "to": {"startLines": "39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,101,126,127,168,169,172,183,184,186,191,192,193,199,200,203,207,208,219,236,239,242", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2570,2629,2688,2748,2808,2868,2928,2988,3048,3108,3168,3228,3288,3347,3407,3467,3527,3587,3647,3707,3767,3827,3887,3947,4006,4066,4126,4185,4244,4303,4362,4421,4480,4554,4612,4667,4718,6072,7620,7685,10532,10598,10788,11369,11421,11550,11801,11855,11891,12149,12199,12349,12587,12634,13123,14171,14283,14394", "endLines": "39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,101,126,127,168,169,172,183,184,186,191,192,193,199,200,203,207,208,219,238,241,245", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "2624,2683,2743,2803,2863,2923,2983,3043,3103,3163,3223,3283,3342,3402,3462,3522,3582,3642,3702,3762,3822,3882,3942,4001,4061,4121,4180,4239,4298,4357,4416,4475,4549,4607,4662,4713,4768,6120,7680,7734,10593,10694,10841,11416,11476,11607,11850,11886,11920,12194,12248,12390,12629,12665,13208,14278,14389,14584"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2cf887c674b6fe54b07f45a262ba7369\\transformed\\material3-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,139,221,298,378,426,487,566,668,750,866,916,981,1038,1103,1188,1279,1349,1442,1531,1625,1770,1857,1941,2033,2127,2187,2251,2334,2424,2487,2555,2623,2720,2825,2897,2962,3006,3052,3121,3174,3227,3295,3341,3391,3458,3525,3591,3656,3710,3782,3849,3919,4001,4047,4113", "endLines": "2,3,4,5,6,7,8,9,10,13,14,15,16,17,18,19,20,21,22,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "83,81,76,79,47,60,78,101,81,13,49,64,56,64,84,90,69,92,88,93,13,86,83,91,93,59,63,82,89,62,67,67,96,104,71,64,43,45,68,52,52,67,45,49,66,66,65,64,53,71,66,69,81,45,65,60", "endOffsets": "134,216,293,373,421,482,561,663,745,861,911,976,1033,1098,1183,1274,1344,1437,1526,1620,1765,1852,1936,2028,2122,2182,2246,2329,2419,2482,2550,2618,2715,2820,2892,2957,3001,3047,3116,3169,3222,3290,3336,3386,3453,3520,3586,3651,3705,3777,3844,3914,3996,4042,4108,4169"}, "to": {"startLines": "109,110,111,112,128,132,133,134,135,136,139,140,141,142,143,144,145,146,147,148,149,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,171,182,185,202,204,206,220,221,222,223,224,225,226,227,228,229,230,231,233,234", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6515,6599,6681,6758,7739,7955,8016,8095,8197,8279,8395,8445,8510,8567,8632,8717,8808,8878,8971,9060,9154,9299,9386,9470,9562,9656,9716,9780,9863,9953,10016,10084,10152,10249,10354,10426,10744,11323,11481,12296,12395,12519,13213,13259,13309,13376,13443,13509,13574,13628,13700,13767,13837,13919,14002,14068", "endLines": "109,110,111,112,128,132,133,134,135,138,139,140,141,142,143,144,145,146,147,148,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,171,182,185,202,204,206,220,221,222,223,224,225,226,227,228,229,230,231,233,234", "endColumns": "83,81,76,79,47,60,78,101,81,13,49,64,56,64,84,90,69,92,88,93,13,86,83,91,93,59,63,82,89,62,67,67,96,104,71,64,43,45,68,52,52,67,45,49,66,66,65,64,53,71,66,69,81,45,65,60", "endOffsets": "6594,6676,6753,6833,7782,8011,8090,8192,8274,8390,8440,8505,8562,8627,8712,8803,8873,8966,9055,9149,9294,9381,9465,9557,9651,9711,9775,9858,9948,10011,10079,10147,10244,10349,10421,10486,10783,11364,11545,12344,12443,12582,13254,13304,13371,13438,13504,13569,13623,13695,13762,13832,13914,13960,14063,14124"}}, {"source": "C:\\Users\\<USER>\\Desktop\\schedule\\task-manager\\android-app\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "2,6,7,8,3", "startColumns": "4,4,4,4,4", "startOffsets": "55,164,221,280,97", "endColumns": "41,56,58,57,41", "endOffsets": "92,216,275,333,134"}, "to": {"startLines": "5,8,9,10,13", "startColumns": "4,4,4,4,4", "startOffsets": "370,543,600,659,849", "endColumns": "41,56,58,57,41", "endOffsets": "407,595,654,712,886"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4d9ca52df9690b418cb87ebd2737bab4\\transformed\\activity-1.7.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "80,98", "startColumns": "4,4", "startOffsets": "4964,5908", "endColumns": "41,59", "endOffsets": "5001,5963"}}, {"source": "C:\\Users\\<USER>\\Desktop\\schedule\\task-manager\\android-app\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "139", "endLines": "8", "endColumns": "12", "endOffsets": "523"}, "to": {"startLines": "257", "startColumns": "4", "startOffsets": "15347", "endLines": "262", "endColumns": "12", "endOffsets": "15696"}}]}]}