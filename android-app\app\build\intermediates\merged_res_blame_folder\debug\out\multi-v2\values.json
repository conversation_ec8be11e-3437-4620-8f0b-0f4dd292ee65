{"logs": [{"outputFile": "com.taskmanager.app-mergeDebugResources-53:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8dc7ab0e5e3d165b04df2d50d1b4bfac\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "98", "startColumns": "4", "startOffsets": "5918", "endColumns": "42", "endOffsets": "5956"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\18cfd43c9a843935a6adda402716495c\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "101", "startColumns": "4", "startOffsets": "6075", "endColumns": "49", "endOffsets": "6120"}}, {"source": "C:\\Users\\<USER>\\Desktop\\schedule\\task-manager\\android-app\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "2,6,7,8,3", "startColumns": "4,4,4,4,4", "startOffsets": "55,164,221,280,97", "endColumns": "41,56,58,57,41", "endOffsets": "92,216,275,333,134"}, "to": {"startLines": "5,8,9,10,13", "startColumns": "4,4,4,4,4", "startOffsets": "370,543,600,659,849", "endColumns": "41,56,58,57,41", "endOffsets": "407,595,654,712,886"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\78c98d357e56231e9d5a0320c13ef217\\transformed\\navigation-runtime-2.7.6\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "79,268,412,415", "startColumns": "4,4,4,4", "startOffsets": "4897,16248,21580,21695", "endLines": "79,274,414,417", "endColumns": "52,24,24,24", "endOffsets": "4945,16547,21690,21805"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a694a578cd6c11e83bcdf0271469a629\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "105", "startColumns": "4", "startOffsets": "6290", "endColumns": "82", "endOffsets": "6368"}}, {"source": "C:\\Users\\<USER>\\Desktop\\schedule\\task-manager\\android-app\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "139", "endLines": "8", "endColumns": "12", "endOffsets": "523"}, "to": {"startLines": "260", "startColumns": "4", "startOffsets": "15755", "endLines": "265", "endColumns": "12", "endOffsets": "16104"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\20318a4763c515b8e8114db0afb41606\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,143,229,310,394,463,528,611,717,803,923,977,1046,1107,1176,1265,1360,1434,1531,1624,1722,1871,1962,2050,2146,2244,2308,2376,2463,2557,2624,2696,2768,2869,2978,3054,3123,3171,3237,3301,3358,3415,3487,3537,3591,3662,3733,3803,3872,3930,4006,4077,4151,4237,4287,4357", "endLines": "2,3,4,5,6,7,8,9,10,13,14,15,16,17,18,19,20,21,22,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "138,224,305,389,458,523,606,712,798,918,972,1041,1102,1171,1260,1355,1429,1526,1619,1717,1866,1957,2045,2141,2239,2303,2371,2458,2552,2619,2691,2763,2864,2973,3049,3118,3166,3232,3296,3353,3410,3482,3532,3586,3657,3728,3798,3867,3925,4001,4072,4146,4232,4282,4352,4417"}, "to": {"startLines": "144,145,146,147,148,149,150,151,152,153,156,157,158,159,160,161,162,163,164,165,166,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8541,8629,8715,8796,8880,8949,9014,9097,9203,9289,9409,9463,9532,9593,9662,9751,9846,9920,10017,10110,10208,10357,10448,10536,10632,10730,10794,10862,10949,11043,11110,11182,11254,11355,11464,11540,11609,11657,11723,11787,11844,11901,11973,12023,12077,12148,12219,12289,12358,12416,12492,12563,12637,12723,12773,12843", "endLines": "144,145,146,147,148,149,150,151,152,155,156,157,158,159,160,161,162,163,164,165,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "8624,8710,8791,8875,8944,9009,9092,9198,9284,9404,9458,9527,9588,9657,9746,9841,9915,10012,10105,10203,10352,10443,10531,10627,10725,10789,10857,10944,11038,11105,11177,11249,11350,11459,11535,11604,11652,11718,11782,11839,11896,11968,12018,12072,12143,12214,12284,12353,12411,12487,12558,12632,12718,12768,12838,12903"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\95e99f18e5cbc62741261c807c9b2283\\transformed\\activity-1.8.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "81,99", "startColumns": "4,4", "startOffsets": "5017,5961", "endColumns": "41,59", "endOffsets": "5054,6016"}}, {"source": "C:\\Users\\<USER>\\Desktop\\schedule\\task-manager\\android-app\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "9,2,44,43,14,34,35,32,33,31,49,62,61,12,5,11,10,57,66,56,68,58,55,67,65,40,42,48,47,39,41,26,28,27,13,23,19,51,18,21,22,50,20,17,52,38,6", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "226,55,1500,1453,443,1134,1181,1039,1088,994,1678,2189,2129,356,120,311,268,2001,2333,1948,2441,2053,1897,2387,2276,1328,1411,1623,1572,1287,1369,840,930,884,400,773,598,1780,548,680,727,1735,639,504,1827,1250,161", "endColumns": "41,43,50,46,37,46,45,48,45,44,56,63,59,43,40,44,42,51,53,52,60,51,50,53,56,40,41,54,50,40,41,43,42,45,42,44,40,46,49,46,45,44,40,43,46,36,41", "endOffsets": "263,94,1546,1495,476,1176,1222,1083,1129,1034,1730,2248,2184,395,156,351,306,2048,2382,1996,2497,2100,1943,2436,2328,1364,1448,1673,1618,1323,1406,879,968,925,438,813,634,1822,593,722,768,1775,675,543,1869,1282,198"}, "to": {"startLines": "104,106,107,108,116,117,118,119,120,121,122,125,126,127,128,131,133,134,135,136,137,138,139,140,141,205,206,207,208,212,213,214,215,216,219,224,225,226,227,228,229,230,231,232,233,235,238", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6248,6373,6417,6468,7026,7064,7111,7157,7206,7252,7297,7473,7537,7597,7641,7849,7952,7995,8047,8101,8154,8215,8267,8318,8372,12970,13011,13053,13108,13283,13324,13366,13410,13453,13603,13846,13891,13932,13979,14029,14076,14122,14167,14208,14252,14389,14537", "endColumns": "41,43,50,46,37,46,45,48,45,44,56,63,59,43,40,44,42,51,53,52,60,51,50,53,56,40,41,54,50,40,41,43,42,45,42,44,40,46,49,46,45,44,40,43,46,36,41", "endOffsets": "6285,6412,6463,6510,7059,7106,7152,7201,7247,7292,7349,7532,7592,7636,7677,7889,7990,8042,8096,8149,8210,8262,8313,8367,8424,13006,13048,13103,13154,13319,13361,13405,13448,13494,13641,13886,13927,13974,14024,14071,14117,14162,14203,14247,14294,14421,14574"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\eb41759101879028f49add9e86811a8d\\transformed\\core-1.12.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "2,3,4,6,7,11,12,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,77,78,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,103,109,110,111,112,113,114,115,221,249,250,254,255,259,266,267,275,281,291,324,345,378", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,210,282,412,477,717,786,891,961,1029,1101,1171,1232,1306,1379,1440,1501,1563,1627,1689,1750,1818,1918,1978,2044,2117,2186,2243,2295,2357,2429,2505,4827,4862,5059,5114,5177,5232,5290,5348,5409,5472,5529,5580,5630,5691,5748,5814,5848,5883,6178,6515,6582,6654,6723,6792,6866,6938,13692,14997,15114,15315,15425,15626,16109,16181,16552,16755,17056,18787,19468,20150", "endLines": "2,3,4,6,7,11,12,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,77,78,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,103,109,110,111,112,113,114,115,221,249,253,254,258,259,266,267,280,290,323,344,377,383", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "205,277,365,472,538,781,844,956,1024,1096,1166,1227,1301,1374,1435,1496,1558,1622,1684,1745,1813,1913,1973,2039,2112,2181,2238,2290,2352,2424,2500,2565,4857,4892,5109,5172,5227,5285,5343,5404,5467,5524,5575,5625,5686,5743,5809,5843,5878,5913,6243,6577,6649,6718,6787,6861,6933,7021,13758,15109,15310,15420,15621,15750,16176,16243,16750,17051,18782,19463,20145,20312"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\24cc38749a18090be21f81d4377ab5a8\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "100", "startColumns": "4", "startOffsets": "6021", "endColumns": "53", "endOffsets": "6070"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fa6771ee7182fe1eb8efde1f760a5ec5\\transformed\\navigation-common-2.7.6\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "384,397,403,409,418", "startColumns": "4,4,4,4,4", "startOffsets": "20317,20956,21200,21447,21810", "endLines": "396,402,408,411,422", "endColumns": "24,24,24,24,24", "endOffsets": "20951,21195,21442,21575,21987"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7a56af73b15e252c2ac3b5bc69694ed2\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "236,237", "startColumns": "4,4", "startOffsets": "14426,14482", "endColumns": "55,54", "endOffsets": "14477,14532"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5ed700988212d8d9085a7fcc8af113be\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3014,3048,3098,3152,3198,3245,3281,3371,3483,3594", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,59,62,66", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3009,3043,3093,3147,3193,3240,3276,3366,3478,3589,3784"}, "to": {"startLines": "39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,102,123,124,129,130,132,142,143,204,209,210,211,217,218,220,222,223,234,239,242,245", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2570,2629,2688,2748,2808,2868,2928,2988,3048,3108,3168,3228,3288,3347,3407,3467,3527,3587,3647,3707,3767,3827,3887,3947,4006,4066,4126,4185,4244,4303,4362,4421,4480,4554,4612,4667,4718,6125,7354,7419,7682,7748,7894,8429,8481,12908,13159,13213,13249,13499,13549,13646,13763,13810,14299,14579,14691,14802", "endLines": "39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,102,123,124,129,130,132,142,143,204,209,210,211,217,218,220,222,223,234,241,244,248", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "2624,2683,2743,2803,2863,2923,2983,3043,3103,3163,3223,3283,3342,3402,3462,3522,3582,3642,3702,3762,3822,3882,3942,4001,4061,4121,4180,4239,4298,4357,4416,4475,4549,4607,4662,4713,4768,6173,7414,7468,7743,7844,7947,8476,8536,12965,13208,13244,13278,13544,13598,13687,13805,13841,14384,14686,14797,14992"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\58d78ea86540194b0aac1267880374b6\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "76,80", "startColumns": "4,4", "startOffsets": "4773,4950", "endColumns": "53,66", "endOffsets": "4822,5012"}}]}]}