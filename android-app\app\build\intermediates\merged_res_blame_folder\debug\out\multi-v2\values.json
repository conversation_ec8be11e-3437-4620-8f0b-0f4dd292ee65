{"logs": [{"outputFile": "com.taskmanager.app-mergeDebugResources-49:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\df7c5721de23b8c2e2ae8f8f49af0229\\transformed\\navigation-common-2.7.6\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "428,441,447,453,462", "startColumns": "4,4,4,4,4", "startOffsets": "23506,24145,24389,24636,24999", "endLines": "440,446,452,455,466", "endColumns": "24,24,24,24,24", "endOffsets": "24140,24384,24631,24764,25176"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\dc5a69b4df93eea844116bde22da938c\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "258,259", "startColumns": "4,4", "startOffsets": "15821,15877", "endColumns": "55,54", "endOffsets": "15872,15927"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ef90d65d0185958ee2dbef55198da579\\transformed\\navigation-runtime-2.7.6\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "101,312,456,459", "startColumns": "4,4,4,4", "startOffsets": "6288,19437,24769,24884", "endLines": "101,318,458,461", "endColumns": "52,24,24,24", "endOffsets": "6336,19736,24879,24994"}}, {"source": "C:\\Users\\<USER>\\Desktop\\schedule\\task-manager\\android-app\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "2,18,20,29,30,28,19,21,7,9,11,13,25,27,15,17,22,23,6,8,10,12,24,26,14,16,3", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,944,1056,1617,1683,1553,999,1120,239,364,491,620,1364,1487,748,875,1186,1243,182,298,432,552,1307,1423,690,808,97", "endColumns": "41,54,63,65,63,63,56,65,58,67,60,69,58,65,59,68,56,63,56,65,58,67,56,63,57,66,41", "endOffsets": "92,994,1115,1678,1742,1612,1051,1181,293,427,547,685,1418,1548,803,939,1238,1302,234,359,486,615,1359,1482,743,870,134"}, "to": {"startLines": "5,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "370,543,598,662,728,792,856,913,979,1038,1106,1167,1237,1296,1362,1422,1491,1548,1612,1669,1735,1794,1862,1919,1983,2041,2240", "endColumns": "41,54,63,65,63,63,56,65,58,67,60,69,58,65,59,68,56,63,56,65,58,67,56,63,57,66,41", "endOffsets": "407,593,657,723,787,851,908,974,1033,1101,1162,1232,1291,1357,1417,1486,1543,1607,1664,1730,1789,1857,1914,1978,2036,2103,2277"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7e8a26d2d8a7fd613a757452c477b1bb\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,143,229,310,394,463,528,611,717,803,923,977,1046,1107,1176,1265,1360,1434,1531,1624,1722,1871,1962,2050,2146,2244,2308,2376,2463,2557,2624,2696,2768,2869,2978,3054,3123,3171,3237,3301,3358,3415,3487,3537,3591,3662,3733,3803,3872,3930,4006,4077,4151,4237,4287,4357", "endLines": "2,3,4,5,6,7,8,9,10,13,14,15,16,17,18,19,20,21,22,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "138,224,305,389,458,523,606,712,798,918,972,1041,1102,1171,1260,1355,1429,1526,1619,1717,1866,1957,2045,2141,2239,2303,2371,2458,2552,2619,2691,2763,2864,2973,3049,3118,3166,3232,3296,3353,3410,3482,3532,3586,3657,3728,3798,3867,3925,4001,4072,4146,4232,4282,4352,4417"}, "to": {"startLines": "166,167,168,169,170,171,172,173,174,175,178,179,180,181,182,183,184,185,186,187,188,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9928,10016,10102,10183,10267,10336,10401,10484,10590,10676,10796,10850,10919,10980,11049,11138,11233,11307,11404,11497,11595,11744,11835,11923,12019,12117,12181,12249,12336,12430,12497,12569,12641,12742,12851,12927,12996,13044,13110,13174,13231,13288,13360,13410,13464,13535,13606,13676,13745,13803,13879,13950,14024,14110,14160,14230", "endLines": "166,167,168,169,170,171,172,173,174,177,178,179,180,181,182,183,184,185,186,187,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "10011,10097,10178,10262,10331,10396,10479,10585,10671,10791,10845,10914,10975,11044,11133,11228,11302,11399,11492,11590,11739,11830,11918,12014,12112,12176,12244,12331,12425,12492,12564,12636,12737,12846,12922,12991,13039,13105,13169,13226,13283,13355,13405,13459,13530,13601,13671,13740,13798,13874,13945,14019,14105,14155,14225,14290"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1033f5e7992b93c8623f7d35b8f229b5\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "123", "startColumns": "4", "startOffsets": "7466", "endColumns": "49", "endOffsets": "7511"}}, {"source": "C:\\Users\\<USER>\\Desktop\\schedule\\task-manager\\android-app\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "139", "endLines": "30", "endColumns": "12", "endOffsets": "2323"}, "to": {"startLines": "282", "startColumns": "4", "startOffsets": "17150", "endLines": "309", "endColumns": "12", "endOffsets": "19293"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2094e2c8f8f000297a612ce5427e3eff\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3014,3048,3098,3152,3198,3245,3281,3371,3483,3594", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,59,62,66", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3009,3043,3093,3147,3193,3240,3276,3366,3478,3589,3784"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,124,145,146,151,152,154,164,165,226,231,232,233,239,240,242,244,245,256,261,264,267", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3961,4020,4079,4139,4199,4259,4319,4379,4439,4499,4559,4619,4679,4738,4798,4858,4918,4978,5038,5098,5158,5218,5278,5338,5397,5457,5517,5576,5635,5694,5753,5812,5871,5945,6003,6058,6109,7516,8741,8806,9069,9135,9281,9816,9868,14295,14546,14600,14636,14894,14944,15041,15158,15205,15694,15974,16086,16197", "endLines": "61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,124,145,146,151,152,154,164,165,226,231,232,233,239,240,242,244,245,256,263,266,270", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "4015,4074,4134,4194,4254,4314,4374,4434,4494,4554,4614,4674,4733,4793,4853,4913,4973,5033,5093,5153,5213,5273,5333,5392,5452,5512,5571,5630,5689,5748,5807,5866,5940,5998,6053,6104,6159,7564,8801,8855,9130,9231,9334,9863,9923,14352,14595,14631,14665,14939,14993,15082,15200,15236,15779,16081,16192,16387"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\89c2052d98b4a771c80178e16473df28\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "120", "startColumns": "4", "startOffsets": "7309", "endColumns": "42", "endOffsets": "7347"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d8e46d1e1b8b3f4c8c6a75b79667be2b\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "98,102", "startColumns": "4,4", "startOffsets": "6164,6341", "endColumns": "53,66", "endOffsets": "6213,6403"}}, {"source": "C:\\Users\\<USER>\\Desktop\\schedule\\task-manager\\android-app\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "9,49,2,44,43,14,34,35,32,33,31,62,61,12,5,11,10,57,66,56,68,58,55,67,65,40,42,48,47,39,41,26,28,27,13,23,19,51,18,21,22,50,20,17,52,38,6", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "226,1686,55,1508,1461,443,1134,1181,1039,1088,994,2193,2133,356,120,311,268,2005,2337,1952,2445,2057,1901,2391,2280,1332,1419,1631,1580,1287,1373,840,930,884,400,773,598,1784,548,680,727,1739,639,504,1831,1250,161", "endColumns": "41,52,43,50,46,37,46,45,48,45,44,63,59,43,40,44,42,51,53,52,60,51,50,53,56,40,41,54,50,44,45,43,42,45,42,44,40,46,49,46,45,44,40,43,46,36,41", "endOffsets": "263,1734,94,1554,1503,476,1176,1222,1083,1129,1034,2252,2188,395,156,351,306,2052,2386,2000,2501,2104,1947,2440,2332,1368,1456,1681,1626,1327,1414,879,968,925,438,813,634,1826,593,722,768,1779,675,543,1873,1282,198"}, "to": {"startLines": "126,127,129,130,131,139,140,141,142,143,144,147,148,149,150,153,155,156,157,158,159,160,161,162,163,227,228,229,230,234,235,236,237,238,241,246,247,248,249,250,251,252,253,254,255,257,260", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7639,7681,7817,7861,7912,8470,8508,8555,8601,8650,8696,8860,8924,8984,9028,9236,9339,9382,9434,9488,9541,9602,9654,9705,9759,14357,14398,14440,14495,14670,14715,14761,14805,14848,14998,15241,15286,15327,15374,15424,15471,15517,15562,15603,15647,15784,15932", "endColumns": "41,52,43,50,46,37,46,45,48,45,44,63,59,43,40,44,42,51,53,52,60,51,50,53,56,40,41,54,50,44,45,43,42,45,42,44,40,46,49,46,45,44,40,43,46,36,41", "endOffsets": "7676,7729,7856,7907,7954,8503,8550,8596,8645,8691,8736,8919,8979,9023,9064,9276,9377,9429,9483,9536,9597,9649,9700,9754,9811,14393,14435,14490,14541,14710,14756,14800,14843,14889,15036,15281,15322,15369,15419,15466,15512,15557,15598,15642,15689,15816,15969"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\9a1d63a29c4f62158660f7187af857ff\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "122", "startColumns": "4", "startOffsets": "7412", "endColumns": "53", "endOffsets": "7461"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d06be7f65ce94d00ab916dfd7b7892a3\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "128", "startColumns": "4", "startOffsets": "7734", "endColumns": "82", "endOffsets": "7812"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4f2cb26be0b26f5fa07889a18e1f2782\\transformed\\core-1.12.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "2,3,4,6,7,33,34,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,99,100,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,125,132,133,134,135,136,137,138,243,271,272,276,277,281,310,311,319,325,335,368,389,422", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,210,282,412,477,2108,2177,2282,2352,2420,2492,2562,2623,2697,2770,2831,2892,2954,3018,3080,3141,3209,3309,3369,3435,3508,3577,3634,3686,3748,3820,3896,6218,6253,6450,6505,6568,6623,6681,6739,6800,6863,6920,6971,7021,7082,7139,7205,7239,7274,7569,7959,8026,8098,8167,8236,8310,8382,15087,16392,16509,16710,16820,17021,19298,19370,19741,19944,20245,21976,22657,23339", "endLines": "2,3,4,6,7,33,34,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,99,100,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,125,132,133,134,135,136,137,138,243,271,275,276,280,281,310,311,324,334,367,388,421,427", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "205,277,365,472,538,2172,2235,2347,2415,2487,2557,2618,2692,2765,2826,2887,2949,3013,3075,3136,3204,3304,3364,3430,3503,3572,3629,3681,3743,3815,3891,3956,6248,6283,6500,6563,6618,6676,6734,6795,6858,6915,6966,7016,7077,7134,7200,7234,7269,7304,7634,8021,8093,8162,8231,8305,8377,8465,15153,16504,16705,16815,17016,17145,19365,19432,19939,20240,21971,22652,23334,23501"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ef6ba7e7ef3a1fe14636c377107beb4f\\transformed\\activity-1.8.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "103,121", "startColumns": "4,4", "startOffsets": "6408,7352", "endColumns": "41,59", "endOffsets": "6445,7407"}}]}]}