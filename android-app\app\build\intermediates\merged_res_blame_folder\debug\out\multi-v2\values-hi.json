{"logs": [{"outputFile": "com.taskmanager.app-mergeDebugResources-49:/values-hi/values-hi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2094e2c8f8f000297a612ce5427e3eff\\transformed\\ui-release\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,283,376,474,563,641,738,827,912,980,1049,1130,1215,1288,1369,1435", "endColumns": "94,82,92,97,88,77,96,88,84,67,68,80,84,72,80,65,119", "endOffsets": "195,278,371,469,558,636,733,822,907,975,1044,1125,1210,1283,1364,1430,1550"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "858,953,1036,1129,1227,1316,1394,7678,7767,7852,7920,7989,8070,8155,8329,8410,8476", "endColumns": "94,82,92,97,88,77,96,88,84,67,68,80,84,72,80,65,119", "endOffsets": "948,1031,1124,1222,1311,1389,1486,7762,7847,7915,7984,8065,8150,8223,8405,8471,8591"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4f2cb26be0b26f5fa07889a18e1f2782\\transformed\\core-1.12.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,361,462,575,681,808", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "148,251,356,457,570,676,803,904"}, "to": {"startLines": "2,3,4,5,6,7,8,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,306,411,512,625,731,8228", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "198,301,406,507,620,726,853,8324"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\dc5a69b4df93eea844116bde22da938c\\transformed\\foundation-release\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,85", "endOffsets": "135,221"}, "to": {"startLines": "83,84", "startColumns": "4,4", "startOffsets": "8596,8681", "endColumns": "84,85", "endOffsets": "8676,8762"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7e8a26d2d8a7fd613a757452c477b1bb\\transformed\\material3-release\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,291,415,531,626,722,835,973,1093,1243,1328,1431,1522,1619,1749,1869,1977,2122,2268,2398,2587,2714,2832,2954,3080,3172,3267,3395,3521,3620,3722,3834,3980,4132,4246,4346,4422,4522,4621,4707,4797,4902,4982,5066,5166,5266,5361,5463,5549,5651,5749,5853,5968,6048,6148", "endColumns": "117,117,123,115,94,95,112,137,119,149,84,102,90,96,129,119,107,144,145,129,188,126,117,121,125,91,94,127,125,98,101,111,145,151,113,99,75,99,98,85,89,104,79,83,99,99,94,101,85,101,97,103,114,79,99,93", "endOffsets": "168,286,410,526,621,717,830,968,1088,1238,1323,1426,1517,1614,1744,1864,1972,2117,2263,2393,2582,2709,2827,2949,3075,3167,3262,3390,3516,3615,3717,3829,3975,4127,4241,4341,4417,4517,4616,4702,4792,4897,4977,5061,5161,5261,5356,5458,5544,5646,5744,5848,5963,6043,6143,6237"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1491,1609,1727,1851,1967,2062,2158,2271,2409,2529,2679,2764,2867,2958,3055,3185,3305,3413,3558,3704,3834,4023,4150,4268,4390,4516,4608,4703,4831,4957,5056,5158,5270,5416,5568,5682,5782,5858,5958,6057,6143,6233,6338,6418,6502,6602,6702,6797,6899,6985,7087,7185,7289,7404,7484,7584", "endColumns": "117,117,123,115,94,95,112,137,119,149,84,102,90,96,129,119,107,144,145,129,188,126,117,121,125,91,94,127,125,98,101,111,145,151,113,99,75,99,98,85,89,104,79,83,99,99,94,101,85,101,97,103,114,79,99,93", "endOffsets": "1604,1722,1846,1962,2057,2153,2266,2404,2524,2674,2759,2862,2953,3050,3180,3300,3408,3553,3699,3829,4018,4145,4263,4385,4511,4603,4698,4826,4952,5051,5153,5265,5411,5563,5677,5777,5853,5953,6052,6138,6228,6333,6413,6497,6597,6697,6792,6894,6980,7082,7180,7284,7399,7479,7579,7673"}}]}]}