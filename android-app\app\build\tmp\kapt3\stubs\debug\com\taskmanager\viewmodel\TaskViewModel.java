package com.taskmanager.viewmodel;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000d\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0002\b\u000b\n\u0002\u0010\t\n\u0002\b\t\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0006\u0010\u001f\u001a\u00020 J*\u0010!\u001a\u00020 2\u0006\u0010\"\u001a\u00020\u00072\u0006\u0010#\u001a\u00020\u00072\u0012\u0010$\u001a\u000e\u0012\u0004\u0012\u00020&\u0012\u0004\u0012\u00020 0%J\u000e\u0010\'\u001a\u00020 2\u0006\u0010(\u001a\u00020\u000eJ\u0018\u0010)\u001a\u0004\u0018\u00010\u000e2\u0006\u0010*\u001a\u00020\nH\u0086@\u00a2\u0006\u0002\u0010+J\u0010\u0010,\u001a\u00020\u00072\u0006\u0010-\u001a\u00020\u0007H\u0002J\u0006\u0010.\u001a\u00020 J\u000e\u0010/\u001a\u00020 2\u0006\u0010(\u001a\u00020\u000eJ\u000e\u00100\u001a\u00020 2\u0006\u00101\u001a\u000202J\u000e\u00103\u001a\u00020 2\u0006\u00104\u001a\u000202J\u0010\u00105\u001a\u00020 2\b\u0010(\u001a\u0004\u0018\u00010\u000eJ\u000e\u00106\u001a\u00020 2\u0006\u0010-\u001a\u00020\u0007J\u0016\u00107\u001a\u00020 2\u0006\u00108\u001a\u00020\n2\u0006\u00109\u001a\u00020\fJ\u000e\u0010:\u001a\u00020 2\u0006\u0010(\u001a\u00020\u000eR\u001c\u0010\u0005\u001a\u0010\u0012\f\u0012\n \b*\u0004\u0018\u00010\u00070\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\t\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\n0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\r\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u000e0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00070\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u001d\u0010\u0013\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\u00140\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0012R\u001d\u0010\u0016\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\u00140\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0012R\u0019\u0010\u0018\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\n0\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0012R\u0017\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\f0\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0012R\u000e\u0010\u001b\u001a\u00020\u001cX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u001d\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u000e0\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u0012\u00a8\u0006;"}, d2 = {"Lcom/taskmanager/viewmodel/TaskViewModel;", "Landroidx/lifecycle/AndroidViewModel;", "application", "Landroid/app/Application;", "(Landroid/app/Application;)V", "_currentDate", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Ljava/time/LocalDate;", "kotlin.jvm.PlatformType", "_errorMessage", "", "_isLoading", "", "_selectedTask", "Lcom/taskmanager/data/model/Task;", "currentDate", "Lkotlinx/coroutines/flow/StateFlow;", "getCurrentDate", "()Lkotlinx/coroutines/flow/StateFlow;", "currentDayTasks", "", "getCurrentDayTasks", "currentWeekTasks", "getCurrentWeekTasks", "errorMessage", "getErrorMessage", "isLoading", "repository", "Lcom/taskmanager/data/repository/TaskRepository;", "selectedTask", "getSelectedTask", "clearError", "", "copyTasksToDate", "sourceDate", "targetDate", "onResult", "Lkotlin/Function1;", "", "deleteTask", "task", "getTaskById", "id", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getWeekStart", "date", "goToToday", "insertTask", "navigateDate", "days", "", "navigateWeek", "weeks", "selectTask", "setCurrentDate", "toggleTaskCompletion", "taskId", "completed", "updateTask", "app_debug"})
public final class TaskViewModel extends androidx.lifecycle.AndroidViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.taskmanager.data.repository.TaskRepository repository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.time.LocalDate> _currentDate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.time.LocalDate> currentDate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.taskmanager.data.model.Task> _selectedTask = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.taskmanager.data.model.Task> selectedTask = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _errorMessage = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> errorMessage = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.taskmanager.data.model.Task>> currentDayTasks = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.taskmanager.data.model.Task>> currentWeekTasks = null;
    
    public TaskViewModel(@org.jetbrains.annotations.NotNull()
    android.app.Application application) {
        super(null);
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.time.LocalDate> getCurrentDate() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.taskmanager.data.model.Task> getSelectedTask() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isLoading() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getErrorMessage() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.taskmanager.data.model.Task>> getCurrentDayTasks() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.taskmanager.data.model.Task>> getCurrentWeekTasks() {
        return null;
    }
    
    public final void setCurrentDate(@org.jetbrains.annotations.NotNull()
    java.time.LocalDate date) {
    }
    
    public final void navigateDate(long days) {
    }
    
    public final void navigateWeek(long weeks) {
    }
    
    public final void goToToday() {
    }
    
    public final void selectTask(@org.jetbrains.annotations.Nullable()
    com.taskmanager.data.model.Task task) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getTaskById(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.taskmanager.data.model.Task> $completion) {
        return null;
    }
    
    public final void insertTask(@org.jetbrains.annotations.NotNull()
    com.taskmanager.data.model.Task task) {
    }
    
    public final void updateTask(@org.jetbrains.annotations.NotNull()
    com.taskmanager.data.model.Task task) {
    }
    
    public final void deleteTask(@org.jetbrains.annotations.NotNull()
    com.taskmanager.data.model.Task task) {
    }
    
    public final void toggleTaskCompletion(@org.jetbrains.annotations.NotNull()
    java.lang.String taskId, boolean completed) {
    }
    
    public final void copyTasksToDate(@org.jetbrains.annotations.NotNull()
    java.time.LocalDate sourceDate, @org.jetbrains.annotations.NotNull()
    java.time.LocalDate targetDate, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onResult) {
    }
    
    public final void clearError() {
    }
    
    private final java.time.LocalDate getWeekStart(java.time.LocalDate date) {
        return null;
    }
}