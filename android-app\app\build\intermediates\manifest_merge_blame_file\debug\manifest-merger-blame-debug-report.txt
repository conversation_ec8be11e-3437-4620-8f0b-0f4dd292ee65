1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.taskmanager"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <permission
11-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f2cb26be0b26f5fa07889a18e1f2782\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
12        android:name="com.taskmanager.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f2cb26be0b26f5fa07889a18e1f2782\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f2cb26be0b26f5fa07889a18e1f2782\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.taskmanager.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f2cb26be0b26f5fa07889a18e1f2782\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f2cb26be0b26f5fa07889a18e1f2782\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
16
17    <application
17-->C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:5:5-24:19
18        android:allowBackup="true"
18-->C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:6:9-35
19        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
19-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f2cb26be0b26f5fa07889a18e1f2782\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
20        android:debuggable="true"
21        android:extractNativeLibs="false"
22        android:icon="@mipmap/ic_launcher"
22-->C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:7:9-43
23        android:label="@string/app_name"
23-->C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:8:9-41
24        android:roundIcon="@mipmap/ic_launcher_round"
24-->C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:9:9-54
25        android:supportsRtl="true"
25-->C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:10:9-35
26        android:testOnly="true"
27        android:theme="@style/Theme.TaskManager" >
27-->C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:11:9-49
28        <activity
28-->C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:14:9-22:20
29            android:name="com.taskmanager.MainActivity"
29-->C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:15:13-41
30            android:exported="true"
30-->C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:16:13-36
31            android:theme="@style/Theme.TaskManager" >
31-->C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:17:13-53
32            <intent-filter>
32-->C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:18:13-21:29
33                <action android:name="android.intent.action.MAIN" />
33-->C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:19:17-69
33-->C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:19:25-66
34
35                <category android:name="android.intent.category.LAUNCHER" />
35-->C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:20:17-77
35-->C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:20:27-74
36            </intent-filter>
37        </activity>
38        <activity
38-->[androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\df6b90e6ec87659413307f159368940f\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:23:9-25:39
39            android:name="androidx.activity.ComponentActivity"
39-->[androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\df6b90e6ec87659413307f159368940f\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:24:13-63
40            android:exported="true" />
40-->[androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\df6b90e6ec87659413307f159368940f\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:25:13-36
41        <activity
41-->[androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4e0eea945616262ef8abba3caa123863\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
42            android:name="androidx.compose.ui.tooling.PreviewActivity"
42-->[androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4e0eea945616262ef8abba3caa123863\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
43            android:exported="true" />
43-->[androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4e0eea945616262ef8abba3caa123863\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
44
45        <provider
45-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\557504dd301c32f6c4bacd16be2e004d\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
46            android:name="androidx.startup.InitializationProvider"
46-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\557504dd301c32f6c4bacd16be2e004d\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
47            android:authorities="com.taskmanager.androidx-startup"
47-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\557504dd301c32f6c4bacd16be2e004d\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
48            android:exported="false" >
48-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\557504dd301c32f6c4bacd16be2e004d\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
49            <meta-data
49-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\557504dd301c32f6c4bacd16be2e004d\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
50                android:name="androidx.emoji2.text.EmojiCompatInitializer"
50-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\557504dd301c32f6c4bacd16be2e004d\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
51                android:value="androidx.startup" />
51-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\557504dd301c32f6c4bacd16be2e004d\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
52            <meta-data
52-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d4e3217c068f7dfe6e1b8e26e9b50cbd\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
53                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
53-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d4e3217c068f7dfe6e1b8e26e9b50cbd\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
54                android:value="androidx.startup" />
54-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d4e3217c068f7dfe6e1b8e26e9b50cbd\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
55            <meta-data
55-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
56                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
56-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
57                android:value="androidx.startup" />
57-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
58        </provider>
59
60        <receiver
60-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
61            android:name="androidx.profileinstaller.ProfileInstallReceiver"
61-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
62            android:directBootAware="false"
62-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
63            android:enabled="true"
63-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
64            android:exported="true"
64-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
65            android:permission="android.permission.DUMP" >
65-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
66            <intent-filter>
66-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
67                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
67-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
67-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
68            </intent-filter>
69            <intent-filter>
69-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
70                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
70-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
70-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
71            </intent-filter>
72            <intent-filter>
72-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
73                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
73-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
73-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
74            </intent-filter>
75            <intent-filter>
75-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
76                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
76-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
76-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
77            </intent-filter>
78        </receiver>
79    </application>
80
81</manifest>
