1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.taskmanager"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <permission
11-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb41759101879028f49add9e86811a8d\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
12        android:name="com.taskmanager.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb41759101879028f49add9e86811a8d\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb41759101879028f49add9e86811a8d\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.taskmanager.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb41759101879028f49add9e86811a8d\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb41759101879028f49add9e86811a8d\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
16
17    <application
17-->C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:5:5-22:19
18        android:allowBackup="true"
18-->C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:6:9-35
19        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
19-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb41759101879028f49add9e86811a8d\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
20        android:debuggable="true"
21        android:extractNativeLibs="false"
22        android:icon="@mipmap/ic_launcher"
22-->C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:7:9-43
23        android:label="@string/app_name"
23-->C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:8:9-41
24        android:supportsRtl="true"
24-->C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:9:9-35
25        android:testOnly="true"
26        android:theme="@style/Theme.TaskManager" >
26-->C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:10:9-49
27        <activity
27-->C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:12:9-20:20
28            android:name="com.taskmanager.MainActivity"
28-->C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:13:13-41
29            android:exported="true"
29-->C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:14:13-36
30            android:theme="@style/Theme.TaskManager" >
30-->C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:15:13-53
31            <intent-filter>
31-->C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:16:13-19:29
32                <action android:name="android.intent.action.MAIN" />
32-->C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:17:17-69
32-->C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:17:25-66
33
34                <category android:name="android.intent.category.LAUNCHER" />
34-->C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:18:17-77
34-->C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:18:27-74
35            </intent-filter>
36        </activity>
37        <activity
37-->[androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0dab35ff3c85b96d4044ec0e200c394\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
38            android:name="androidx.compose.ui.tooling.PreviewActivity"
38-->[androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0dab35ff3c85b96d4044ec0e200c394\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
39            android:exported="true" />
39-->[androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0dab35ff3c85b96d4044ec0e200c394\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
40        <activity
40-->[androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cbabb222178311213a775cb6b3442975\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:23:9-25:39
41            android:name="androidx.activity.ComponentActivity"
41-->[androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cbabb222178311213a775cb6b3442975\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:24:13-63
42            android:exported="true" />
42-->[androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cbabb222178311213a775cb6b3442975\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:25:13-36
43
44        <provider
44-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\de349cf698518a5b79e4de3dd50091f0\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
45            android:name="androidx.startup.InitializationProvider"
45-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\de349cf698518a5b79e4de3dd50091f0\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
46            android:authorities="com.taskmanager.androidx-startup"
46-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\de349cf698518a5b79e4de3dd50091f0\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
47            android:exported="false" >
47-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\de349cf698518a5b79e4de3dd50091f0\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
48            <meta-data
48-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\de349cf698518a5b79e4de3dd50091f0\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
49                android:name="androidx.emoji2.text.EmojiCompatInitializer"
49-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\de349cf698518a5b79e4de3dd50091f0\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
50                android:value="androidx.startup" />
50-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\de349cf698518a5b79e4de3dd50091f0\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
51            <meta-data
51-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a81dcae0be05ee6ff3df92dff9a71584\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
52                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
52-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a81dcae0be05ee6ff3df92dff9a71584\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
53                android:value="androidx.startup" />
53-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a81dcae0be05ee6ff3df92dff9a71584\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
54            <meta-data
54-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
55                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
55-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
56                android:value="androidx.startup" />
56-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
57        </provider>
58
59        <receiver
59-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
60            android:name="androidx.profileinstaller.ProfileInstallReceiver"
60-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
61            android:directBootAware="false"
61-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
62            android:enabled="true"
62-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
63            android:exported="true"
63-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
64            android:permission="android.permission.DUMP" >
64-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
65            <intent-filter>
65-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
66                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
66-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
66-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
67            </intent-filter>
68            <intent-filter>
68-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
69                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
69-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
69-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
70            </intent-filter>
71            <intent-filter>
71-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
72                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
72-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
72-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
73            </intent-filter>
74            <intent-filter>
74-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
75                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
75-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
75-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
76            </intent-filter>
77        </receiver>
78    </application>
79
80</manifest>
