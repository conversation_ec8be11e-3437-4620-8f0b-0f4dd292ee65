{"logs": [{"outputFile": "com.taskmanager.app-mergeDebugResources-53:/values-ro/values-ro.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\20318a4763c515b8e8114db0afb41606\\transformed\\material3-release\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,185,312,429,555,665,762,876,1013,1133,1276,1360,1462,1557,1655,1775,1902,2009,2147,2283,2424,2600,2737,2856,2979,3105,3201,3297,3424,3565,3665,3770,3881,4021,4167,4279,4383,4459,4554,4646,4732,4819,4920,5002,5085,5184,5288,5383,5484,5571,5682,5782,5888,6009,6091,6206", "endColumns": "129,126,116,125,109,96,113,136,119,142,83,101,94,97,119,126,106,137,135,140,175,136,118,122,125,95,95,126,140,99,104,110,139,145,111,103,75,94,91,85,86,100,81,82,98,103,94,100,86,110,99,105,120,81,114,103", "endOffsets": "180,307,424,550,660,757,871,1008,1128,1271,1355,1457,1552,1650,1770,1897,2004,2142,2278,2419,2595,2732,2851,2974,3100,3196,3292,3419,3560,3660,3765,3876,4016,4162,4274,4378,4454,4549,4641,4727,4814,4915,4997,5080,5179,5283,5378,5479,5566,5677,5777,5883,6004,6086,6201,6305"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1465,1595,1722,1839,1965,2075,2172,2286,2423,2543,2686,2770,2872,2967,3065,3185,3312,3419,3557,3693,3834,4010,4147,4266,4389,4515,4611,4707,4834,4975,5075,5180,5291,5431,5577,5689,5793,5869,5964,6056,6142,6229,6330,6412,6495,6594,6698,6793,6894,6981,7092,7192,7298,7419,7501,7616", "endColumns": "129,126,116,125,109,96,113,136,119,142,83,101,94,97,119,126,106,137,135,140,175,136,118,122,125,95,95,126,140,99,104,110,139,145,111,103,75,94,91,85,86,100,81,82,98,103,94,100,86,110,99,105,120,81,114,103", "endOffsets": "1590,1717,1834,1960,2070,2167,2281,2418,2538,2681,2765,2867,2962,3060,3180,3307,3414,3552,3688,3829,4005,4142,4261,4384,4510,4606,4702,4829,4970,5070,5175,5286,5426,5572,5684,5788,5864,5959,6051,6137,6224,6325,6407,6490,6589,6693,6788,6889,6976,7087,7187,7293,7414,7496,7611,7715"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7a56af73b15e252c2ac3b5bc69694ed2\\transformed\\foundation-release\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,150", "endColumns": "94,99", "endOffsets": "145,245"}, "to": {"startLines": "83,84", "startColumns": "4,4", "startOffsets": "8645,8740", "endColumns": "94,99", "endOffsets": "8735,8835"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\eb41759101879028f49add9e86811a8d\\transformed\\core-1.12.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,454,556,665,782", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "148,250,350,449,551,660,777,878"}, "to": {"startLines": "2,3,4,5,6,7,8,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,405,504,606,715,8285", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "198,300,400,499,601,710,827,8381"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5ed700988212d8d9085a7fcc8af113be\\transformed\\ui-release\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,383,485,573,651,738,829,911,983,1052,1140,1230,1303,1380,1447", "endColumns": "96,83,96,101,87,77,86,90,81,71,68,87,89,72,76,66,114", "endOffsets": "197,281,378,480,568,646,733,824,906,978,1047,1135,1225,1298,1375,1442,1557"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "832,929,1013,1110,1212,1300,1378,7720,7811,7893,7965,8034,8122,8212,8386,8463,8530", "endColumns": "96,83,96,101,87,77,86,90,81,71,68,87,89,72,76,66,114", "endOffsets": "924,1008,1105,1207,1295,1373,1460,7806,7888,7960,8029,8117,8207,8280,8458,8525,8640"}}]}]}