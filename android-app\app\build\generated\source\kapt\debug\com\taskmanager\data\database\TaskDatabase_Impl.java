package com.taskmanager.data.database;

import androidx.annotation.NonNull;
import androidx.room.DatabaseConfiguration;
import androidx.room.InvalidationTracker;
import androidx.room.RoomDatabase;
import androidx.room.RoomOpenHelper;
import androidx.room.migration.AutoMigrationSpec;
import androidx.room.migration.Migration;
import androidx.room.util.DBUtil;
import androidx.room.util.TableInfo;
import androidx.sqlite.db.SupportSQLiteDatabase;
import androidx.sqlite.db.SupportSQLiteOpenHelper;
import java.lang.Class;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@SuppressWarnings({"unchecked", "deprecation"})
public final class TaskDatabase_Impl extends TaskDatabase {
  private volatile TaskDao _taskDao;

  private volatile ThemeDao _themeDao;

  @Override
  @NonNull
  protected SupportSQLiteOpenHelper createOpenHelper(@NonNull final DatabaseConfiguration config) {
    final SupportSQLiteOpenHelper.Callback _openCallback = new RoomOpenHelper(config, new RoomOpenHelper.Delegate(3) {
      @Override
      public void createAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("CREATE TABLE IF NOT EXISTS `tasks` (`id` TEXT NOT NULL, `title` TEXT NOT NULL, `description` TEXT, `date` TEXT NOT NULL, `startTime` TEXT NOT NULL, `endTime` TEXT NOT NULL, `priority` TEXT NOT NULL, `category` TEXT NOT NULL, `completed` INTEGER NOT NULL, PRIMARY KEY(`id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `theme_settings` (`id` INTEGER NOT NULL, `backgroundStartColor` INTEGER NOT NULL, `backgroundEndColor` INTEGER NOT NULL, `workCategoryColor` INTEGER NOT NULL, `personalCategoryColor` INTEGER NOT NULL, `studyCategoryColor` INTEGER NOT NULL, `healthCategoryColor` INTEGER NOT NULL, `otherCategoryColor` INTEGER NOT NULL, `highPriorityColor` INTEGER NOT NULL, `mediumPriorityColor` INTEGER NOT NULL, `lowPriorityColor` INTEGER NOT NULL, `primaryTextColor` INTEGER NOT NULL, `secondaryTextColor` INTEGER NOT NULL, `onTaskTextColor` INTEGER NOT NULL, `cardBackgroundColor` INTEGER NOT NULL, `cardBackgroundDarkColor` INTEGER NOT NULL, `primaryColor` INTEGER NOT NULL, `primaryVariantColor` INTEGER NOT NULL, PRIMARY KEY(`id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)");
        db.execSQL("INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '767d293420d3269323089b6a3b66ff90')");
      }

      @Override
      public void dropAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("DROP TABLE IF EXISTS `tasks`");
        db.execSQL("DROP TABLE IF EXISTS `theme_settings`");
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onDestructiveMigration(db);
          }
        }
      }

      @Override
      public void onCreate(@NonNull final SupportSQLiteDatabase db) {
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onCreate(db);
          }
        }
      }

      @Override
      public void onOpen(@NonNull final SupportSQLiteDatabase db) {
        mDatabase = db;
        internalInitInvalidationTracker(db);
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onOpen(db);
          }
        }
      }

      @Override
      public void onPreMigrate(@NonNull final SupportSQLiteDatabase db) {
        DBUtil.dropFtsSyncTriggers(db);
      }

      @Override
      public void onPostMigrate(@NonNull final SupportSQLiteDatabase db) {
      }

      @Override
      @NonNull
      public RoomOpenHelper.ValidationResult onValidateSchema(
          @NonNull final SupportSQLiteDatabase db) {
        final HashMap<String, TableInfo.Column> _columnsTasks = new HashMap<String, TableInfo.Column>(9);
        _columnsTasks.put("id", new TableInfo.Column("id", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("title", new TableInfo.Column("title", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("description", new TableInfo.Column("description", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("date", new TableInfo.Column("date", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("startTime", new TableInfo.Column("startTime", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("endTime", new TableInfo.Column("endTime", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("priority", new TableInfo.Column("priority", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("category", new TableInfo.Column("category", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("completed", new TableInfo.Column("completed", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysTasks = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesTasks = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoTasks = new TableInfo("tasks", _columnsTasks, _foreignKeysTasks, _indicesTasks);
        final TableInfo _existingTasks = TableInfo.read(db, "tasks");
        if (!_infoTasks.equals(_existingTasks)) {
          return new RoomOpenHelper.ValidationResult(false, "tasks(com.taskmanager.data.model.Task).\n"
                  + " Expected:\n" + _infoTasks + "\n"
                  + " Found:\n" + _existingTasks);
        }
        final HashMap<String, TableInfo.Column> _columnsThemeSettings = new HashMap<String, TableInfo.Column>(18);
        _columnsThemeSettings.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsThemeSettings.put("backgroundStartColor", new TableInfo.Column("backgroundStartColor", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsThemeSettings.put("backgroundEndColor", new TableInfo.Column("backgroundEndColor", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsThemeSettings.put("workCategoryColor", new TableInfo.Column("workCategoryColor", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsThemeSettings.put("personalCategoryColor", new TableInfo.Column("personalCategoryColor", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsThemeSettings.put("studyCategoryColor", new TableInfo.Column("studyCategoryColor", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsThemeSettings.put("healthCategoryColor", new TableInfo.Column("healthCategoryColor", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsThemeSettings.put("otherCategoryColor", new TableInfo.Column("otherCategoryColor", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsThemeSettings.put("highPriorityColor", new TableInfo.Column("highPriorityColor", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsThemeSettings.put("mediumPriorityColor", new TableInfo.Column("mediumPriorityColor", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsThemeSettings.put("lowPriorityColor", new TableInfo.Column("lowPriorityColor", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsThemeSettings.put("primaryTextColor", new TableInfo.Column("primaryTextColor", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsThemeSettings.put("secondaryTextColor", new TableInfo.Column("secondaryTextColor", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsThemeSettings.put("onTaskTextColor", new TableInfo.Column("onTaskTextColor", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsThemeSettings.put("cardBackgroundColor", new TableInfo.Column("cardBackgroundColor", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsThemeSettings.put("cardBackgroundDarkColor", new TableInfo.Column("cardBackgroundDarkColor", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsThemeSettings.put("primaryColor", new TableInfo.Column("primaryColor", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsThemeSettings.put("primaryVariantColor", new TableInfo.Column("primaryVariantColor", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysThemeSettings = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesThemeSettings = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoThemeSettings = new TableInfo("theme_settings", _columnsThemeSettings, _foreignKeysThemeSettings, _indicesThemeSettings);
        final TableInfo _existingThemeSettings = TableInfo.read(db, "theme_settings");
        if (!_infoThemeSettings.equals(_existingThemeSettings)) {
          return new RoomOpenHelper.ValidationResult(false, "theme_settings(com.taskmanager.data.model.ThemeSettings).\n"
                  + " Expected:\n" + _infoThemeSettings + "\n"
                  + " Found:\n" + _existingThemeSettings);
        }
        return new RoomOpenHelper.ValidationResult(true, null);
      }
    }, "767d293420d3269323089b6a3b66ff90", "eeeb4b0c7bec430cc1b8c2b1eee282c3");
    final SupportSQLiteOpenHelper.Configuration _sqliteConfig = SupportSQLiteOpenHelper.Configuration.builder(config.context).name(config.name).callback(_openCallback).build();
    final SupportSQLiteOpenHelper _helper = config.sqliteOpenHelperFactory.create(_sqliteConfig);
    return _helper;
  }

  @Override
  @NonNull
  protected InvalidationTracker createInvalidationTracker() {
    final HashMap<String, String> _shadowTablesMap = new HashMap<String, String>(0);
    final HashMap<String, Set<String>> _viewTables = new HashMap<String, Set<String>>(0);
    return new InvalidationTracker(this, _shadowTablesMap, _viewTables, "tasks","theme_settings");
  }

  @Override
  public void clearAllTables() {
    super.assertNotMainThread();
    final SupportSQLiteDatabase _db = super.getOpenHelper().getWritableDatabase();
    try {
      super.beginTransaction();
      _db.execSQL("DELETE FROM `tasks`");
      _db.execSQL("DELETE FROM `theme_settings`");
      super.setTransactionSuccessful();
    } finally {
      super.endTransaction();
      _db.query("PRAGMA wal_checkpoint(FULL)").close();
      if (!_db.inTransaction()) {
        _db.execSQL("VACUUM");
      }
    }
  }

  @Override
  @NonNull
  protected Map<Class<?>, List<Class<?>>> getRequiredTypeConverters() {
    final HashMap<Class<?>, List<Class<?>>> _typeConvertersMap = new HashMap<Class<?>, List<Class<?>>>();
    _typeConvertersMap.put(TaskDao.class, TaskDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(ThemeDao.class, ThemeDao_Impl.getRequiredConverters());
    return _typeConvertersMap;
  }

  @Override
  @NonNull
  public Set<Class<? extends AutoMigrationSpec>> getRequiredAutoMigrationSpecs() {
    final HashSet<Class<? extends AutoMigrationSpec>> _autoMigrationSpecsSet = new HashSet<Class<? extends AutoMigrationSpec>>();
    return _autoMigrationSpecsSet;
  }

  @Override
  @NonNull
  public List<Migration> getAutoMigrations(
      @NonNull final Map<Class<? extends AutoMigrationSpec>, AutoMigrationSpec> autoMigrationSpecs) {
    final List<Migration> _autoMigrations = new ArrayList<Migration>();
    return _autoMigrations;
  }

  @Override
  public TaskDao taskDao() {
    if (_taskDao != null) {
      return _taskDao;
    } else {
      synchronized(this) {
        if(_taskDao == null) {
          _taskDao = new TaskDao_Impl(this);
        }
        return _taskDao;
      }
    }
  }

  @Override
  public ThemeDao themeDao() {
    if (_themeDao != null) {
      return _themeDao;
    } else {
      synchronized(this) {
        if(_themeDao == null) {
          _themeDao = new ThemeDao_Impl(this);
        }
        return _themeDao;
      }
    }
  }
}
