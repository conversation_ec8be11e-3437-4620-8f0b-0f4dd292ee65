# 智能任务清单

一个功能丰富的任务管理应用，采用现代化设计，参考甘特图和苹果日历的视觉风格。

## 功能特性

### 🎯 核心功能
- **任务管理**：添加、编辑、删除任务
- **时间设置**：为每个任务设置具体的日期和时间
- **持续时间**：设置任务的预计完成时间
- **优先级**：高、中、低三个优先级别
- **分类管理**：工作、个人、学习、健康、其他

### 📅 双视图模式
- **日视图**：显示单日的所有任务，按时间轴排列
- **周视图**：显示一周的任务概览，网格布局

### 🔄 任务复制
- 可以将一天的所有任务复制到未来的任何一天
- 支持批量复制，提高效率

### 🎨 设计特色
- **现代化UI**：采用渐变色彩和圆角设计
- **甘特图风格**：时间轴清晰展示任务安排
- **苹果日历风格**：简洁优雅的界面设计
- **响应式布局**：支持桌面和移动设备

## 使用方法

### 启动应用
1. 直接在浏览器中打开 `index.html` 文件
2. 无需安装任何依赖，开箱即用

### 基本操作

#### 添加任务
1. 点击"添加任务"按钮
2. 填写任务信息：
   - 任务标题（必填）
   - 任务描述（可选）
   - 日期和时间（必填）
   - 持续时间（默认60分钟）
   - 优先级（高/中/低）
   - 分类（工作/个人/学习/健康/其他）
3. 点击"保存任务"

#### 编辑任务
1. 在任务卡片上悬停，点击编辑图标
2. 修改任务信息
3. 保存更改

#### 删除任务
1. 在任务卡片上悬停，点击删除图标
2. 确认删除操作

#### 切换视图
- 点击头部的"日视图"或"周视图"按钮
- 使用左右箭头导航不同的日期/周
- 点击"今天"按钮快速回到当前日期

#### 复制任务
1. 在日视图中，点击"复制到其他天"按钮
2. 选择要复制到的目标日期
3. 确认复制操作

### 快捷操作
- **周视图中点击某一天**：快速切换到该天的日视图
- **键盘导航**：使用左右箭头键快速切换日期
- **任务排序**：任务自动按时间顺序排列

## 技术特性

### 前端技术
- **HTML5**：语义化标签，良好的可访问性
- **CSS3**：现代化样式，渐变效果，动画过渡
- **JavaScript ES6+**：模块化代码，面向对象设计

### 数据存储
- **localStorage**：本地存储，数据持久化
- **JSON格式**：结构化数据存储

### 浏览器兼容性
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 文件结构

```
task-manager/
├── index.html          # 主页面
├── styles.css          # 样式文件
├── script.js           # 主要逻辑
└── README.md           # 说明文档
```

## 自定义配置

### 修改时间间隔
在 `script.js` 中的 `generateTimeline()` 方法中，可以修改时间轴的显示间隔。

### 添加新的分类
在 `index.html` 的任务分类选择器中添加新选项，并在 `styles.css` 中添加对应的样式。

### 修改颜色主题
在 `styles.css` 中修改 CSS 变量或渐变色值来自定义主题颜色。

## 数据格式

任务数据结构：
```json
{
  "id": "唯一标识符",
  "title": "任务标题",
  "description": "任务描述",
  "date": "2024-01-01",
  "time": "09:00",
  "duration": 60,
  "priority": "medium",
  "category": "work",
  "completed": false
}
```

## 未来计划

- [ ] 添加任务完成状态切换
- [ ] 支持任务提醒功能
- [ ] 添加数据导出/导入功能
- [ ] 支持任务标签系统
- [ ] 添加统计和报表功能
- [ ] 支持团队协作功能

## 许可证

MIT License - 可自由使用和修改

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！
