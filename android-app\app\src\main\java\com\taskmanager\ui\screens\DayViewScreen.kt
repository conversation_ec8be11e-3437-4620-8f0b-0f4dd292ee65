package com.taskmanager.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.ContentCopy
import androidx.compose.material.icons.filled.KeyboardArrowLeft
import androidx.compose.material.icons.filled.KeyboardArrowRight
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.taskmanager.data.model.Task
import com.taskmanager.ui.components.AddTaskDialog
import com.taskmanager.ui.components.CopyTasksDialog
import com.taskmanager.ui.components.DayScheduleView
import com.taskmanager.ui.components.TaskCard
import com.taskmanager.ui.theme.GradientColors
import com.taskmanager.viewmodel.TaskViewModel
import java.time.format.DateTimeFormatter
import java.time.format.TextStyle
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DayViewScreen(
    viewModel: TaskViewModel = viewModel()
) {
    val currentDate by viewModel.currentDate.collectAsState()
    val tasks by viewModel.currentDayTasks.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    
    // 对话框状态
    var showCopyDialog by remember { mutableStateOf(false) }
    var showAddTaskDialog by remember { mutableStateOf(false) }
    var showEditTaskDialog by remember { mutableStateOf(false) }
    var taskToEdit by remember { mutableStateOf<Task?>(null) }

    // 视图模式状态
    var isScheduleView by remember { mutableStateOf(true) }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(GradientColors)
            )
    ) {
        // 头部导航
        Card(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(bottomStart = 16.dp, bottomEnd = 16.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                // 日期导航
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    IconButton(onClick = { viewModel.navigateDate(-1) }) {
                        Icon(Icons.Default.KeyboardArrowLeft, contentDescription = "前一天")
                    }
                    
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = currentDate.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")),
                            style = MaterialTheme.typography.titleLarge,
                            fontWeight = FontWeight.Bold
                        )
                        Text(
                            text = currentDate.dayOfWeek.getDisplayName(TextStyle.FULL, Locale.CHINESE),
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    
                    IconButton(onClick = { viewModel.navigateDate(1) }) {
                        Icon(Icons.Default.KeyboardArrowRight, contentDescription = "后一天")
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 操作按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Button(
                        onClick = { showAddTaskDialog = true },
                        modifier = Modifier.weight(1f)
                    ) {
                        Icon(Icons.Default.Add, contentDescription = null)
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("添加任务")
                    }
                    
                    OutlinedButton(
                        onClick = { 
                            if (tasks.isNotEmpty()) {
                                showCopyDialog = true
                            }
                        },
                        enabled = tasks.isNotEmpty(),
                        modifier = Modifier.weight(1f)
                    ) {
                        Icon(Icons.Default.ContentCopy, contentDescription = null)
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("复制任务")
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                // 视图切换和今天按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    TextButton(onClick = { viewModel.goToToday() }) {
                        Text("回到今天")
                    }

                    OutlinedButton(
                        onClick = { isScheduleView = !isScheduleView }
                    ) {
                        Text(if (isScheduleView) "列表视图" else "日程视图")
                    }
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 任务列表
        if (isLoading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else if (tasks.isEmpty()) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "今天还没有任务",
                        style = MaterialTheme.typography.headlineSmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "点击上方按钮添加任务",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        } else {
            if (isScheduleView) {
                // 日程表视图
                DayScheduleView(
                    tasks = tasks,
                    onTaskClick = { task ->
                        taskToEdit = task
                        showEditTaskDialog = true
                    },
                    modifier = Modifier.fillMaxSize()
                )
            } else {
                // 列表视图
                LazyColumn(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(horizontal = 16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(tasks) { task ->
                        TaskCard(
                            task = task,
                            onEdit = {
                                taskToEdit = task
                                showEditTaskDialog = true
                            },
                            onDelete = { viewModel.deleteTask(task) },
                            onToggleComplete = { completed ->
                                viewModel.toggleTaskCompletion(task.id, completed)
                            }
                        )
                    }

                    // 底部间距
                    item {
                        Spacer(modifier = Modifier.height(16.dp))
                    }
                }
            }
        }
    }
    
    // 添加任务对话框
    if (showAddTaskDialog) {
        AddTaskDialog(
            currentDate = currentDate,
            onDismiss = { showAddTaskDialog = false },
            onConfirm = { task ->
                viewModel.insertTask(task)
                showAddTaskDialog = false
            }
        )
    }

    // 复制任务对话框
    if (showCopyDialog) {
        CopyTasksDialog(
            sourceDate = currentDate,
            taskCount = tasks.size,
            onDismiss = { showCopyDialog = false },
            onConfirm = { targetDate ->
                viewModel.copyTasksToDate(
                    sourceDate = currentDate,
                    targetDate = targetDate
                ) { copiedCount ->
                    // TODO: 显示成功消息
                }
                showCopyDialog = false
            }
        )
    }

    // 编辑任务对话框
    if (showEditTaskDialog && taskToEdit != null) {
        AddTaskDialog(
            currentDate = currentDate,
            task = taskToEdit,
            onDismiss = {
                showEditTaskDialog = false
                taskToEdit = null
            },
            onConfirm = { task ->
                viewModel.updateTask(task)
                showEditTaskDialog = false
                taskToEdit = null
            }
        )
    }
}
