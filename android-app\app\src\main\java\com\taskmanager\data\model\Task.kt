package com.taskmanager.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.time.LocalDate
import java.time.LocalTime

@Entity(tableName = "tasks")
@Parcelize
data class Task(
    @PrimaryKey
    val id: String,
    val title: String,
    val description: String? = null,
    val date: LocalDate,
    val startTime: LocalTime,
    val endTime: LocalTime,
    val priority: TaskPriority,
    val category: TaskCategory,
    val completed: Boolean = false
) : Parcelable {

    // 计算任务持续时间（分钟）
    val durationMinutes: Int
        get() {
            val startMinutes = startTime.hour * 60 + startTime.minute
            val endMinutes = endTime.hour * 60 + endTime.minute
            return if (endMinutes >= startMinutes) {
                endMinutes - startMinutes
            } else {
                // 跨天的情况（如23:00-01:00）
                (24 * 60 - startMinutes) + endMinutes
            }
        }

    // 格式化时间显示
    val timeDisplay: String
        get() = "${startTime.format(java.time.format.DateTimeFormatter.ofPattern("HH:mm"))} - ${endTime.format(java.time.format.DateTimeFormatter.ofPattern("HH:mm"))}"
}

enum class TaskPriority(val displayName: String, val value: String) {
    LOW("低", "low"),
    MEDIUM("中", "medium"),
    HIGH("高", "high");
    
    companion object {
        fun fromValue(value: String): TaskPriority {
            return values().find { it.value == value } ?: MEDIUM
        }
    }
}

enum class TaskCategory(val displayName: String, val value: String) {
    WORK("工作", "work"),
    PERSONAL("个人", "personal"),
    STUDY("学习", "study"),
    HEALTH("健康", "health"),
    OTHER("其他", "other");
    
    companion object {
        fun fromValue(value: String): TaskCategory {
            return values().find { it.value == value } ?: OTHER
        }
    }
}
