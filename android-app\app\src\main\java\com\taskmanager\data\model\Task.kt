package com.taskmanager.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.time.LocalDate
import java.time.LocalTime

@Entity(tableName = "tasks")
@Parcelize
data class Task(
    @PrimaryKey
    val id: String,
    val title: String,
    val description: String? = null,
    val date: LocalDate,
    val time: LocalTime,
    val duration: Int, // 持续时间（分钟）
    val priority: TaskPriority,
    val category: TaskCategory,
    val completed: Boolean = false
) : Parcelable

enum class TaskPriority(val displayName: String, val value: String) {
    LOW("低", "low"),
    MEDIUM("中", "medium"),
    HIGH("高", "high");
    
    companion object {
        fun fromValue(value: String): TaskPriority {
            return values().find { it.value == value } ?: MEDIUM
        }
    }
}

enum class TaskCategory(val displayName: String, val value: String) {
    WORK("工作", "work"),
    PERSONAL("个人", "personal"),
    STUDY("学习", "study"),
    HEALTH("健康", "health"),
    OTHER("其他", "other");
    
    companion object {
        fun fromValue(value: String): TaskCategory {
            return values().find { it.value == value } ?: OTHER
        }
    }
}
