package com.taskmanager.ui.navigation

import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CalendarToday
import androidx.compose.material.icons.filled.CalendarViewWeek
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.navigation.NavDestination.Companion.hierarchy
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.taskmanager.ui.screens.DayViewScreen
import com.taskmanager.ui.screens.WeekViewScreen

sealed class Screen(val route: String, val title: String, val icon: ImageVector) {
    object DayView : Screen("day_view", "日视图", Icons.Default.CalendarToday)
    object WeekView : Screen("week_view", "周视图", Icons.Default.CalendarViewWeek)
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TaskManagerNavigation() {
    val navController = rememberNavController()
    val items = listOf(Screen.DayView, Screen.WeekView)
    
    Scaffold(
        bottomBar = {
            NavigationBar {
                val navBackStackEntry by navController.currentBackStackEntryAsState()
                val currentDestination = navBackStackEntry?.destination
                
                items.forEach { screen ->
                    NavigationBarItem(
                        icon = { Icon(screen.icon, contentDescription = screen.title) },
                        label = { Text(screen.title) },
                        selected = currentDestination?.hierarchy?.any { it.route == screen.route } == true,
                        onClick = {
                            navController.navigate(screen.route) {
                                // Pop up to the start destination of the graph to
                                // avoid building up a large stack of destinations
                                // on the back stack as users select items
                                popUpTo(navController.graph.findStartDestination().id) {
                                    saveState = true
                                }
                                // Avoid multiple copies of the same destination when
                                // reselecting the same item
                                launchSingleTop = true
                                // Restore state when reselecting a previously selected item
                                restoreState = true
                            }
                        }
                    )
                }
            }
        }
    ) { innerPadding ->
        NavHost(
            navController = navController,
            startDestination = Screen.DayView.route,
            modifier = Modifier.padding(innerPadding)
        ) {
            composable(Screen.DayView.route) {
                DayViewScreen()
            }
            composable(Screen.WeekView.route) {
                WeekViewScreen()
            }
        }
    }
}
