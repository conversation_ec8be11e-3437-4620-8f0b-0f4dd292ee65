package com.taskmanager.data.model

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "theme_settings")
data class ThemeSettings(
    @PrimaryKey
    val id: Int = 1, // 只有一个主题设置记录
    
    // 背景颜色
    val backgroundStartColor: Int = Color(0xFF667EEA).toArgb(),
    val backgroundEndColor: Int = Color(0xFF764BA2).toArgb(),
    
    // 任务分类颜色
    val workCategoryColor: Int = Color(0xFF2196F3).toArgb(),
    val personalCategoryColor: Int = Color(0xFF9C27B0).toArgb(),
    val studyCategoryColor: Int = Color(0xFF4CAF50).toArgb(),
    val healthCategoryColor: Int = Color(0xFFFF5722).toArgb(),
    val otherCategoryColor: Int = Color(0xFF607D8B).toArgb(),
    
    // 优先级颜色
    val highPriorityColor: Int = Color(0xFFFF5252).toArgb(),
    val mediumPriorityColor: Int = Color(0xFFFF9800).toArgb(),
    val lowPriorityColor: Int = Color(0xFF4CAF50).toArgb(),
    
    // 文字颜色
    val primaryTextColor: Int = Color(0xFF212121).toArgb(),
    val secondaryTextColor: Int = Color(0xFF757575).toArgb(),
    val onTaskTextColor: Int = Color(0xFFFFFFFF).toArgb(), // 任务卡片上的文字颜色
    
    // 卡片颜色
    val cardBackgroundColor: Int = Color(0xFFFAFAFA).toArgb(),
    val cardBackgroundDarkColor: Int = Color(0xFF2D2D2D).toArgb(),
    
    // 主色调
    val primaryColor: Int = Color(0xFF4FACFE).toArgb(),
    val primaryVariantColor: Int = Color(0xFF00F2FE).toArgb()
) {
    // 转换为Compose Color的扩展属性
    val backgroundStartColorCompose: Color get() = Color(backgroundStartColor)
    val backgroundEndColorCompose: Color get() = Color(backgroundEndColor)
    
    val workCategoryColorCompose: Color get() = Color(workCategoryColor)
    val personalCategoryColorCompose: Color get() = Color(personalCategoryColor)
    val studyCategoryColorCompose: Color get() = Color(studyCategoryColor)
    val healthCategoryColorCompose: Color get() = Color(healthCategoryColor)
    val otherCategoryColorCompose: Color get() = Color(otherCategoryColor)
    
    val highPriorityColorCompose: Color get() = Color(highPriorityColor)
    val mediumPriorityColorCompose: Color get() = Color(mediumPriorityColor)
    val lowPriorityColorCompose: Color get() = Color(lowPriorityColor)
    
    val primaryTextColorCompose: Color get() = Color(primaryTextColor)
    val secondaryTextColorCompose: Color get() = Color(secondaryTextColor)
    val onTaskTextColorCompose: Color get() = Color(onTaskTextColor)
    
    val cardBackgroundColorCompose: Color get() = Color(cardBackgroundColor)
    val cardBackgroundDarkColorCompose: Color get() = Color(cardBackgroundDarkColor)
    
    val primaryColorCompose: Color get() = Color(primaryColor)
    val primaryVariantColorCompose: Color get() = Color(primaryVariantColor)
    
    // 根据分类获取颜色
    fun getCategoryColor(category: TaskCategory): Color {
        return when (category) {
            TaskCategory.WORK -> workCategoryColorCompose
            TaskCategory.PERSONAL -> personalCategoryColorCompose
            TaskCategory.STUDY -> studyCategoryColorCompose
            TaskCategory.HEALTH -> healthCategoryColorCompose
            TaskCategory.OTHER -> otherCategoryColorCompose
        }
    }
    
    // 根据优先级获取颜色
    fun getPriorityColor(priority: TaskPriority): Color {
        return when (priority) {
            TaskPriority.HIGH -> highPriorityColorCompose
            TaskPriority.MEDIUM -> mediumPriorityColorCompose
            TaskPriority.LOW -> lowPriorityColorCompose
        }
    }
    
    // 获取渐变背景色列表
    val gradientColors: List<Color>
        get() = listOf(backgroundStartColorCompose, backgroundEndColorCompose)
}

// 预设主题
object PresetThemes {
    val defaultTheme = ThemeSettings()
    
    val darkTheme = ThemeSettings(
        backgroundStartColor = Color(0xFF1A1A2E).toArgb(),
        backgroundEndColor = Color(0xFF16213E).toArgb(),
        cardBackgroundColor = Color(0xFF2D2D2D).toArgb(),
        primaryTextColor = Color(0xFFFFFFFF).toArgb(),
        secondaryTextColor = Color(0xFFBBBBBB).toArgb()
    )
    
    val greenTheme = ThemeSettings(
        backgroundStartColor = Color(0xFF56AB2F).toArgb(),
        backgroundEndColor = Color(0xFFA8E6CF).toArgb(),
        primaryColor = Color(0xFF4CAF50).toArgb(),
        primaryVariantColor = Color(0xFF81C784).toArgb()
    )
    
    val purpleTheme = ThemeSettings(
        backgroundStartColor = Color(0xFF8360C3).toArgb(),
        backgroundEndColor = Color(0xFF2EBFA5).toArgb(),
        primaryColor = Color(0xFF9C27B0).toArgb(),
        primaryVariantColor = Color(0xFFBA68C8).toArgb()
    )
    
    val orangeTheme = ThemeSettings(
        backgroundStartColor = Color(0xFFFF9A56).toArgb(),
        backgroundEndColor = Color(0xFFFFAD56).toArgb(),
        primaryColor = Color(0xFFFF9800).toArgb(),
        primaryVariantColor = Color(0xFFFFB74D).toArgb()
    )
}
