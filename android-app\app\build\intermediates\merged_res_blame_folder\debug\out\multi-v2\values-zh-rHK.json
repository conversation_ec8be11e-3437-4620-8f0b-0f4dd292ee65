{"logs": [{"outputFile": "com.taskmanager.app-mergeDebugResources-42:/values-zh-rHK/values-zh-rHK.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2cf887c674b6fe54b07f45a262ba7369\\transformed\\material3-1.1.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,155,254,354,452,524,609,711,812,913,1025,1103,1195,1275,1359,1460,1569,1666,1770,1872,1976,2088,2189,2287,2389,2492,2573,2664,2765,2870,2956,3054,3148,3252,3362,3458,3544,3613,3684,3762,3839,3924,4000,4078,4171,4261,4350,4439,4519,4611,4703,4793,4897,4973,5061", "endColumns": "99,98,99,97,71,84,101,100,100,111,77,91,79,83,100,108,96,103,101,103,111,100,97,101,102,80,90,100,104,85,97,93,103,109,95,85,68,70,77,76,84,75,77,92,89,88,88,79,91,91,89,103,75,87,85", "endOffsets": "150,249,349,447,519,604,706,807,908,1020,1098,1190,1270,1354,1455,1564,1661,1765,1867,1971,2083,2184,2282,2384,2487,2568,2659,2760,2865,2951,3049,3143,3247,3357,3453,3539,3608,3679,3757,3834,3919,3995,4073,4166,4256,4345,4434,4514,4606,4698,4788,4892,4968,5056,5142"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,304,404,1313,1385,1470,1572,1673,1774,1886,1964,2056,2136,2220,2321,2430,2527,2631,2733,2837,2949,3050,3148,3250,3353,3434,3525,3626,3731,3817,3915,4009,4113,4223,4319,4584,4731,5379,5524,5702,6043,6119,6197,6290,6380,6469,6558,6638,6730,6822,6912,7016,7092,7180", "endColumns": "99,98,99,97,71,84,101,100,100,111,77,91,79,83,100,108,96,103,101,103,111,100,97,101,102,80,90,100,104,85,97,93,103,109,95,85,68,70,77,76,84,75,77,92,89,88,88,79,91,91,89,103,75,87,85", "endOffsets": "200,299,399,497,1380,1465,1567,1668,1769,1881,1959,2051,2131,2215,2316,2425,2522,2626,2728,2832,2944,3045,3143,3245,3348,3429,3520,3621,3726,3812,3910,4004,4108,4218,4314,4400,4648,4797,5452,5596,5782,6114,6192,6285,6375,6464,6553,6633,6725,6817,6907,7011,7087,7175,7261"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e84e839e6aad51a26b803212332dad05\\transformed\\core-1.10.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "502,594,693,787,881,974,1067,5601", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "589,688,782,876,969,1062,1158,5697"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1c27dd2d2d266e151b930b964e47cb67\\transformed\\ui-release\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,181,255,343,434,512,586,663,741,815,878,941,1014,1089,1156,1231,1296", "endColumns": "75,73,87,90,77,73,76,77,73,62,62,72,74,66,74,64,115", "endOffsets": "176,250,338,429,507,581,658,736,810,873,936,1009,1084,1151,1226,1291,1407"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1163,1239,4405,4493,4653,4802,4876,4953,5031,5105,5168,5231,5304,5457,5787,5862,5927", "endColumns": "75,73,87,90,77,73,76,77,73,62,62,72,74,66,74,64,115", "endOffsets": "1234,1308,4488,4579,4726,4871,4948,5026,5100,5163,5226,5299,5374,5519,5857,5922,6038"}}]}]}