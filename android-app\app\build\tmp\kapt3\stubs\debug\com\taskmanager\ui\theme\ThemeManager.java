package com.taskmanager.ui.theme;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000J\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u000b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0007J\u0006\u0010\u0011\u001a\u00020\u000fJ \u0010\u0012\u001a\u00020\u000f2\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u0014\u00f8\u0001\u0000\u00a2\u0006\u0004\b\u0016\u0010\u0017J \u0010\u0018\u001a\u00020\u000f2\u0006\u0010\u0019\u001a\u00020\u001a2\u0006\u0010\u001b\u001a\u00020\u0014\u00f8\u0001\u0000\u00a2\u0006\u0004\b\u001c\u0010\u001dJ \u0010\u001e\u001a\u00020\u000f2\u0006\u0010\u001f\u001a\u00020 2\u0006\u0010\u001b\u001a\u00020\u0014\u00f8\u0001\u0000\u00a2\u0006\u0004\b!\u0010\"J(\u0010#\u001a\u00020\u000f2\u0006\u0010$\u001a\u00020\u00142\u0006\u0010%\u001a\u00020\u00142\u0006\u0010&\u001a\u00020\u0014\u00f8\u0001\u0000\u00a2\u0006\u0004\b\'\u0010(J\u000e\u0010)\u001a\u00020\u000f2\u0006\u0010*\u001a\u00020\u0007R\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00070\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u000e\u0010\f\u001a\u00020\rX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006+"}, d2 = {"Lcom/taskmanager/ui/theme/ThemeManager;", "Landroidx/lifecycle/ViewModel;", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "_currentTheme", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/taskmanager/data/model/ThemeSettings;", "currentTheme", "Lkotlinx/coroutines/flow/StateFlow;", "getCurrentTheme", "()Lkotlinx/coroutines/flow/StateFlow;", "themeDao", "Lcom/taskmanager/data/database/ThemeDao;", "applyPresetTheme", "", "presetTheme", "resetToDefault", "updateBackgroundColors", "startColor", "Landroidx/compose/ui/graphics/Color;", "endColor", "updateBackgroundColors--OWjLjI", "(JJ)V", "updateCategoryColor", "category", "Lcom/taskmanager/data/model/TaskCategory;", "color", "updateCategoryColor-4WTKRHQ", "(Lcom/taskmanager/data/model/TaskCategory;J)V", "updatePriorityColor", "priority", "Lcom/taskmanager/data/model/TaskPriority;", "updatePriorityColor-4WTKRHQ", "(Lcom/taskmanager/data/model/TaskPriority;J)V", "updateTextColors", "primaryColor", "secondaryColor", "onTaskColor", "updateTextColors-ysEtTa8", "(JJJ)V", "updateTheme", "newTheme", "app_debug"})
public final class ThemeManager extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.taskmanager.data.database.ThemeDao themeDao = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.taskmanager.data.model.ThemeSettings> _currentTheme = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.taskmanager.data.model.ThemeSettings> currentTheme = null;
    
    public ThemeManager(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.taskmanager.data.model.ThemeSettings> getCurrentTheme() {
        return null;
    }
    
    public final void updateTheme(@org.jetbrains.annotations.NotNull()
    com.taskmanager.data.model.ThemeSettings newTheme) {
    }
    
    public final void applyPresetTheme(@org.jetbrains.annotations.NotNull()
    com.taskmanager.data.model.ThemeSettings presetTheme) {
    }
    
    public final void resetToDefault() {
    }
}