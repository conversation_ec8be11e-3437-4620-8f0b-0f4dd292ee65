{"logs": [{"outputFile": "com.taskmanager.app-mergeDebugResources-42:/values-th/values-th.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e84e839e6aad51a26b803212332dad05\\transformed\\core-1.10.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,352,450,553,658,770", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "146,249,347,445,548,653,765,866"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "539,635,738,836,934,1037,1142,6234", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "630,733,831,929,1032,1137,1249,6330"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2cf887c674b6fe54b07f45a262ba7369\\transformed\\material3-1.1.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,272,381,489,561,650,757,883,1003,1140,1222,1318,1405,1499,1613,1725,1826,1949,2069,2193,2341,2460,2574,2695,2813,2901,2996,3104,3233,3325,3439,3542,3663,3793,3899,3992,4069,4141,4223,4302,4403,4479,4562,4661,4759,4853,4953,5035,5132,5226,5324,5437,5513,5619", "endColumns": "108,107,108,107,71,88,106,125,119,136,81,95,86,93,113,111,100,122,119,123,147,118,113,120,117,87,94,107,128,91,113,102,120,129,105,92,76,71,81,78,100,75,82,98,97,93,99,81,96,93,97,112,75,105,101", "endOffsets": "159,267,376,484,556,645,752,878,998,1135,1217,1313,1400,1494,1608,1720,1821,1944,2064,2188,2336,2455,2569,2690,2808,2896,2991,3099,3228,3320,3434,3537,3658,3788,3894,3987,4064,4136,4218,4297,4398,4474,4557,4656,4754,4848,4948,5030,5127,5221,5319,5432,5508,5614,5716"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,322,431,1417,1489,1578,1685,1811,1931,2068,2150,2246,2333,2427,2541,2653,2754,2877,2997,3121,3269,3388,3502,3623,3741,3829,3924,4032,4161,4253,4367,4470,4591,4721,4827,5118,5283,6000,6155,6335,6704,6780,6863,6962,7060,7154,7254,7336,7433,7527,7625,7738,7814,7920", "endColumns": "108,107,108,107,71,88,106,125,119,136,81,95,86,93,113,111,100,122,119,123,147,118,113,120,117,87,94,107,128,91,113,102,120,129,105,92,76,71,81,78,100,75,82,98,97,93,99,81,96,93,97,112,75,105,101", "endOffsets": "209,317,426,534,1484,1573,1680,1806,1926,2063,2145,2241,2328,2422,2536,2648,2749,2872,2992,3116,3264,3383,3497,3618,3736,3824,3919,4027,4156,4248,4362,4465,4586,4716,4822,4915,5190,5350,6077,6229,6431,6775,6858,6957,7055,7149,7249,7331,7428,7522,7620,7733,7809,7915,8017"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1c27dd2d2d266e151b930b964e47cb67\\transformed\\ui-release\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,191,268,365,466,554,639,724,810,893,958,1024,1110,1199,1272,1350,1417", "endColumns": "85,76,96,100,87,84,84,85,82,64,65,85,88,72,77,66,122", "endOffsets": "186,263,360,461,549,634,719,805,888,953,1019,1105,1194,1267,1345,1412,1535"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1254,1340,4920,5017,5195,5355,5440,5525,5611,5694,5759,5825,5911,6082,6436,6514,6581", "endColumns": "85,76,96,100,87,84,84,85,82,64,65,85,88,72,77,66,122", "endOffsets": "1335,1412,5012,5113,5278,5435,5520,5606,5689,5754,5820,5906,5995,6150,6509,6576,6699"}}]}]}