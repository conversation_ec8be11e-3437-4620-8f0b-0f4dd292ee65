{"logs": [{"outputFile": "com.taskmanager.app-mergeDebugResources-42:/values-pa/values-pa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1c27dd2d2d266e151b930b964e47cb67\\transformed\\ui-release\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,373,472,560,638,736,824,908,976,1045,1124,1205,1277,1357,1423", "endColumns": "92,82,91,98,87,77,97,87,83,67,68,78,80,71,79,65,117", "endOffsets": "193,276,368,467,555,633,731,819,903,971,1040,1119,1200,1272,1352,1418,1536"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1293,1386,5052,5144,5314,5485,5563,5661,5749,5833,5901,5970,6049,6211,6563,6643,6709", "endColumns": "92,82,91,98,87,77,97,87,83,67,68,78,80,71,79,65,117", "endOffsets": "1381,1464,5139,5238,5397,5558,5656,5744,5828,5896,5965,6044,6125,6278,6638,6704,6822"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2cf887c674b6fe54b07f45a262ba7369\\transformed\\material3-1.1.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,283,395,510,589,686,801,935,1055,1193,1274,1373,1459,1552,1660,1777,1881,2018,2151,2278,2440,2562,2673,2789,2906,2993,3085,3200,3332,3430,3529,3631,3758,3892,3999,4093,4164,4247,4328,4412,4507,4583,4663,4759,4854,4945,5039,5121,5218,5312,5409,5520,5596,5694", "endColumns": "112,114,111,114,78,96,114,133,119,137,80,98,85,92,107,116,103,136,132,126,161,121,110,115,116,86,91,114,131,97,98,101,126,133,106,93,70,82,80,83,94,75,79,95,94,90,93,81,96,93,96,110,75,97,91", "endOffsets": "163,278,390,505,584,681,796,930,1050,1188,1269,1368,1454,1547,1655,1772,1876,2013,2146,2273,2435,2557,2668,2784,2901,2988,3080,3195,3327,3425,3524,3626,3753,3887,3994,4088,4159,4242,4323,4407,4502,4578,4658,4754,4849,4940,5034,5116,5213,5307,5404,5515,5591,5689,5781"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,333,445,1469,1548,1645,1760,1894,2014,2152,2233,2332,2418,2511,2619,2736,2840,2977,3110,3237,3399,3521,3632,3748,3865,3952,4044,4159,4291,4389,4488,4590,4717,4851,4958,5243,5402,6130,6283,6468,6827,6903,6983,7079,7174,7265,7359,7441,7538,7632,7729,7840,7916,8014", "endColumns": "112,114,111,114,78,96,114,133,119,137,80,98,85,92,107,116,103,136,132,126,161,121,110,115,116,86,91,114,131,97,98,101,126,133,106,93,70,82,80,83,94,75,79,95,94,90,93,81,96,93,96,110,75,97,91", "endOffsets": "213,328,440,555,1543,1640,1755,1889,2009,2147,2228,2327,2413,2506,2614,2731,2835,2972,3105,3232,3394,3516,3627,3743,3860,3947,4039,4154,4286,4384,4483,4585,4712,4846,4953,5047,5309,5480,6206,6362,6558,6898,6978,7074,7169,7260,7354,7436,7533,7627,7724,7835,7911,8009,8101"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e84e839e6aad51a26b803212332dad05\\transformed\\core-1.10.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,358,459,561,659,788", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "148,250,353,454,556,654,783,884"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "560,658,760,863,964,1066,1164,6367", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "653,755,858,959,1061,1159,1288,6463"}}]}]}