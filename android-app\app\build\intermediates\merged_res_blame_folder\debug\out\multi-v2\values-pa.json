{"logs": [{"outputFile": "com.taskmanager.app-mergeDebugResources-42:/values-pa/values-pa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5ed700988212d8d9085a7fcc8af113be\\transformed\\ui-release\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,373,472,560,638,736,824,908,976,1045,1124,1205,1277,1357,1423", "endColumns": "92,82,91,98,87,77,97,87,83,67,68,78,80,71,79,65,117", "endOffsets": "193,276,368,467,555,633,731,819,903,971,1040,1119,1200,1272,1352,1418,1536"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "838,931,1014,1106,1205,1293,1371,7541,7629,7713,7781,7850,7929,8010,8183,8263,8329", "endColumns": "92,82,91,98,87,77,97,87,83,67,68,78,80,71,79,65,117", "endOffsets": "926,1009,1101,1200,1288,1366,1464,7624,7708,7776,7845,7924,8005,8077,8258,8324,8442"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7a56af73b15e252c2ac3b5bc69694ed2\\transformed\\foundation-release\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,86", "endOffsets": "137,224"}, "to": {"startLines": "83,84", "startColumns": "4,4", "startOffsets": "8447,8534", "endColumns": "86,86", "endOffsets": "8529,8616"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\eb41759101879028f49add9e86811a8d\\transformed\\core-1.12.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,358,459,561,659,788", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "148,250,353,454,556,654,783,884"}, "to": {"startLines": "2,3,4,5,6,7,8,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,408,509,611,709,8082", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "198,300,403,504,606,704,833,8178"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\20318a4763c515b8e8114db0afb41606\\transformed\\material3-release\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,291,407,526,623,724,842,980,1104,1247,1332,1435,1525,1622,1734,1855,1963,2098,2235,2366,2532,2658,2773,2892,3012,3103,3199,3318,3454,3556,3659,3765,3897,4035,4146,4245,4321,4418,4519,4604,4692,4791,4871,4955,5055,5154,5249,5347,5433,5534,5632,5734,5849,5929,6031", "endColumns": "116,118,115,118,96,100,117,137,123,142,84,102,89,96,111,120,107,134,136,130,165,125,114,118,119,90,95,118,135,101,102,105,131,137,110,98,75,96,100,84,87,98,79,83,99,98,94,97,85,100,97,101,114,79,101,95", "endOffsets": "167,286,402,521,618,719,837,975,1099,1242,1327,1430,1520,1617,1729,1850,1958,2093,2230,2361,2527,2653,2768,2887,3007,3098,3194,3313,3449,3551,3654,3760,3892,4030,4141,4240,4316,4413,4514,4599,4687,4786,4866,4950,5050,5149,5244,5342,5428,5529,5627,5729,5844,5924,6026,6122"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1469,1586,1705,1821,1940,2037,2138,2256,2394,2518,2661,2746,2849,2939,3036,3148,3269,3377,3512,3649,3780,3946,4072,4187,4306,4426,4517,4613,4732,4868,4970,5073,5179,5311,5449,5560,5659,5735,5832,5933,6018,6106,6205,6285,6369,6469,6568,6663,6761,6847,6948,7046,7148,7263,7343,7445", "endColumns": "116,118,115,118,96,100,117,137,123,142,84,102,89,96,111,120,107,134,136,130,165,125,114,118,119,90,95,118,135,101,102,105,131,137,110,98,75,96,100,84,87,98,79,83,99,98,94,97,85,100,97,101,114,79,101,95", "endOffsets": "1581,1700,1816,1935,2032,2133,2251,2389,2513,2656,2741,2844,2934,3031,3143,3264,3372,3507,3644,3775,3941,4067,4182,4301,4421,4512,4608,4727,4863,4965,5068,5174,5306,5444,5555,5654,5730,5827,5928,6013,6101,6200,6280,6364,6464,6563,6658,6756,6842,6943,7041,7143,7258,7338,7440,7536"}}]}]}