-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:2:1-24:12
INJECTED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:2:1-24:12
INJECTED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:2:1-24:12
INJECTED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:2:1-24:12
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa6771ee7182fe1eb8efde1f760a5ec5\transformed\navigation-common-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\78c98d357e56231e9d5a0320c13ef217\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\12994a8eb8f82ed7835f81514d3e835b\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\30dc3987959c50318c8e8a13c2cd979e\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\ac7197176d77e3766533029e3603291e\transformed\navigation-compose-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\95e99f18e5cbc62741261c807c9b2283\transformed\activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f388d2d4d1fceadc28a19904d5d06ae\transformed\activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3-android:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\20318a4763c515b8e8114db0afb41606\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e51ce302666170e2490c41c2e65fc97d\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ac764e35e1ce524faf1d65491bd463e5\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\26f3ca8bffc9a7f5b7b292213f4f3491\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e814dae375794f8d168818de00bb20f9\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7a56af73b15e252c2ac3b5bc69694ed2\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ca9379b59a8fb565fc8dc985d1c1a97e\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d5ea51721a57c0b4747d7c6dfcbd9dfb\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5376804ee5955a739ac9e6f131762078\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d4af8e497c0cbe0a25129a6ea63348ab\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ed4c2e483f771d1d57a45995ea2932d3\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\23eccfdc050a784e7d23c25aca5f71c6\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\72491647805cd53f3b8c86197258c751\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1d8c6e650e5dedf0c1d74487cf93d351\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa89a5b018fb9651ea5ad9c7b97cf220\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\518b52dba909a18e1f831b6737fb1a7a\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cbabb222178311213a775cb6b3442975\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0dab35ff3c85b96d4044ec0e200c394\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\7ec871574ab35886b691665cd139324d\transformed\activity-compose-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\de349cf698518a5b79e4de3dd50091f0\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a81dcae0be05ee6ff3df92dff9a71584\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\36bb74256eb4c67929f148de117d4b1e\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7022fdc86c848e8b18f24952de50c75\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\24cc38749a18090be21f81d4377ab5a8\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b7534520e94004cc35e3bd48cb1aba9\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d80b4a5813488ec7a1208e23fb5b3ca7\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\18cfd43c9a843935a6adda402716495c\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8dc7ab0e5e3d165b04df2d50d1b4bfac\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5b69302766103bfd8577326126146d60\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8d1003a7302aa7ecd8240b5f6e6bbd54\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5ed700988212d8d9085a7fcc8af113be\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ef7fe5fb234f41aa0335a1c51d8ab90f\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb41759101879028f49add9e86811a8d\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\58d78ea86540194b0aac1267880374b6\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\220fe53d0985685c78e74b1257a82542\transformed\core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d97f1401e006d0b312d3c1e09386873f\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c9a876891ed51e5bb39ac0d2b57451c\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1d7874c73573390f6d3f32317742f5cd\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\096fabe514c968a0e0b3ab49ddc3f73f\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\57661a5c647bb3ec7228085769f405ef\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3d5069c7e643d55295d8561939220d5c\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b91235adef012b688bf083b07565fbd2\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb13d695b764c243b13f513ebafe0ca3\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f35272bd16f27a48d8edc40d0765dd6f\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b3884271a0250a5344ebd41c0a611fa5\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a694a578cd6c11e83bcdf0271469a629\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b88c62551db371bdeeb3913d536fc888\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
	package
		INJECTED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:2:11-69
application
ADDED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:5:5-22:19
INJECTED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:5:5-22:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cbabb222178311213a775cb6b3442975\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cbabb222178311213a775cb6b3442975\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0dab35ff3c85b96d4044ec0e200c394\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0dab35ff3c85b96d4044ec0e200c394\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\de349cf698518a5b79e4de3dd50091f0\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\de349cf698518a5b79e4de3dd50091f0\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a81dcae0be05ee6ff3df92dff9a71584\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a81dcae0be05ee6ff3df92dff9a71584\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb41759101879028f49add9e86811a8d\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb41759101879028f49add9e86811a8d\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d97f1401e006d0b312d3c1e09386873f\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d97f1401e006d0b312d3c1e09386873f\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb13d695b764c243b13f513ebafe0ca3\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb13d695b764c243b13f513ebafe0ca3\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a694a578cd6c11e83bcdf0271469a629\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a694a578cd6c11e83bcdf0271469a629\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb41759101879028f49add9e86811a8d\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:9:9-35
	android:label
		ADDED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:8:9-41
	android:icon
		ADDED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:7:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:6:9-35
	android:theme
		ADDED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:10:9-49
activity#com.taskmanager.MainActivity
ADDED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:12:9-20:20
	android:exported
		ADDED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:14:13-36
	android:theme
		ADDED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:15:13-53
	android:name
		ADDED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:13:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:16:13-19:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:17:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:17:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:18:17-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:18:27-74
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa6771ee7182fe1eb8efde1f760a5ec5\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa6771ee7182fe1eb8efde1f760a5ec5\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\78c98d357e56231e9d5a0320c13ef217\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\78c98d357e56231e9d5a0320c13ef217\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\12994a8eb8f82ed7835f81514d3e835b\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\12994a8eb8f82ed7835f81514d3e835b\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\30dc3987959c50318c8e8a13c2cd979e\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\30dc3987959c50318c8e8a13c2cd979e\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\ac7197176d77e3766533029e3603291e\transformed\navigation-compose-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\ac7197176d77e3766533029e3603291e\transformed\navigation-compose-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\95e99f18e5cbc62741261c807c9b2283\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\95e99f18e5cbc62741261c807c9b2283\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f388d2d4d1fceadc28a19904d5d06ae\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f388d2d4d1fceadc28a19904d5d06ae\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\20318a4763c515b8e8114db0afb41606\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\20318a4763c515b8e8114db0afb41606\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e51ce302666170e2490c41c2e65fc97d\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e51ce302666170e2490c41c2e65fc97d\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ac764e35e1ce524faf1d65491bd463e5\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ac764e35e1ce524faf1d65491bd463e5\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\26f3ca8bffc9a7f5b7b292213f4f3491\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\26f3ca8bffc9a7f5b7b292213f4f3491\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e814dae375794f8d168818de00bb20f9\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e814dae375794f8d168818de00bb20f9\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7a56af73b15e252c2ac3b5bc69694ed2\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7a56af73b15e252c2ac3b5bc69694ed2\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ca9379b59a8fb565fc8dc985d1c1a97e\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ca9379b59a8fb565fc8dc985d1c1a97e\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d5ea51721a57c0b4747d7c6dfcbd9dfb\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d5ea51721a57c0b4747d7c6dfcbd9dfb\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5376804ee5955a739ac9e6f131762078\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5376804ee5955a739ac9e6f131762078\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d4af8e497c0cbe0a25129a6ea63348ab\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d4af8e497c0cbe0a25129a6ea63348ab\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ed4c2e483f771d1d57a45995ea2932d3\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ed4c2e483f771d1d57a45995ea2932d3\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\23eccfdc050a784e7d23c25aca5f71c6\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\23eccfdc050a784e7d23c25aca5f71c6\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\72491647805cd53f3b8c86197258c751\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\72491647805cd53f3b8c86197258c751\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1d8c6e650e5dedf0c1d74487cf93d351\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1d8c6e650e5dedf0c1d74487cf93d351\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa89a5b018fb9651ea5ad9c7b97cf220\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\fa89a5b018fb9651ea5ad9c7b97cf220\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\518b52dba909a18e1f831b6737fb1a7a\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\518b52dba909a18e1f831b6737fb1a7a\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cbabb222178311213a775cb6b3442975\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cbabb222178311213a775cb6b3442975\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0dab35ff3c85b96d4044ec0e200c394\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0dab35ff3c85b96d4044ec0e200c394\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\7ec871574ab35886b691665cd139324d\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\7ec871574ab35886b691665cd139324d\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\de349cf698518a5b79e4de3dd50091f0\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\de349cf698518a5b79e4de3dd50091f0\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a81dcae0be05ee6ff3df92dff9a71584\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a81dcae0be05ee6ff3df92dff9a71584\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\36bb74256eb4c67929f148de117d4b1e\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\36bb74256eb4c67929f148de117d4b1e\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7022fdc86c848e8b18f24952de50c75\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7022fdc86c848e8b18f24952de50c75\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\24cc38749a18090be21f81d4377ab5a8\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\24cc38749a18090be21f81d4377ab5a8\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b7534520e94004cc35e3bd48cb1aba9\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b7534520e94004cc35e3bd48cb1aba9\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d80b4a5813488ec7a1208e23fb5b3ca7\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d80b4a5813488ec7a1208e23fb5b3ca7\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\18cfd43c9a843935a6adda402716495c\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\18cfd43c9a843935a6adda402716495c\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8dc7ab0e5e3d165b04df2d50d1b4bfac\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8dc7ab0e5e3d165b04df2d50d1b4bfac\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5b69302766103bfd8577326126146d60\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5b69302766103bfd8577326126146d60\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8d1003a7302aa7ecd8240b5f6e6bbd54\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8d1003a7302aa7ecd8240b5f6e6bbd54\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5ed700988212d8d9085a7fcc8af113be\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5ed700988212d8d9085a7fcc8af113be\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ef7fe5fb234f41aa0335a1c51d8ab90f\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ef7fe5fb234f41aa0335a1c51d8ab90f\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb41759101879028f49add9e86811a8d\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb41759101879028f49add9e86811a8d\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\58d78ea86540194b0aac1267880374b6\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\58d78ea86540194b0aac1267880374b6\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\220fe53d0985685c78e74b1257a82542\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\220fe53d0985685c78e74b1257a82542\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d97f1401e006d0b312d3c1e09386873f\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d97f1401e006d0b312d3c1e09386873f\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c9a876891ed51e5bb39ac0d2b57451c\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c9a876891ed51e5bb39ac0d2b57451c\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1d7874c73573390f6d3f32317742f5cd\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1d7874c73573390f6d3f32317742f5cd\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\096fabe514c968a0e0b3ab49ddc3f73f\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\096fabe514c968a0e0b3ab49ddc3f73f\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\57661a5c647bb3ec7228085769f405ef\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\57661a5c647bb3ec7228085769f405ef\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3d5069c7e643d55295d8561939220d5c\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3d5069c7e643d55295d8561939220d5c\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b91235adef012b688bf083b07565fbd2\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b91235adef012b688bf083b07565fbd2\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb13d695b764c243b13f513ebafe0ca3\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb13d695b764c243b13f513ebafe0ca3\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f35272bd16f27a48d8edc40d0765dd6f\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f35272bd16f27a48d8edc40d0765dd6f\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b3884271a0250a5344ebd41c0a611fa5\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b3884271a0250a5344ebd41c0a611fa5\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a694a578cd6c11e83bcdf0271469a629\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a694a578cd6c11e83bcdf0271469a629\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b88c62551db371bdeeb3913d536fc888\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b88c62551db371bdeeb3913d536fc888\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cbabb222178311213a775cb6b3442975\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cbabb222178311213a775cb6b3442975\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cbabb222178311213a775cb6b3442975\transformed\ui-test-manifest-1.6.1\AndroidManifest.xml:24:13-63
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0dab35ff3c85b96d4044ec0e200c394\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0dab35ff3c85b96d4044ec0e200c394\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0dab35ff3c85b96d4044ec0e200c394\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\de349cf698518a5b79e4de3dd50091f0\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a81dcae0be05ee6ff3df92dff9a71584\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a81dcae0be05ee6ff3df92dff9a71584\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a694a578cd6c11e83bcdf0271469a629\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a694a578cd6c11e83bcdf0271469a629\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\de349cf698518a5b79e4de3dd50091f0\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\de349cf698518a5b79e4de3dd50091f0\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\de349cf698518a5b79e4de3dd50091f0\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\de349cf698518a5b79e4de3dd50091f0\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\de349cf698518a5b79e4de3dd50091f0\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\de349cf698518a5b79e4de3dd50091f0\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\de349cf698518a5b79e4de3dd50091f0\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a81dcae0be05ee6ff3df92dff9a71584\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a81dcae0be05ee6ff3df92dff9a71584\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a81dcae0be05ee6ff3df92dff9a71584\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb41759101879028f49add9e86811a8d\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb41759101879028f49add9e86811a8d\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb41759101879028f49add9e86811a8d\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.taskmanager.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb41759101879028f49add9e86811a8d\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb41759101879028f49add9e86811a8d\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb41759101879028f49add9e86811a8d\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb41759101879028f49add9e86811a8d\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb41759101879028f49add9e86811a8d\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.taskmanager.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb41759101879028f49add9e86811a8d\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb41759101879028f49add9e86811a8d\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d97f1401e006d0b312d3c1e09386873f\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d97f1401e006d0b312d3c1e09386873f\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d97f1401e006d0b312d3c1e09386873f\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d97f1401e006d0b312d3c1e09386873f\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d97f1401e006d0b312d3c1e09386873f\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ca161e2e9de73a7146387e3e7473164\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
