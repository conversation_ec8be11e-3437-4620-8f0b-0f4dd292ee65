-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:2:1-24:12
INJECTED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:2:1-24:12
INJECTED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:2:1-24:12
INJECTED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:2:1-24:12
MERGED from [androidx.compose.material3:material3:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2cf887c674b6fe54b07f45a262ba7369\transformed\material3-1.1.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\4d9ca52df9690b418cb87ebd2737bab4\transformed\activity-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\3737bd546e19d32296c61f6c799b2ae8\transformed\activity-ktx-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-ripple-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b49d47d8f6840986dfde78a31d41d400\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d0d427fa80122103b1fc1dab2c242d59\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fbe7aad602302da452bb8956aab7b57d\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9a9693fbe1d6a1c87bb53f5d83dc7180\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0d11a8a0f8421a0bc73c3941f91fd86a\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5cfe64396d557884d7b3dd90bc52ae13\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\66d34f668ae7b9665e9274931b5c8014\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c74d59dfff8033637c68f33993c17fb3\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a6732fbd39a9b62a1055796fc05a2390\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\789eeea9ad8a0f36c1a4365c36c24914\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a245aaab50e1ad0243af59e7a8159086\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e45ec252c26b3d3679ec78f542cf23cc\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\92ff1b001756687e992902facbc6bded\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe28169880c6b7bed26337e5321650dc\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d1809d2cc45575a204429bccbd1173e6\transformed\ui-test-manifest-1.5.0\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\41355d5d9f7032047d7b702f60b96447\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.activity:activity-compose:1.7.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\63af007b913783e7021a463bac6b408b\transformed\activity-compose-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c27dd2d2d266e151b930b964e47cb67\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7438b210de76eb60bbaf9a1168848bd9\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\94715fc28b87a2651d284a714dec6533\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e84e839e6aad51a26b803212332dad05\transformed\core-1.10.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d8e46d1e1b8b3f4c8c6a75b79667be2b\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\430a3653a787bc5286314775d6d8c585\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\238735d3e07ea2ba8e2243f8ec9ec55f\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e2c0284e75ef7083868aece982811acb\transformed\lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\142eb087cc88d530f10cb0e8eabfbea5\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1847d1dd7bfc61376df8d6b618500c51\transformed\lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\11e3015613825d96d065c95dda0d086a\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0002e3f4e1490ee9473429306b554e0\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9a1d63a29c4f62158660f7187af857ff\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\90a7d6a7dd098bf72cc5710f4521a75c\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4b6b1a040279954498051bcc861da0bf\transformed\core-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e65187ed9b61754df40db2a089b249f4\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\93112de3d41945f9f41cb1b43a592b50\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\337301a92c9afe82ae7ae25645d6f535\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\249274cd3a66ed5351111252442d4759\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe61387f3ba53d5b1ce08dc5be7aa3fb\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\94dc3a8ae9af2ad992a7a870a50bd378\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d06be7f65ce94d00ab916dfd7b7892a3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1cac4da37f992234f0e81455e67211d9\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
	package
		INJECTED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:2:11-69
application
ADDED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:5:5-22:19
INJECTED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:5:5-22:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d1809d2cc45575a204429bccbd1173e6\transformed\ui-test-manifest-1.5.0\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d1809d2cc45575a204429bccbd1173e6\transformed\ui-test-manifest-1.5.0\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\41355d5d9f7032047d7b702f60b96447\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\41355d5d9f7032047d7b702f60b96447\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\94715fc28b87a2651d284a714dec6533\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\94715fc28b87a2651d284a714dec6533\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e84e839e6aad51a26b803212332dad05\transformed\core-1.10.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e84e839e6aad51a26b803212332dad05\transformed\core-1.10.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\430a3653a787bc5286314775d6d8c585\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\430a3653a787bc5286314775d6d8c585\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe61387f3ba53d5b1ce08dc5be7aa3fb\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe61387f3ba53d5b1ce08dc5be7aa3fb\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d06be7f65ce94d00ab916dfd7b7892a3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d06be7f65ce94d00ab916dfd7b7892a3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e84e839e6aad51a26b803212332dad05\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:9:9-35
	android:label
		ADDED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:8:9-41
	android:icon
		ADDED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:7:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:6:9-35
	android:theme
		ADDED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:10:9-49
activity#com.taskmanager.MainActivity
ADDED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:12:9-20:20
	android:exported
		ADDED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:14:13-36
	android:theme
		ADDED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:15:13-53
	android:name
		ADDED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:13:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:16:13-19:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:17:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:17:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:18:17-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml:18:27-74
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml
MERGED from [androidx.compose.material3:material3:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2cf887c674b6fe54b07f45a262ba7369\transformed\material3-1.1.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2cf887c674b6fe54b07f45a262ba7369\transformed\material3-1.1.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\4d9ca52df9690b418cb87ebd2737bab4\transformed\activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\4d9ca52df9690b418cb87ebd2737bab4\transformed\activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\3737bd546e19d32296c61f6c799b2ae8\transformed\activity-ktx-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\3737bd546e19d32296c61f6c799b2ae8\transformed\activity-ktx-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b49d47d8f6840986dfde78a31d41d400\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b49d47d8f6840986dfde78a31d41d400\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d0d427fa80122103b1fc1dab2c242d59\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d0d427fa80122103b1fc1dab2c242d59\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fbe7aad602302da452bb8956aab7b57d\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fbe7aad602302da452bb8956aab7b57d\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9a9693fbe1d6a1c87bb53f5d83dc7180\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9a9693fbe1d6a1c87bb53f5d83dc7180\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0d11a8a0f8421a0bc73c3941f91fd86a\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0d11a8a0f8421a0bc73c3941f91fd86a\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5cfe64396d557884d7b3dd90bc52ae13\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5cfe64396d557884d7b3dd90bc52ae13\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\66d34f668ae7b9665e9274931b5c8014\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\66d34f668ae7b9665e9274931b5c8014\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c74d59dfff8033637c68f33993c17fb3\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c74d59dfff8033637c68f33993c17fb3\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a6732fbd39a9b62a1055796fc05a2390\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a6732fbd39a9b62a1055796fc05a2390\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\789eeea9ad8a0f36c1a4365c36c24914\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\789eeea9ad8a0f36c1a4365c36c24914\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a245aaab50e1ad0243af59e7a8159086\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a245aaab50e1ad0243af59e7a8159086\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e45ec252c26b3d3679ec78f542cf23cc\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e45ec252c26b3d3679ec78f542cf23cc\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\92ff1b001756687e992902facbc6bded\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\92ff1b001756687e992902facbc6bded\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe28169880c6b7bed26337e5321650dc\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe28169880c6b7bed26337e5321650dc\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d1809d2cc45575a204429bccbd1173e6\transformed\ui-test-manifest-1.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d1809d2cc45575a204429bccbd1173e6\transformed\ui-test-manifest-1.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\41355d5d9f7032047d7b702f60b96447\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\41355d5d9f7032047d7b702f60b96447\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.7.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\63af007b913783e7021a463bac6b408b\transformed\activity-compose-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.7.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\63af007b913783e7021a463bac6b408b\transformed\activity-compose-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c27dd2d2d266e151b930b964e47cb67\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c27dd2d2d266e151b930b964e47cb67\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7438b210de76eb60bbaf9a1168848bd9\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7438b210de76eb60bbaf9a1168848bd9\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\94715fc28b87a2651d284a714dec6533\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\94715fc28b87a2651d284a714dec6533\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e84e839e6aad51a26b803212332dad05\transformed\core-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e84e839e6aad51a26b803212332dad05\transformed\core-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d8e46d1e1b8b3f4c8c6a75b79667be2b\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d8e46d1e1b8b3f4c8c6a75b79667be2b\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\430a3653a787bc5286314775d6d8c585\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\430a3653a787bc5286314775d6d8c585\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\238735d3e07ea2ba8e2243f8ec9ec55f\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\238735d3e07ea2ba8e2243f8ec9ec55f\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e2c0284e75ef7083868aece982811acb\transformed\lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e2c0284e75ef7083868aece982811acb\transformed\lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\142eb087cc88d530f10cb0e8eabfbea5\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\142eb087cc88d530f10cb0e8eabfbea5\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1847d1dd7bfc61376df8d6b618500c51\transformed\lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1847d1dd7bfc61376df8d6b618500c51\transformed\lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\11e3015613825d96d065c95dda0d086a\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\11e3015613825d96d065c95dda0d086a\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0002e3f4e1490ee9473429306b554e0\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0002e3f4e1490ee9473429306b554e0\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9a1d63a29c4f62158660f7187af857ff\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9a1d63a29c4f62158660f7187af857ff\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\90a7d6a7dd098bf72cc5710f4521a75c\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\90a7d6a7dd098bf72cc5710f4521a75c\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4b6b1a040279954498051bcc861da0bf\transformed\core-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4b6b1a040279954498051bcc861da0bf\transformed\core-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e65187ed9b61754df40db2a089b249f4\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e65187ed9b61754df40db2a089b249f4\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\93112de3d41945f9f41cb1b43a592b50\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\93112de3d41945f9f41cb1b43a592b50\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\337301a92c9afe82ae7ae25645d6f535\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\337301a92c9afe82ae7ae25645d6f535\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\249274cd3a66ed5351111252442d4759\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\249274cd3a66ed5351111252442d4759\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe61387f3ba53d5b1ce08dc5be7aa3fb\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe61387f3ba53d5b1ce08dc5be7aa3fb\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\94dc3a8ae9af2ad992a7a870a50bd378\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\94dc3a8ae9af2ad992a7a870a50bd378\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d06be7f65ce94d00ab916dfd7b7892a3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d06be7f65ce94d00ab916dfd7b7892a3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1cac4da37f992234f0e81455e67211d9\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1cac4da37f992234f0e81455e67211d9\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\schedule\task-manager\android-app\app\src\main\AndroidManifest.xml
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d1809d2cc45575a204429bccbd1173e6\transformed\ui-test-manifest-1.5.0\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d1809d2cc45575a204429bccbd1173e6\transformed\ui-test-manifest-1.5.0\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d1809d2cc45575a204429bccbd1173e6\transformed\ui-test-manifest-1.5.0\AndroidManifest.xml:24:13-63
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\41355d5d9f7032047d7b702f60b96447\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\41355d5d9f7032047d7b702f60b96447\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\41355d5d9f7032047d7b702f60b96447\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\94715fc28b87a2651d284a714dec6533\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\430a3653a787bc5286314775d6d8c585\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\430a3653a787bc5286314775d6d8c585\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d06be7f65ce94d00ab916dfd7b7892a3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d06be7f65ce94d00ab916dfd7b7892a3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\94715fc28b87a2651d284a714dec6533\transformed\emoji2-1.4.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\94715fc28b87a2651d284a714dec6533\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\94715fc28b87a2651d284a714dec6533\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\94715fc28b87a2651d284a714dec6533\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\94715fc28b87a2651d284a714dec6533\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\94715fc28b87a2651d284a714dec6533\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\94715fc28b87a2651d284a714dec6533\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e84e839e6aad51a26b803212332dad05\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e84e839e6aad51a26b803212332dad05\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e84e839e6aad51a26b803212332dad05\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
permission#com.taskmanager.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e84e839e6aad51a26b803212332dad05\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e84e839e6aad51a26b803212332dad05\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e84e839e6aad51a26b803212332dad05\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e84e839e6aad51a26b803212332dad05\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e84e839e6aad51a26b803212332dad05\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
uses-permission#com.taskmanager.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e84e839e6aad51a26b803212332dad05\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e84e839e6aad51a26b803212332dad05\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\430a3653a787bc5286314775d6d8c585\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\430a3653a787bc5286314775d6d8c585\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\430a3653a787bc5286314775d6d8c585\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b69a0a84dc950e452c182bd9ed200045\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
