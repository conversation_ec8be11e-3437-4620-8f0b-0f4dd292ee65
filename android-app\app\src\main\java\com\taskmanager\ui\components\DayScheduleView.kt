package com.taskmanager.ui.components

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.taskmanager.data.model.Task
import com.taskmanager.ui.theme.*
import java.time.LocalTime
import java.time.format.DateTimeFormatter

@Composable
fun DayScheduleView(
    tasks: List<Task>,
    onTaskClick: (Task) -> Unit,
    modifier: Modifier = Modifier,
    themeSettings: com.taskmanager.data.model.ThemeSettings? = null
) {
    val hours = (0..23).toList()
    val sortedTasks = tasks.sortedBy { it.startTime }
    
    LazyColumn(
        modifier = modifier.fillMaxSize(),
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(0.dp)
    ) {
        items(hours) { hour ->
            HourSlot(
                hour = hour,
                tasks = sortedTasks.filter { task ->
                    task.startTime.hour == hour ||
                    (task.startTime.hour < hour && task.endTime.hour > hour) ||
                    (task.startTime.hour < hour && task.endTime.hour == hour && task.endTime.minute > 0)
                },
                themeSettings = themeSettings,
                onTaskClick = onTaskClick
            )
        }
    }
}

@Composable
private fun HourSlot(
    hour: Int,
    tasks: List<Task>,
    themeSettings: com.taskmanager.data.model.ThemeSettings? = null,
    onTaskClick: (Task) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .height(60.dp)
    ) {
        // 时间标签
        Box(
            modifier = Modifier
                .width(60.dp)
                .fillMaxHeight(),
            contentAlignment = Alignment.TopStart
        ) {
            Text(
                text = String.format("%02d:00", hour),
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(top = 4.dp)
            )
        }
        
        // 时间线
        Box(
            modifier = Modifier
                .width(1.dp)
                .fillMaxHeight()
                .background(MaterialTheme.colorScheme.outline.copy(alpha = 0.3f))
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        // 任务区域
        Box(
            modifier = Modifier
                .weight(1f)
                .fillMaxHeight()
        ) {
            // 背景网格线
            Canvas(
                modifier = Modifier.fillMaxSize()
            ) {
                val lineColor = Color.Gray.copy(alpha = 0.2f)
                // 绘制水平线
                drawLine(
                    color = lineColor,
                    start = Offset(0f, 0f),
                    end = Offset(size.width, 0f),
                    strokeWidth = 1.dp.toPx()
                )
            }
            
            // 任务卡片
            tasks.forEach { task ->
                TaskTimeSlot(
                    task = task,
                    hour = hour,
                    themeSettings = themeSettings,
                    onClick = { onTaskClick(task) }
                )
            }
        }
    }
}

@Composable
private fun TaskTimeSlot(
    task: Task,
    hour: Int,
    themeSettings: com.taskmanager.data.model.ThemeSettings? = null,
    onClick: () -> Unit
) {
    val startHour = task.startTime.hour
    val startMinute = task.startTime.minute
    val endHour = task.endTime.hour
    val endMinute = task.endTime.minute
    
    // 计算在当前小时槽中的位置和高度
    val slotStartMinute = if (startHour == hour) startMinute else 0
    val slotEndMinute = if (endHour == hour) endMinute else 60
    
    // 如果任务不在当前小时槽中，不显示
    if (slotEndMinute <= slotStartMinute && !(startHour < hour && endHour > hour)) {
        return
    }
    
    val topOffsetValue = (slotStartMinute / 60f) * 60f
    val heightValue = ((slotEndMinute - slotStartMinute) / 60f) * 60f
    val topOffset = topOffsetValue.dp
    val taskHeight = heightValue.dp

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(taskHeight)
            .offset(y = topOffset)
            .padding(end = 4.dp)
            .clickable { onClick() },
        colors = CardDefaults.cardColors(
            containerColor = (themeSettings?.getCategoryColor(task.category)
                ?: getCategoryColor(task.category)).copy(alpha = 0.8f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(4.dp),
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = task.title,
                style = MaterialTheme.typography.bodySmall,
                fontWeight = FontWeight.Medium,
                color = themeSettings?.onTaskTextColorCompose ?: Color.White,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
            
            if (startHour == hour) {
                Text(
                    text = task.timeDisplay,
                    style = MaterialTheme.typography.labelSmall,
                    color = (themeSettings?.onTaskTextColorCompose ?: Color.White).copy(alpha = 0.9f),
                    maxLines = 1
                )
            }
        }
    }
}

private fun getCategoryColor(category: com.taskmanager.data.model.TaskCategory): Color {
    return when (category) {
        com.taskmanager.data.model.TaskCategory.WORK -> WorkCategoryColor
        com.taskmanager.data.model.TaskCategory.PERSONAL -> PersonalCategoryColor
        com.taskmanager.data.model.TaskCategory.STUDY -> StudyCategoryColor
        com.taskmanager.data.model.TaskCategory.HEALTH -> HealthCategoryColor
        com.taskmanager.data.model.TaskCategory.OTHER -> OtherCategoryColor
    }
}
