package com.taskmanager.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000B\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\u001aV\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\u0012\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010\b2\b\b\u0002\u0010\n\u001a\u00020\u000b2\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\rH\u0007\u001a\u0015\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0011H\u0002\u00a2\u0006\u0002\u0010\u0012\u001a\u0015\u0010\u0013\u001a\u00020\u000f2\u0006\u0010\u0014\u001a\u00020\u0015H\u0002\u00a2\u0006\u0002\u0010\u0016\u00a8\u0006\u0017"}, d2 = {"TaskCard", "", "task", "Lcom/taskmanager/data/model/Task;", "onEdit", "Lkotlin/Function0;", "onDelete", "onToggleComplete", "Lkotlin/Function1;", "", "modifier", "Landroidx/compose/ui/Modifier;", "themeSettings", "Lcom/taskmanager/data/model/ThemeSettings;", "getCategoryColor", "Landroidx/compose/ui/graphics/Color;", "category", "Lcom/taskmanager/data/model/TaskCategory;", "(Lcom/taskmanager/data/model/TaskCategory;)J", "getPriorityColor", "priority", "Lcom/taskmanager/data/model/TaskPriority;", "(Lcom/taskmanager/data/model/TaskPriority;)J", "app_debug"})
public final class TaskCardKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void TaskCard(@org.jetbrains.annotations.NotNull()
    com.taskmanager.data.model.Task task, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onEdit, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDelete, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onToggleComplete, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.Nullable()
    com.taskmanager.data.model.ThemeSettings themeSettings) {
    }
    
    private static final long getPriorityColor(com.taskmanager.data.model.TaskPriority priority) {
        return 0L;
    }
    
    private static final long getCategoryColor(com.taskmanager.data.model.TaskCategory category) {
        return 0L;
    }
}