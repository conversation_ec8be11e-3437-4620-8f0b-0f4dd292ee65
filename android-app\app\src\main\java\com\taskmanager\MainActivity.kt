package com.taskmanager

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CalendarToday
import androidx.compose.material.icons.filled.CalendarViewWeek
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.navigation.NavDestination.Companion.hierarchy
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.taskmanager.ui.screens.DayViewScreen
import com.taskmanager.ui.screens.ThemeSettingsScreen
import com.taskmanager.ui.screens.WeekViewScreen
import com.taskmanager.ui.theme.GlobalTheme
import com.taskmanager.ui.theme.ProvideCustomTheme
import com.taskmanager.ui.theme.TaskManagerTheme

sealed class Screen(val route: String, val title: String, val icon: ImageVector) {
    object DayView : Screen("day_view", "日视图", Icons.Default.CalendarToday)
    object WeekView : Screen("week_view", "周视图", Icons.Default.CalendarViewWeek)
    object ThemeSettings : Screen("theme_settings", "主题设置", Icons.Default.Settings)
}

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 初始化主题管理器
        GlobalTheme.initialize(this)

        setContent {
            TaskManagerTheme {
                ProvideCustomTheme(GlobalTheme.getThemeManager()) { currentTheme ->
                    TaskManagerApp(currentTheme)
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TaskManagerApp(currentTheme: com.taskmanager.data.model.ThemeSettings) {
    val navController = rememberNavController()
    val items = listOf(Screen.DayView, Screen.WeekView, Screen.ThemeSettings)

    Scaffold(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(currentTheme.gradientColors)
            ),
        bottomBar = {
            NavigationBar {
                val navBackStackEntry by navController.currentBackStackEntryAsState()
                val currentDestination = navBackStackEntry?.destination

                items.forEach { screen ->
                    NavigationBarItem(
                        icon = { Icon(screen.icon, contentDescription = screen.title) },
                        label = { Text(screen.title) },
                        selected = currentDestination?.hierarchy?.any { it.route == screen.route } == true,
                        onClick = {
                            navController.navigate(screen.route) {
                                popUpTo(navController.graph.findStartDestination().id) {
                                    saveState = true
                                }
                                launchSingleTop = true
                                restoreState = true
                            }
                        }
                    )
                }
            }
        }
    ) { innerPadding ->
        NavHost(
            navController = navController,
            startDestination = Screen.DayView.route,
            modifier = Modifier.padding(innerPadding)
        ) {
            composable(Screen.DayView.route) {
                DayViewScreen()
            }
            composable(Screen.WeekView.route) {
                WeekViewScreen()
            }
            composable(Screen.ThemeSettings.route) {
                ThemeSettingsScreen(
                    themeManager = GlobalTheme.getThemeManager(),
                    onBackClick = {
                        navController.popBackStack()
                    }
                )
            }
        }
    }
}

@androidx.compose.ui.tooling.preview.Preview(showBackground = true)
@Composable
fun TaskManagerAppPreview() {
    TaskManagerTheme {
        TaskManagerApp(com.taskmanager.data.model.PresetThemes.defaultTheme)
    }
}
