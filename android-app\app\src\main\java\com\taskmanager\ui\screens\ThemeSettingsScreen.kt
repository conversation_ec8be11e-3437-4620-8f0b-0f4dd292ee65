package com.taskmanager.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Palette
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.taskmanager.data.model.PresetThemes
import com.taskmanager.data.model.TaskCategory
import com.taskmanager.data.model.TaskPriority
import com.taskmanager.data.model.ThemeSettings
import com.taskmanager.ui.components.ColorPickerDialog
import com.taskmanager.ui.theme.ThemeManager

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ThemeSettingsScreen(
    themeManager: ThemeManager,
    onBackClick: () -> Unit
) {
    val currentTheme by themeManager.currentTheme.collectAsState()
    
    // 颜色选择器状态
    var showColorPicker by remember { mutableStateOf(false) }
    var colorPickerTitle by remember { mutableStateOf("") }
    var colorPickerInitialColor by remember { mutableStateOf(Color.Blue) }
    var colorPickerCallback by remember { mutableStateOf<(Color) -> Unit>({}) }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(currentTheme.gradientColors)
            )
    ) {
        // 顶部栏
        TopAppBar(
            title = { Text("主题设置") },
            navigationIcon = {
                IconButton(onClick = onBackClick) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = Color.Transparent
            )
        )
        
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 预设主题
            item {
                PresetThemeSection(
                    currentTheme = currentTheme,
                    onThemeSelected = { themeManager.applyPresetTheme(it) }
                )
            }
            
            // 背景颜色设置
            item {
                BackgroundColorSection(
                    currentTheme = currentTheme,
                    onStartColorClick = {
                        colorPickerTitle = "选择背景起始颜色"
                        colorPickerInitialColor = currentTheme.backgroundStartColorCompose
                        colorPickerCallback = { color ->
                            themeManager.updateBackgroundColors(color, currentTheme.backgroundEndColorCompose)
                        }
                        showColorPicker = true
                    },
                    onEndColorClick = {
                        colorPickerTitle = "选择背景结束颜色"
                        colorPickerInitialColor = currentTheme.backgroundEndColorCompose
                        colorPickerCallback = { color ->
                            themeManager.updateBackgroundColors(currentTheme.backgroundStartColorCompose, color)
                        }
                        showColorPicker = true
                    }
                )
            }
            
            // 任务分类颜色设置
            item {
                CategoryColorSection(
                    currentTheme = currentTheme,
                    onCategoryColorClick = { category, color ->
                        colorPickerTitle = "选择${category.displayName}分类颜色"
                        colorPickerInitialColor = color
                        colorPickerCallback = { newColor ->
                            themeManager.updateCategoryColor(category, newColor)
                        }
                        showColorPicker = true
                    }
                )
            }
            
            // 优先级颜色设置
            item {
                PriorityColorSection(
                    currentTheme = currentTheme,
                    onPriorityColorClick = { priority, color ->
                        colorPickerTitle = "选择${priority.displayName}优先级颜色"
                        colorPickerInitialColor = color
                        colorPickerCallback = { newColor ->
                            themeManager.updatePriorityColor(priority, newColor)
                        }
                        showColorPicker = true
                    }
                )
            }
            
            // 重置按钮
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Button(
                            onClick = { themeManager.resetToDefault() },
                            modifier = Modifier.fillMaxWidth(),
                            colors = ButtonDefaults.buttonColors(
                                containerColor = MaterialTheme.colorScheme.error
                            )
                        ) {
                            Text("重置为默认主题")
                        }
                    }
                }
            }
        }
    }
    
    // 颜色选择器对话框
    if (showColorPicker) {
        ColorPickerDialog(
            title = colorPickerTitle,
            initialColor = colorPickerInitialColor,
            onDismiss = { showColorPicker = false },
            onColorSelected = { color ->
                colorPickerCallback(color)
                showColorPicker = false
            }
        )
    }
}

@Composable
private fun PresetThemeSection(
    currentTheme: ThemeSettings,
    onThemeSelected: (ThemeSettings) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "预设主题",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            val presetThemes = listOf(
                "默认" to PresetThemes.defaultTheme,
                "深色" to PresetThemes.darkTheme,
                "绿色" to PresetThemes.greenTheme,
                "紫色" to PresetThemes.purpleTheme,
                "橙色" to PresetThemes.orangeTheme
            )
            
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                items(presetThemes) { (name, theme) ->
                    PresetThemeItem(
                        name = name,
                        theme = theme,
                        isSelected = theme.backgroundStartColor == currentTheme.backgroundStartColor,
                        onClick = { onThemeSelected(theme) }
                    )
                }
            }
        }
    }
}

@Composable
private fun PresetThemeItem(
    name: String,
    theme: ThemeSettings,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.clickable { onClick() }
    ) {
        Box(
            modifier = Modifier
                .size(60.dp)
                .clip(CircleShape)
                .background(
                    brush = Brush.verticalGradient(theme.gradientColors)
                )
                .border(
                    width = if (isSelected) 3.dp else 1.dp,
                    color = if (isSelected) MaterialTheme.colorScheme.primary else Color.Gray,
                    shape = CircleShape
                )
        )

        Text(
            text = name,
            style = MaterialTheme.typography.bodySmall,
            modifier = Modifier.padding(top = 4.dp)
        )
    }
}

@Composable
private fun BackgroundColorSection(
    currentTheme: ThemeSettings,
    onStartColorClick: () -> Unit,
    onEndColorClick: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "背景颜色",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 12.dp)
            )

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                ColorSettingItem(
                    title = "起始颜色",
                    color = currentTheme.backgroundStartColorCompose,
                    onClick = onStartColorClick,
                    modifier = Modifier.weight(1f)
                )

                ColorSettingItem(
                    title = "结束颜色",
                    color = currentTheme.backgroundEndColorCompose,
                    onClick = onEndColorClick,
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }
}

@Composable
private fun CategoryColorSection(
    currentTheme: ThemeSettings,
    onCategoryColorClick: (TaskCategory, Color) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "任务分类颜色",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 12.dp)
            )

            TaskCategory.values().forEach { category ->
                val color = currentTheme.getCategoryColor(category)
                ColorSettingItem(
                    title = category.displayName,
                    color = color,
                    onClick = { onCategoryColorClick(category, color) },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 4.dp)
                )
            }
        }
    }
}

@Composable
private fun PriorityColorSection(
    currentTheme: ThemeSettings,
    onPriorityColorClick: (TaskPriority, Color) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "优先级颜色",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 12.dp)
            )

            TaskPriority.values().forEach { priority ->
                val color = currentTheme.getPriorityColor(priority)
                ColorSettingItem(
                    title = priority.displayName,
                    color = color,
                    onClick = { onPriorityColorClick(priority, color) },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 4.dp)
                )
            }
        }
    }
}

@Composable
private fun ColorSettingItem(
    title: String,
    color: Color,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .clip(RoundedCornerShape(8.dp))
            .clickable { onClick() }
            .padding(8.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.bodyMedium
        )

        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Box(
                modifier = Modifier
                    .size(24.dp)
                    .clip(CircleShape)
                    .background(color)
                    .border(1.dp, Color.Gray, CircleShape)
            )

            Icon(
                imageVector = Icons.Default.Palette,
                contentDescription = "选择颜色",
                modifier = Modifier
                    .padding(start = 8.dp)
                    .size(16.dp),
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}
