{"logs": [{"outputFile": "com.taskmanager.app-mergeDebugResources-42:/values-be/values-be.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1c27dd2d2d266e151b930b964e47cb67\\transformed\\ui-release\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,376,479,565,645,734,822,904,975,1045,1128,1215,1287,1372,1442", "endColumns": "92,83,93,102,85,79,88,87,81,70,69,82,86,71,84,69,122", "endOffsets": "193,277,371,474,560,640,729,817,899,970,1040,1123,1210,1282,1367,1437,1560"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1288,1381,5145,5239,5423,5587,5667,5756,5844,5926,5997,6067,6150,6319,6671,6756,6826", "endColumns": "92,83,93,102,85,79,88,87,81,70,69,82,86,71,84,69,122", "endOffsets": "1376,1460,5234,5337,5504,5662,5751,5839,5921,5992,6062,6145,6232,6386,6751,6821,6944"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2cf887c674b6fe54b07f45a262ba7369\\transformed\\material3-1.1.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,279,394,507,584,677,788,907,1018,1159,1239,1346,1435,1528,1638,1757,1863,2006,2148,2282,2455,2583,2704,2830,2949,3039,3133,3255,3384,3479,3589,3694,3839,3988,4092,4187,4268,4346,4428,4511,4607,4690,4773,4869,4971,5063,5157,5242,5346,5438,5533,5675,5761,5876", "endColumns": "112,110,114,112,76,92,110,118,110,140,79,106,88,92,109,118,105,142,141,133,172,127,120,125,118,89,93,121,128,94,109,104,144,148,103,94,80,77,81,82,95,82,82,95,101,91,93,84,103,91,94,141,85,114,91", "endOffsets": "163,274,389,502,579,672,783,902,1013,1154,1234,1341,1430,1523,1633,1752,1858,2001,2143,2277,2450,2578,2699,2825,2944,3034,3128,3250,3379,3474,3584,3689,3834,3983,4087,4182,4263,4341,4423,4506,4602,4685,4768,4864,4966,5058,5152,5237,5341,5433,5528,5670,5756,5871,5963"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,329,444,1465,1542,1635,1746,1865,1976,2117,2197,2304,2393,2486,2596,2715,2821,2964,3106,3240,3413,3541,3662,3788,3907,3997,4091,4213,4342,4437,4547,4652,4797,4946,5050,5342,5509,6237,6391,6575,6949,7032,7115,7211,7313,7405,7499,7584,7688,7780,7875,8017,8103,8218", "endColumns": "112,110,114,112,76,92,110,118,110,140,79,106,88,92,109,118,105,142,141,133,172,127,120,125,118,89,93,121,128,94,109,104,144,148,103,94,80,77,81,82,95,82,82,95,101,91,93,84,103,91,94,141,85,114,91", "endOffsets": "213,324,439,552,1537,1630,1741,1860,1971,2112,2192,2299,2388,2481,2591,2710,2816,2959,3101,3235,3408,3536,3657,3783,3902,3992,4086,4208,4337,4432,4542,4647,4792,4941,5045,5140,5418,5582,6314,6469,6666,7027,7110,7206,7308,7400,7494,7579,7683,7775,7870,8012,8098,8213,8305"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e84e839e6aad51a26b803212332dad05\\transformed\\core-1.10.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,562,665,786", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "148,250,350,451,557,660,781,882"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "557,655,757,857,958,1064,1167,6474", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "650,752,852,953,1059,1162,1283,6570"}}]}]}