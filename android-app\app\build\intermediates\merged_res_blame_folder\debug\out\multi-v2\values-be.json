{"logs": [{"outputFile": "com.taskmanager.app-mergeDebugResources-49:/values-be/values-be.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7e8a26d2d8a7fd613a757452c477b1bb\\transformed\\material3-release\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,287,406,523,621,718,832,955,1070,1215,1299,1410,1503,1600,1714,1837,1953,2100,2246,2384,2561,2693,2818,2947,3069,3163,3261,3387,3520,3619,3730,3839,3989,4142,4250,4350,4435,4530,4626,4712,4799,4899,4986,5073,5173,5279,5375,5473,5562,5670,5766,5866,6012,6102,6220", "endColumns": "116,114,118,116,97,96,113,122,114,144,83,110,92,96,113,122,115,146,145,137,176,131,124,128,121,93,97,125,132,98,110,108,149,152,107,99,84,94,95,85,86,99,86,86,99,105,95,97,88,107,95,99,145,89,117,95", "endOffsets": "167,282,401,518,616,713,827,950,1065,1210,1294,1405,1498,1595,1709,1832,1948,2095,2241,2379,2556,2688,2813,2942,3064,3158,3256,3382,3515,3614,3725,3834,3984,4137,4245,4345,4430,4525,4621,4707,4794,4894,4981,5068,5168,5274,5370,5468,5557,5665,5761,5861,6007,6097,6215,6311"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1465,1582,1697,1816,1933,2031,2128,2242,2365,2480,2625,2709,2820,2913,3010,3124,3247,3363,3510,3656,3794,3971,4103,4228,4357,4479,4573,4671,4797,4930,5029,5140,5249,5399,5552,5660,5760,5845,5940,6036,6122,6209,6309,6396,6483,6583,6689,6785,6883,6972,7080,7176,7276,7422,7512,7630", "endColumns": "116,114,118,116,97,96,113,122,114,144,83,110,92,96,113,122,115,146,145,137,176,131,124,128,121,93,97,125,132,98,110,108,149,152,107,99,84,94,95,85,86,99,86,86,99,105,95,97,88,107,95,99,145,89,117,95", "endOffsets": "1577,1692,1811,1928,2026,2123,2237,2360,2475,2620,2704,2815,2908,3005,3119,3242,3358,3505,3651,3789,3966,4098,4223,4352,4474,4568,4666,4792,4925,5024,5135,5244,5394,5547,5655,5755,5840,5935,6031,6117,6204,6304,6391,6478,6578,6684,6780,6878,6967,7075,7171,7271,7417,7507,7625,7721"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2094e2c8f8f000297a612ce5427e3eff\\transformed\\ui-release\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,376,479,565,645,734,822,904,975,1045,1128,1215,1287,1372,1442", "endColumns": "92,83,93,102,85,79,88,87,81,70,69,82,86,71,84,69,122", "endOffsets": "193,277,371,474,560,640,729,817,899,970,1040,1123,1210,1282,1367,1437,1560"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "836,929,1013,1107,1210,1296,1376,7726,7814,7896,7967,8037,8120,8207,8380,8465,8535", "endColumns": "92,83,93,102,85,79,88,87,81,70,69,82,86,71,84,69,122", "endOffsets": "924,1008,1102,1205,1291,1371,1460,7809,7891,7962,8032,8115,8202,8274,8460,8530,8653"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4f2cb26be0b26f5fa07889a18e1f2782\\transformed\\core-1.12.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,562,665,786", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "148,250,350,451,557,660,781,882"}, "to": {"startLines": "2,3,4,5,6,7,8,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,405,506,612,715,8279", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "198,300,400,501,607,710,831,8375"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\dc5a69b4df93eea844116bde22da938c\\transformed\\foundation-release\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,102", "endOffsets": "137,240"}, "to": {"startLines": "83,84", "startColumns": "4,4", "startOffsets": "8658,8745", "endColumns": "86,102", "endOffsets": "8740,8843"}}]}]}