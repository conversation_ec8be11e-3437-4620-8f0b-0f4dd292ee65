// 任务管理应用主要逻辑
class TaskManager {
    constructor() {
        this.tasks = this.loadTasks();
        this.currentView = 'day';
        this.currentDate = new Date();
        this.editingTask = null;
        this.copyingDate = null;
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.updateDisplay();
        this.generateTimeline();
    }

    // 绑定事件监听器
    bindEvents() {
        // 视图切换
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchView(e.target.dataset.view);
            });
        });

        // 日期导航
        document.getElementById('prevBtn').addEventListener('click', () => this.navigateDate(-1));
        document.getElementById('nextBtn').addEventListener('click', () => this.navigateDate(1));
        document.getElementById('todayBtn').addEventListener('click', () => this.goToToday());

        // 添加任务
        document.getElementById('addTaskBtn').addEventListener('click', () => this.openTaskModal());
        document.getElementById('addWeekTaskBtn').addEventListener('click', () => this.openTaskModal());

        // 复制任务
        document.getElementById('copyDayBtn').addEventListener('click', () => this.openCopyModal());

        // 模态框控制
        document.getElementById('closeModal').addEventListener('click', () => this.closeTaskModal());
        document.getElementById('closeCopyModal').addEventListener('click', () => this.closeCopyModal());
        document.getElementById('cancelBtn').addEventListener('click', () => this.closeTaskModal());
        document.getElementById('cancelCopyBtn').addEventListener('click', () => this.closeCopyModal());

        // 表单提交
        document.getElementById('taskForm').addEventListener('submit', (e) => this.handleTaskSubmit(e));
        document.getElementById('confirmCopyBtn').addEventListener('click', () => this.confirmCopy());

        // 点击模态框外部关闭
        document.getElementById('taskModal').addEventListener('click', (e) => {
            if (e.target.id === 'taskModal') this.closeTaskModal();
        });
        document.getElementById('copyModal').addEventListener('click', (e) => {
            if (e.target.id === 'copyModal') this.closeCopyModal();
        });
    }

    // 切换视图
    switchView(view) {
        this.currentView = view;
        
        // 更新按钮状态
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.view === view);
        });

        // 更新视图容器
        document.querySelectorAll('.view-container').forEach(container => {
            container.classList.toggle('active', container.id === `${view}View`);
        });

        this.updateDisplay();
    }

    // 日期导航
    navigateDate(direction) {
        if (this.currentView === 'day') {
            this.currentDate.setDate(this.currentDate.getDate() + direction);
        } else {
            this.currentDate.setDate(this.currentDate.getDate() + (direction * 7));
        }
        this.updateDisplay();
    }

    goToToday() {
        this.currentDate = new Date();
        this.updateDisplay();
    }

    // 更新显示
    updateDisplay() {
        this.updateDateDisplay();
        if (this.currentView === 'day') {
            this.renderDayView();
        } else {
            this.renderWeekView();
        }
    }

    updateDateDisplay() {
        const options = { 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric',
            weekday: 'long'
        };
        
        if (this.currentView === 'day') {
            document.getElementById('currentDate').textContent = 
                this.currentDate.toLocaleDateString('zh-CN', options);
            document.getElementById('dayTitle').textContent = 
                this.currentDate.toLocaleDateString('zh-CN', options);
        } else {
            const weekStart = this.getWeekStart(this.currentDate);
            const weekEnd = new Date(weekStart);
            weekEnd.setDate(weekEnd.getDate() + 6);
            
            document.getElementById('currentDate').textContent = 
                `${weekStart.toLocaleDateString('zh-CN', { month: 'long', day: 'numeric' })} - ${weekEnd.toLocaleDateString('zh-CN', { month: 'long', day: 'numeric' })}`;
            document.getElementById('weekTitle').textContent = 
                `${weekStart.toLocaleDateString('zh-CN', { month: 'long', day: 'numeric' })} - ${weekEnd.toLocaleDateString('zh-CN', { month: 'long', day: 'numeric' })}`;
        }
    }

    // 生成时间轴
    generateTimeline() {
        const timeline = document.getElementById('dayTimeline');
        timeline.innerHTML = '';

        for (let hour = 0; hour < 24; hour++) {
            const timeSlot = document.createElement('div');
            timeSlot.className = 'time-slot';
            timeSlot.innerHTML = `
                <div class="time-label">${hour.toString().padStart(2, '0')}:00</div>
                <div class="time-content" data-hour="${hour}"></div>
            `;
            timeline.appendChild(timeSlot);
        }
    }

    // 渲染日视图
    renderDayView() {
        const dateStr = this.formatDate(this.currentDate);
        const dayTasks = this.tasks.filter(task => task.date === dateStr);
        
        // 清空所有时间槽
        document.querySelectorAll('.time-content').forEach(content => {
            content.innerHTML = '';
        });

        // 按时间排序任务
        dayTasks.sort((a, b) => a.time.localeCompare(b.time));

        // 渲染任务
        dayTasks.forEach(task => {
            const hour = parseInt(task.time.split(':')[0]);
            const timeContent = document.querySelector(`[data-hour="${hour}"]`);
            if (timeContent) {
                const taskElement = this.createTaskElement(task);
                timeContent.appendChild(taskElement);
            }
        });
    }

    // 渲染周视图
    renderWeekView() {
        const weekGrid = document.getElementById('weekGrid');
        weekGrid.innerHTML = '';

        const weekStart = this.getWeekStart(this.currentDate);
        const today = new Date();
        const todayStr = this.formatDate(today);

        for (let i = 0; i < 7; i++) {
            const date = new Date(weekStart);
            date.setDate(date.getDate() + i);
            const dateStr = this.formatDate(date);
            const isToday = dateStr === todayStr;

            const dayElement = document.createElement('div');
            dayElement.className = `week-day ${isToday ? 'today' : ''}`;
            
            const dayTasks = this.tasks.filter(task => task.date === dateStr);
            dayTasks.sort((a, b) => a.time.localeCompare(b.time));

            dayElement.innerHTML = `
                <div class="week-day-header">
                    <div class="week-day-name">${this.getDayName(date)}</div>
                    <div class="week-day-date">${date.getDate()}</div>
                </div>
                <div class="week-tasks">
                    ${dayTasks.map(task => `
                        <div class="week-task priority-${task.priority}" onclick="taskManager.editTask('${task.id}')">
                            <div class="task-title">${task.title}</div>
                            <div class="task-time">${task.time}</div>
                        </div>
                    `).join('')}
                </div>
            `;

            // 添加点击事件切换到日视图
            dayElement.addEventListener('click', (e) => {
                if (!e.target.closest('.week-task')) {
                    this.currentDate = new Date(date);
                    this.switchView('day');
                }
            });

            weekGrid.appendChild(dayElement);
        }
    }

    // 创建任务元素
    createTaskElement(task) {
        const taskElement = document.createElement('div');
        taskElement.className = `task-card priority-${task.priority} category-${task.category}`;
        taskElement.innerHTML = `
            <div class="task-title">${task.title}</div>
            <div class="task-time">${task.time} (${task.duration}分钟)</div>
            ${task.description ? `<div class="task-description">${task.description}</div>` : ''}
            <div class="task-actions">
                <button class="task-action-btn" onclick="taskManager.editTask('${task.id}')">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="task-action-btn" onclick="taskManager.deleteTask('${task.id}')">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
        return taskElement;
    }

    // 打开任务模态框
    openTaskModal(task = null) {
        this.editingTask = task;
        const modal = document.getElementById('taskModal');
        const form = document.getElementById('taskForm');
        
        if (task) {
            document.getElementById('modalTitle').textContent = '编辑任务';
            document.getElementById('taskTitle').value = task.title;
            document.getElementById('taskDescription').value = task.description || '';
            document.getElementById('taskDate').value = task.date;
            document.getElementById('taskTime').value = task.time;
            document.getElementById('taskDuration').value = task.duration;
            document.getElementById('taskPriority').value = task.priority;
            document.getElementById('taskCategory').value = task.category;
        } else {
            document.getElementById('modalTitle').textContent = '添加任务';
            form.reset();
            document.getElementById('taskDate').value = this.formatDate(this.currentDate);
            document.getElementById('taskTime').value = '09:00';
            document.getElementById('taskDuration').value = '60';
        }
        
        modal.classList.add('active');
    }

    closeTaskModal() {
        document.getElementById('taskModal').classList.remove('active');
        this.editingTask = null;
    }

    // 处理任务表单提交
    handleTaskSubmit(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const taskData = {
            id: this.editingTask ? this.editingTask.id : this.generateId(),
            title: document.getElementById('taskTitle').value,
            description: document.getElementById('taskDescription').value,
            date: document.getElementById('taskDate').value,
            time: document.getElementById('taskTime').value,
            duration: parseInt(document.getElementById('taskDuration').value),
            priority: document.getElementById('taskPriority').value,
            category: document.getElementById('taskCategory').value,
            completed: this.editingTask ? this.editingTask.completed : false
        };

        if (this.editingTask) {
            const index = this.tasks.findIndex(t => t.id === this.editingTask.id);
            this.tasks[index] = taskData;
        } else {
            this.tasks.push(taskData);
        }

        this.saveTasks();
        this.updateDisplay();
        this.closeTaskModal();
    }

    // 编辑任务
    editTask(taskId) {
        const task = this.tasks.find(t => t.id === taskId);
        if (task) {
            this.openTaskModal(task);
        }
    }

    // 删除任务
    deleteTask(taskId) {
        if (confirm('确定要删除这个任务吗？')) {
            this.tasks = this.tasks.filter(t => t.id !== taskId);
            this.saveTasks();
            this.updateDisplay();
        }
    }

    // 打开复制模态框
    openCopyModal() {
        const dateStr = this.formatDate(this.currentDate);
        const dayTasks = this.tasks.filter(task => task.date === dateStr);
        
        if (dayTasks.length === 0) {
            alert('当前日期没有任务可以复制');
            return;
        }

        this.copyingDate = dateStr;
        const modal = document.getElementById('copyModal');
        const dateSelector = document.getElementById('dateSelector');
        
        // 生成未来7天的日期选项
        dateSelector.innerHTML = '';
        for (let i = 1; i <= 7; i++) {
            const date = new Date(this.currentDate);
            date.setDate(date.getDate() + i);
            const dateStr = this.formatDate(date);
            
            const option = document.createElement('div');
            option.className = 'date-option';
            option.dataset.date = dateStr;
            option.innerHTML = `
                <div>${this.getDayName(date)}</div>
                <div>${date.getDate()}日</div>
            `;
            option.addEventListener('click', () => {
                document.querySelectorAll('.date-option').forEach(opt => opt.classList.remove('selected'));
                option.classList.add('selected');
            });
            dateSelector.appendChild(option);
        }
        
        modal.classList.add('active');
    }

    closeCopyModal() {
        document.getElementById('copyModal').classList.remove('active');
        this.copyingDate = null;
    }

    // 确认复制任务
    confirmCopy() {
        const selectedOption = document.querySelector('.date-option.selected');
        if (!selectedOption) {
            alert('请选择要复制到的日期');
            return;
        }

        const targetDate = selectedOption.dataset.date;
        const sourceTasks = this.tasks.filter(task => task.date === this.copyingDate);
        
        sourceTasks.forEach(task => {
            const newTask = {
                ...task,
                id: this.generateId(),
                date: targetDate
            };
            this.tasks.push(newTask);
        });

        this.saveTasks();
        this.updateDisplay();
        this.closeCopyModal();
        alert(`已成功复制 ${sourceTasks.length} 个任务`);
    }

    // 工具方法
    formatDate(date) {
        return date.toISOString().split('T')[0];
    }

    getWeekStart(date) {
        const d = new Date(date);
        const day = d.getDay();
        const diff = d.getDate() - day + (day === 0 ? -6 : 1); // 周一为一周开始
        return new Date(d.setDate(diff));
    }

    getDayName(date) {
        const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
        return days[date.getDay()];
    }

    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // 数据持久化
    saveTasks() {
        localStorage.setItem('tasks', JSON.stringify(this.tasks));
    }

    loadTasks() {
        const saved = localStorage.getItem('tasks');
        return saved ? JSON.parse(saved) : [];
    }
}

// 初始化应用
let taskManager;
document.addEventListener('DOMContentLoaded', () => {
    taskManager = new TaskManager();
});
