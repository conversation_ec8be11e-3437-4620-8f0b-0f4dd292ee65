package com.taskmanager.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.KeyboardArrowLeft
import androidx.compose.material.icons.filled.KeyboardArrowRight
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.taskmanager.data.model.Task
import com.taskmanager.ui.components.AddTaskDialog
import com.taskmanager.ui.theme.*
import com.taskmanager.viewmodel.TaskViewModel
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.format.TextStyle
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WeekViewScreen(
    viewModel: TaskViewModel = viewModel()
) {
    val currentDate by viewModel.currentDate.collectAsState()
    val weekTasks by viewModel.currentWeekTasks.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()

    // 对话框状态
    var showAddTaskDialog by remember { mutableStateOf(false) }
    var showEditTaskDialog by remember { mutableStateOf(false) }
    var taskToEdit by remember { mutableStateOf<Task?>(null) }
    
    // 计算当前周的开始日期
    val weekStart = currentDate.minusDays(currentDate.dayOfWeek.value - 1L)
    val weekDays = (0..6).map { weekStart.plusDays(it.toLong()) }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(GradientColors)
            )
    ) {
        // 头部导航
        Card(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(bottomStart = 16.dp, bottomEnd = 16.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                // 周导航
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    IconButton(onClick = { viewModel.navigateWeek(-1) }) {
                        Icon(Icons.Default.KeyboardArrowLeft, contentDescription = "上一周")
                    }
                    
                    Text(
                        text = "${weekStart.format(DateTimeFormatter.ofPattern("MM月dd日"))} - ${weekStart.plusDays(6).format(DateTimeFormatter.ofPattern("MM月dd日"))}",
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Bold
                    )
                    
                    IconButton(onClick = { viewModel.navigateWeek(1) }) {
                        Icon(Icons.Default.KeyboardArrowRight, contentDescription = "下一周")
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 添加任务按钮
                Button(
                    onClick = { showAddTaskDialog = true },
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Icon(Icons.Default.Add, contentDescription = null)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("添加任务")
                }
                
                // 回到今天按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.Center
                ) {
                    TextButton(onClick = { viewModel.goToToday() }) {
                        Text("回到本周")
                    }
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 周视图网格
        if (isLoading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else {
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 16.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(weekDays) { date ->
                    WeekDayCard(
                        date = date,
                        tasks = weekTasks.filter { it.date == date },
                        isToday = date == LocalDate.now(),
                        onDateClick = { 
                            viewModel.setCurrentDate(date)
                            // TODO: 切换到日视图
                        },
                        onTaskClick = { task ->
                            taskToEdit = task
                            showEditTaskDialog = true
                        }
                    )
                }
                
                // 底部间距
                item {
                    Spacer(modifier = Modifier.height(16.dp))
                }
            }
        }
    }

    // 添加任务对话框
    if (showAddTaskDialog) {
        AddTaskDialog(
            currentDate = currentDate,
            onDismiss = { showAddTaskDialog = false },
            onConfirm = { task ->
                viewModel.insertTask(task)
                showAddTaskDialog = false
            }
        )
    }

    // 编辑任务对话框
    if (showEditTaskDialog && taskToEdit != null) {
        AddTaskDialog(
            currentDate = currentDate,
            task = taskToEdit,
            onDismiss = {
                showEditTaskDialog = false
                taskToEdit = null
            },
            onConfirm = { task ->
                viewModel.updateTask(task)
                showEditTaskDialog = false
                taskToEdit = null
            }
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun WeekDayCard(
    date: LocalDate,
    tasks: List<Task>,
    isToday: Boolean,
    onDateClick: () -> Unit,
    onTaskClick: (Task) -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onDateClick() },
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (isToday) PrimaryBlue.copy(alpha = 0.1f) else CardBackground
        )
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            // 日期头部
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = date.dayOfWeek.getDisplayName(TextStyle.SHORT, Locale.CHINESE),
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = if (isToday) PrimaryBlue else MaterialTheme.colorScheme.onSurface
                )
                
                Text(
                    text = date.dayOfMonth.toString(),
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = if (isToday) PrimaryBlue else MaterialTheme.colorScheme.onSurface
                )
            }
            
            if (tasks.isNotEmpty()) {
                Spacer(modifier = Modifier.height(8.dp))
                
                // 任务列表
                tasks.take(3).forEach { task ->
                    WeekTaskItem(
                        task = task,
                        onClick = { onTaskClick(task) }
                    )
                }
                
                // 如果任务超过3个，显示更多提示
                if (tasks.size > 3) {
                    Text(
                        text = "还有 ${tasks.size - 3} 个任务...",
                        style = MaterialTheme.typography.bodySmall,
                        color = TextSecondary,
                        textAlign = TextAlign.Center,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = 4.dp)
                    )
                }
            } else {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "无任务",
                    style = MaterialTheme.typography.bodySmall,
                    color = TextSecondary,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    }
}

@Composable
private fun WeekTaskItem(
    task: Task,
    onClick: () -> Unit
) {
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        color = getCategoryColor(task.category).copy(alpha = 0.1f),
        shape = RoundedCornerShape(8.dp)
    ) {
        Row(
            modifier = Modifier.padding(8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 优先级指示器
            Box(
                modifier = Modifier
                    .size(8.dp)
                    .background(
                        color = getPriorityColor(task.priority),
                        shape = RoundedCornerShape(4.dp)
                    )
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = task.title,
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium,
                    maxLines = 1
                )
                Text(
                    text = task.timeDisplay,
                    style = MaterialTheme.typography.bodySmall,
                    color = TextSecondary
                )
            }
        }
    }
    
    Spacer(modifier = Modifier.height(4.dp))
}

private fun getCategoryColor(category: com.taskmanager.data.model.TaskCategory): androidx.compose.ui.graphics.Color {
    return when (category) {
        com.taskmanager.data.model.TaskCategory.WORK -> WorkCategoryColor
        com.taskmanager.data.model.TaskCategory.PERSONAL -> PersonalCategoryColor
        com.taskmanager.data.model.TaskCategory.STUDY -> StudyCategoryColor
        com.taskmanager.data.model.TaskCategory.HEALTH -> HealthCategoryColor
        com.taskmanager.data.model.TaskCategory.OTHER -> OtherCategoryColor
    }
}

private fun getPriorityColor(priority: com.taskmanager.data.model.TaskPriority): androidx.compose.ui.graphics.Color {
    return when (priority) {
        com.taskmanager.data.model.TaskPriority.HIGH -> HighPriorityColor
        com.taskmanager.data.model.TaskPriority.MEDIUM -> MediumPriorityColor
        com.taskmanager.data.model.TaskPriority.LOW -> LowPriorityColor
    }
}
