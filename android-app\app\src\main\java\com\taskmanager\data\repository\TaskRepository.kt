package com.taskmanager.data.repository

import com.taskmanager.data.database.TaskDao
import com.taskmanager.data.model.Task
import kotlinx.coroutines.flow.Flow
import java.time.LocalDate

class TaskRepository(private val taskDao: TaskDao) {
    
    fun getAllTasks(): Flow<List<Task>> = taskDao.getAllTasks()
    
    fun getTasksByDate(date: LocalDate): Flow<List<Task>> = taskDao.getTasksByDate(date)
    
    fun getTasksByDateRange(startDate: LocalDate, endDate: LocalDate): Flow<List<Task>> = 
        taskDao.getTasksByDateRange(startDate, endDate)
    
    suspend fun getTaskById(id: String): Task? = taskDao.getTaskById(id)
    
    suspend fun insertTask(task: Task) = taskDao.insertTask(task)
    
    suspend fun insertTasks(tasks: List<Task>) = taskDao.insertTasks(tasks)
    
    suspend fun updateTask(task: Task) = taskDao.updateTask(task)
    
    suspend fun deleteTask(task: Task) = taskDao.deleteTask(task)
    
    suspend fun deleteTaskById(id: String) = taskDao.deleteTaskById(id)
    
    suspend fun getTaskCountByDate(date: LocalDate): Int = taskDao.getTaskCountByDate(date)
    
    suspend fun updateTaskCompletion(id: String, completed: Boolean) = 
        taskDao.updateTaskCompletion(id, completed)
    
    /**
     * 复制指定日期的所有任务到目标日期
     */
    suspend fun copyTasksToDate(sourceDate: LocalDate, targetDate: LocalDate): Int {
        val sourceTasks = taskDao.getTasksByDate(sourceDate)
        var copiedCount = 0
        
        sourceTasks.collect { tasks ->
            val newTasks = tasks.map { task ->
                task.copy(
                    id = generateTaskId(),
                    date = targetDate
                )
            }
            if (newTasks.isNotEmpty()) {
                taskDao.insertTasks(newTasks)
                copiedCount = newTasks.size
            }
        }
        
        return copiedCount
    }
    
    private fun generateTaskId(): String {
        return "${System.currentTimeMillis()}_${(1000..9999).random()}"
    }
}
