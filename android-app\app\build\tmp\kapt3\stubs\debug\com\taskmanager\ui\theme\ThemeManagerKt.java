package com.taskmanager.ui.theme;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u001c\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\u001a)\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0017\u0010\u0004\u001a\u0013\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u0005\u00a2\u0006\u0002\b\u0007H\u0007\u00a8\u0006\b"}, d2 = {"ProvideCustomTheme", "", "themeManager", "Lcom/taskmanager/ui/theme/ThemeManager;", "content", "Lkotlin/Function1;", "Lcom/taskmanager/data/model/ThemeSettings;", "Landroidx/compose/runtime/Composable;", "app_debug"})
public final class ThemeManagerKt {
    
    @androidx.compose.runtime.Composable()
    public static final void ProvideCustomTheme(@org.jetbrains.annotations.NotNull()
    com.taskmanager.ui.theme.ThemeManager themeManager, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.taskmanager.data.model.ThemeSettings, kotlin.Unit> content) {
    }
}