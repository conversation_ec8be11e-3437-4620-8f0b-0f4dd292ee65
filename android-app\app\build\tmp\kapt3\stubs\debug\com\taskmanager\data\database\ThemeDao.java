package com.taskmanager.data.database;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\bg\u0018\u00002\u00020\u0001J\u000e\u0010\u0002\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0010\u0010\u0005\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070\u0006H\'J\u0010\u0010\b\u001a\u0004\u0018\u00010\u0007H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\t\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\u000bJ\u0016\u0010\f\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\u000b\u00a8\u0006\r"}, d2 = {"Lcom/taskmanager/data/database/ThemeDao;", "", "deleteAllThemeSettings", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getThemeSettings", "Lkotlinx/coroutines/flow/Flow;", "Lcom/taskmanager/data/model/ThemeSettings;", "getThemeSettingsSync", "insertThemeSettings", "themeSettings", "(Lcom/taskmanager/data/model/ThemeSettings;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateThemeSettings", "app_debug"})
@androidx.room.Dao()
public abstract interface ThemeDao {
    
    @androidx.room.Query(value = "SELECT * FROM theme_settings WHERE id = 1")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<com.taskmanager.data.model.ThemeSettings> getThemeSettings();
    
    @androidx.room.Query(value = "SELECT * FROM theme_settings WHERE id = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getThemeSettingsSync(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.taskmanager.data.model.ThemeSettings> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertThemeSettings(@org.jetbrains.annotations.NotNull()
    com.taskmanager.data.model.ThemeSettings themeSettings, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateThemeSettings(@org.jetbrains.annotations.NotNull()
    com.taskmanager.data.model.ThemeSettings themeSettings, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM theme_settings")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteAllThemeSettings(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}