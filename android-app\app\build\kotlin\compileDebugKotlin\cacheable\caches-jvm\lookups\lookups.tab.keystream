  Activity android.app  Application android.app  Bundle android.app.Activity  TaskManagerApp android.app.Activity  TaskManagerTheme android.app.Activity  	getWINDOW android.app.Activity  	getWindow android.app.Activity  onCreate android.app.Activity  
setContent android.app.Activity  	setWindow android.app.Activity  window android.app.Activity  Context android.content  Bundle android.content.Context  TaskManagerApp android.content.Context  TaskManagerTheme android.content.Context  applicationContext android.content.Context  getAPPLICATIONContext android.content.Context  getApplicationContext android.content.Context  onCreate android.content.Context  setApplicationContext android.content.Context  
setContent android.content.Context  Bundle android.content.ContextWrapper  TaskManagerApp android.content.ContextWrapper  TaskManagerTheme android.content.ContextWrapper  onCreate android.content.ContextWrapper  
setContent android.content.ContextWrapper  Build 
android.os  Bundle 
android.os  
Parcelable 
android.os  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  View android.view  Window android.view  Bundle  android.view.ContextThemeWrapper  TaskManagerApp  android.view.ContextThemeWrapper  TaskManagerTheme  android.view.ContextThemeWrapper  onCreate  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  context android.view.View  
getCONTEXT android.view.View  
getContext android.view.View  getISInEditMode android.view.View  getIsInEditMode android.view.View  isInEditMode android.view.View  
setContext android.view.View  
setInEditMode android.view.View  getSTATUSBarColor android.view.Window  getStatusBarColor android.view.Window  setStatusBarColor android.view.Window  statusBarColor android.view.Window  ComponentActivity androidx.activity  Bundle #androidx.activity.ComponentActivity  TaskManagerApp #androidx.activity.ComponentActivity  TaskManagerTheme #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  AnimatedContentScope androidx.compose.animation  
DayViewScreen /androidx.compose.animation.AnimatedContentScope  WeekViewScreen /androidx.compose.animation.AnimatedContentScope  BorderStroke androidx.compose.foundation  ScrollState androidx.compose.foundation  
background androidx.compose.foundation  	clickable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  rememberScrollState androidx.compose.foundation  verticalScroll androidx.compose.foundation  	Alignment "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardBackground "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  Checkbox "androidx.compose.foundation.layout  CircularProgressIndicator "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  DateOptionCard "androidx.compose.foundation.layout  DateTimeFormatter "androidx.compose.foundation.layout  
DayViewScreen "androidx.compose.foundation.layout  DividerColor "androidx.compose.foundation.layout  DropdownMenuItem "androidx.compose.foundation.layout  
ErrorColor "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  ExposedDropdownMenuBox "androidx.compose.foundation.layout  ExposedDropdownMenuDefaults "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  GradientColors "androidx.compose.foundation.layout  HealthCategoryColor "androidx.compose.foundation.layout  HighPriorityColor "androidx.compose.foundation.layout  HorizontalDivider "androidx.compose.foundation.layout  Icon "androidx.compose.foundation.layout  
IconButton "androidx.compose.foundation.layout  Icons "androidx.compose.foundation.layout  
LazyColumn "androidx.compose.foundation.layout  	LocalDate "androidx.compose.foundation.layout  	LocalTime "androidx.compose.foundation.layout  Locale "androidx.compose.foundation.layout  LowPriorityColor "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  MediumPriorityColor "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  
NavigationBar "androidx.compose.foundation.layout  NavigationBarItem "androidx.compose.foundation.layout  OtherCategoryColor "androidx.compose.foundation.layout  OutlinedButton "androidx.compose.foundation.layout  OutlinedTextField "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  PersonalCategoryColor "androidx.compose.foundation.layout  PrimaryBlue "androidx.compose.foundation.layout  RoundedCornerShape "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Scaffold "androidx.compose.foundation.layout  Screen "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  StudyCategoryColor "androidx.compose.foundation.layout  Surface "androidx.compose.foundation.layout  System "androidx.compose.foundation.layout  Task "androidx.compose.foundation.layout  TaskCard "androidx.compose.foundation.layout  TaskCategory "androidx.compose.foundation.layout  TaskManagerApp "androidx.compose.foundation.layout  TaskManagerTheme "androidx.compose.foundation.layout  TaskPriority "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  	TextAlign "androidx.compose.foundation.layout  
TextButton "androidx.compose.foundation.layout  TextOverflow "androidx.compose.foundation.layout  
TextSecondary "androidx.compose.foundation.layout  	TextStyle "androidx.compose.foundation.layout  WeekDayCard "androidx.compose.foundation.layout  WeekTaskItem "androidx.compose.foundation.layout  WeekViewScreen "androidx.compose.foundation.layout  WorkCategoryColor "androidx.compose.foundation.layout  androidx "androidx.compose.foundation.layout  any "androidx.compose.foundation.layout  
background "androidx.compose.foundation.layout  clip "androidx.compose.foundation.layout  collectAsState "androidx.compose.foundation.layout  com "androidx.compose.foundation.layout  
composable "androidx.compose.foundation.layout  currentBackStackEntryAsState "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  filter "androidx.compose.foundation.layout  findStartDestination "androidx.compose.foundation.layout  forEach "androidx.compose.foundation.layout  getCategoryColor "androidx.compose.foundation.layout  getPriorityColor "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  heightIn "androidx.compose.foundation.layout  isBlank "androidx.compose.foundation.layout  
isNotBlank "androidx.compose.foundation.layout  
isNotEmpty "androidx.compose.foundation.layout  
isNullOrBlank "androidx.compose.foundation.layout  items "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  map "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  random "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  rememberScrollState "androidx.compose.foundation.layout  
setContent "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  take "androidx.compose.foundation.layout  takeIf "androidx.compose.foundation.layout  toIntOrNull "androidx.compose.foundation.layout  trim "androidx.compose.foundation.layout  verticalScroll "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  End .androidx.compose.foundation.layout.Arrangement  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  	Alignment +androidx.compose.foundation.layout.BoxScope  CircularProgressIndicator +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  Spacer +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  	getHEIGHT +androidx.compose.foundation.layout.BoxScope  	getHeight +androidx.compose.foundation.layout.BoxScope  height +androidx.compose.foundation.layout.BoxScope  Add .androidx.compose.foundation.layout.ColumnScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  Check .androidx.compose.foundation.layout.ColumnScope  Checkbox .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  ContentCopy .androidx.compose.foundation.layout.ColumnScope  DateOptionCard .androidx.compose.foundation.layout.ColumnScope  DateTimeFormatter .androidx.compose.foundation.layout.ColumnScope  Delete .androidx.compose.foundation.layout.ColumnScope  DividerColor .androidx.compose.foundation.layout.ColumnScope  DropdownMenuItem .androidx.compose.foundation.layout.ColumnScope  Edit .androidx.compose.foundation.layout.ColumnScope  
ErrorColor .androidx.compose.foundation.layout.ColumnScope  ExposedDropdownMenuBox .androidx.compose.foundation.layout.ColumnScope  ExposedDropdownMenuDefaults .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  HorizontalDivider .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  
IconButton .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  KeyboardArrowLeft .androidx.compose.foundation.layout.ColumnScope  KeyboardArrowRight .androidx.compose.foundation.layout.ColumnScope  
LazyColumn .androidx.compose.foundation.layout.ColumnScope  	LocalDate .androidx.compose.foundation.layout.ColumnScope  Locale .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  OutlinedButton .androidx.compose.foundation.layout.ColumnScope  OutlinedTextField .androidx.compose.foundation.layout.ColumnScope  PrimaryBlue .androidx.compose.foundation.layout.ColumnScope  RoundedCornerShape .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  Surface .androidx.compose.foundation.layout.ColumnScope  System .androidx.compose.foundation.layout.ColumnScope  Task .androidx.compose.foundation.layout.ColumnScope  TaskCard .androidx.compose.foundation.layout.ColumnScope  TaskCategory .androidx.compose.foundation.layout.ColumnScope  TaskPriority .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  	TextAlign .androidx.compose.foundation.layout.ColumnScope  
TextButton .androidx.compose.foundation.layout.ColumnScope  TextOverflow .androidx.compose.foundation.layout.ColumnScope  
TextSecondary .androidx.compose.foundation.layout.ColumnScope  	TextStyle .androidx.compose.foundation.layout.ColumnScope  WeekDayCard .androidx.compose.foundation.layout.ColumnScope  WeekTaskItem .androidx.compose.foundation.layout.ColumnScope  
background .androidx.compose.foundation.layout.ColumnScope  clip .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  filter .androidx.compose.foundation.layout.ColumnScope  forEach .androidx.compose.foundation.layout.ColumnScope  
getBACKGROUND .androidx.compose.foundation.layout.ColumnScope  
getBackground .androidx.compose.foundation.layout.ColumnScope  getCLIP .androidx.compose.foundation.layout.ColumnScope  getCategoryColor .androidx.compose.foundation.layout.ColumnScope  getClip .androidx.compose.foundation.layout.ColumnScope  getFILLMaxSize .androidx.compose.foundation.layout.ColumnScope  getFILLMaxWidth .androidx.compose.foundation.layout.ColumnScope  	getFILTER .androidx.compose.foundation.layout.ColumnScope  
getFOREach .androidx.compose.foundation.layout.ColumnScope  getFillMaxSize .androidx.compose.foundation.layout.ColumnScope  getFillMaxWidth .androidx.compose.foundation.layout.ColumnScope  	getFilter .androidx.compose.foundation.layout.ColumnScope  
getForEach .androidx.compose.foundation.layout.ColumnScope  getGETCategoryColor .androidx.compose.foundation.layout.ColumnScope  getGETPriorityColor .androidx.compose.foundation.layout.ColumnScope  getGETValue .androidx.compose.foundation.layout.ColumnScope  getGetCategoryColor .androidx.compose.foundation.layout.ColumnScope  getGetPriorityColor .androidx.compose.foundation.layout.ColumnScope  getGetValue .androidx.compose.foundation.layout.ColumnScope  	getHEIGHT .androidx.compose.foundation.layout.ColumnScope  getHEIGHTIn .androidx.compose.foundation.layout.ColumnScope  	getHeight .androidx.compose.foundation.layout.ColumnScope  getHeightIn .androidx.compose.foundation.layout.ColumnScope  
getISBlank .androidx.compose.foundation.layout.ColumnScope  
getISNotBlank .androidx.compose.foundation.layout.ColumnScope  
getISNotEmpty .androidx.compose.foundation.layout.ColumnScope  getISNullOrBlank .androidx.compose.foundation.layout.ColumnScope  
getIsBlank .androidx.compose.foundation.layout.ColumnScope  
getIsNotBlank .androidx.compose.foundation.layout.ColumnScope  
getIsNotEmpty .androidx.compose.foundation.layout.ColumnScope  getIsNullOrBlank .androidx.compose.foundation.layout.ColumnScope  getLET .androidx.compose.foundation.layout.ColumnScope  getLet .androidx.compose.foundation.layout.ColumnScope  getMUTABLEStateOf .androidx.compose.foundation.layout.ColumnScope  getMutableStateOf .androidx.compose.foundation.layout.ColumnScope  
getPADDING .androidx.compose.foundation.layout.ColumnScope  getPROVIDEDelegate .androidx.compose.foundation.layout.ColumnScope  
getPadding .androidx.compose.foundation.layout.ColumnScope  getPriorityColor .androidx.compose.foundation.layout.ColumnScope  getProvideDelegate .androidx.compose.foundation.layout.ColumnScope  	getRANDOM .androidx.compose.foundation.layout.ColumnScope  getREMEMBER .androidx.compose.foundation.layout.ColumnScope  getREMEMBERScrollState .androidx.compose.foundation.layout.ColumnScope  	getRandom .androidx.compose.foundation.layout.ColumnScope  getRemember .androidx.compose.foundation.layout.ColumnScope  getRememberScrollState .androidx.compose.foundation.layout.ColumnScope  getSETValue .androidx.compose.foundation.layout.ColumnScope  getSIZE .androidx.compose.foundation.layout.ColumnScope  getSetValue .androidx.compose.foundation.layout.ColumnScope  getSize .androidx.compose.foundation.layout.ColumnScope  getTAKE .androidx.compose.foundation.layout.ColumnScope  	getTAKEIf .androidx.compose.foundation.layout.ColumnScope  getTOIntOrNull .androidx.compose.foundation.layout.ColumnScope  getTRIM .androidx.compose.foundation.layout.ColumnScope  getTake .androidx.compose.foundation.layout.ColumnScope  	getTakeIf .androidx.compose.foundation.layout.ColumnScope  getToIntOrNull .androidx.compose.foundation.layout.ColumnScope  getTrim .androidx.compose.foundation.layout.ColumnScope  getVERTICALScroll .androidx.compose.foundation.layout.ColumnScope  getValue .androidx.compose.foundation.layout.ColumnScope  getVerticalScroll .androidx.compose.foundation.layout.ColumnScope  getWIDTH .androidx.compose.foundation.layout.ColumnScope  getWidth .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  heightIn .androidx.compose.foundation.layout.ColumnScope  isBlank .androidx.compose.foundation.layout.ColumnScope  
isNotBlank .androidx.compose.foundation.layout.ColumnScope  
isNotEmpty .androidx.compose.foundation.layout.ColumnScope  
isNullOrBlank .androidx.compose.foundation.layout.ColumnScope  items .androidx.compose.foundation.layout.ColumnScope  let .androidx.compose.foundation.layout.ColumnScope  mutableStateOf .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  provideDelegate .androidx.compose.foundation.layout.ColumnScope  random .androidx.compose.foundation.layout.ColumnScope  remember .androidx.compose.foundation.layout.ColumnScope  rememberScrollState .androidx.compose.foundation.layout.ColumnScope  setValue .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  take .androidx.compose.foundation.layout.ColumnScope  takeIf .androidx.compose.foundation.layout.ColumnScope  toIntOrNull .androidx.compose.foundation.layout.ColumnScope  trim .androidx.compose.foundation.layout.ColumnScope  verticalScroll .androidx.compose.foundation.layout.ColumnScope  width .androidx.compose.foundation.layout.ColumnScope  Add +androidx.compose.foundation.layout.RowScope  	Alignment +androidx.compose.foundation.layout.RowScope  Box +androidx.compose.foundation.layout.RowScope  Button +androidx.compose.foundation.layout.RowScope  Check +androidx.compose.foundation.layout.RowScope  Checkbox +androidx.compose.foundation.layout.RowScope  Column +androidx.compose.foundation.layout.RowScope  ContentCopy +androidx.compose.foundation.layout.RowScope  DateTimeFormatter +androidx.compose.foundation.layout.RowScope  Delete +androidx.compose.foundation.layout.RowScope  Edit +androidx.compose.foundation.layout.RowScope  
ErrorColor +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  
IconButton +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  KeyboardArrowLeft +androidx.compose.foundation.layout.RowScope  KeyboardArrowRight +androidx.compose.foundation.layout.RowScope  Locale +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  NavigationBarItem +androidx.compose.foundation.layout.RowScope  OutlinedButton +androidx.compose.foundation.layout.RowScope  PrimaryBlue +androidx.compose.foundation.layout.RowScope  RoundedCornerShape +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  Surface +androidx.compose.foundation.layout.RowScope  System +androidx.compose.foundation.layout.RowScope  Task +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  
TextButton +androidx.compose.foundation.layout.RowScope  TextOverflow +androidx.compose.foundation.layout.RowScope  
TextSecondary +androidx.compose.foundation.layout.RowScope  	TextStyle +androidx.compose.foundation.layout.RowScope  any +androidx.compose.foundation.layout.RowScope  
background +androidx.compose.foundation.layout.RowScope  clip +androidx.compose.foundation.layout.RowScope  currentBackStackEntryAsState +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  findStartDestination +androidx.compose.foundation.layout.RowScope  getANY +androidx.compose.foundation.layout.RowScope  getAny +androidx.compose.foundation.layout.RowScope  
getBACKGROUND +androidx.compose.foundation.layout.RowScope  
getBackground +androidx.compose.foundation.layout.RowScope  getCLIP +androidx.compose.foundation.layout.RowScope  getCURRENTBackStackEntryAsState +androidx.compose.foundation.layout.RowScope  getCategoryColor +androidx.compose.foundation.layout.RowScope  getClip +androidx.compose.foundation.layout.RowScope  getCurrentBackStackEntryAsState +androidx.compose.foundation.layout.RowScope  getFINDStartDestination +androidx.compose.foundation.layout.RowScope  getFindStartDestination +androidx.compose.foundation.layout.RowScope  getGETCategoryColor +androidx.compose.foundation.layout.RowScope  getGETPriorityColor +androidx.compose.foundation.layout.RowScope  getGETValue +androidx.compose.foundation.layout.RowScope  getGetCategoryColor +androidx.compose.foundation.layout.RowScope  getGetPriorityColor +androidx.compose.foundation.layout.RowScope  getGetValue +androidx.compose.foundation.layout.RowScope  
getISBlank +androidx.compose.foundation.layout.RowScope  
getISNotBlank +androidx.compose.foundation.layout.RowScope  
getISNotEmpty +androidx.compose.foundation.layout.RowScope  getISNullOrBlank +androidx.compose.foundation.layout.RowScope  
getIsBlank +androidx.compose.foundation.layout.RowScope  
getIsNotBlank +androidx.compose.foundation.layout.RowScope  
getIsNotEmpty +androidx.compose.foundation.layout.RowScope  getIsNullOrBlank +androidx.compose.foundation.layout.RowScope  getLET +androidx.compose.foundation.layout.RowScope  getLet +androidx.compose.foundation.layout.RowScope  
getPADDING +androidx.compose.foundation.layout.RowScope  getPROVIDEDelegate +androidx.compose.foundation.layout.RowScope  
getPadding +androidx.compose.foundation.layout.RowScope  getPriorityColor +androidx.compose.foundation.layout.RowScope  getProvideDelegate +androidx.compose.foundation.layout.RowScope  	getRANDOM +androidx.compose.foundation.layout.RowScope  	getRandom +androidx.compose.foundation.layout.RowScope  getSIZE +androidx.compose.foundation.layout.RowScope  getSize +androidx.compose.foundation.layout.RowScope  	getTAKEIf +androidx.compose.foundation.layout.RowScope  getTOIntOrNull +androidx.compose.foundation.layout.RowScope  getTRIM +androidx.compose.foundation.layout.RowScope  	getTakeIf +androidx.compose.foundation.layout.RowScope  getToIntOrNull +androidx.compose.foundation.layout.RowScope  getTrim +androidx.compose.foundation.layout.RowScope  getValue +androidx.compose.foundation.layout.RowScope  getWIDTH +androidx.compose.foundation.layout.RowScope  getWidth +androidx.compose.foundation.layout.RowScope  	hierarchy +androidx.compose.foundation.layout.RowScope  isBlank +androidx.compose.foundation.layout.RowScope  
isNotBlank +androidx.compose.foundation.layout.RowScope  
isNotEmpty +androidx.compose.foundation.layout.RowScope  
isNullOrBlank +androidx.compose.foundation.layout.RowScope  let +androidx.compose.foundation.layout.RowScope  padding +androidx.compose.foundation.layout.RowScope  provideDelegate +androidx.compose.foundation.layout.RowScope  random +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  takeIf +androidx.compose.foundation.layout.RowScope  toIntOrNull +androidx.compose.foundation.layout.RowScope  trim +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  DateOptionCard .androidx.compose.foundation.lazy.LazyItemScope  	LocalDate .androidx.compose.foundation.lazy.LazyItemScope  Modifier .androidx.compose.foundation.lazy.LazyItemScope  Spacer .androidx.compose.foundation.lazy.LazyItemScope  TaskCard .androidx.compose.foundation.lazy.LazyItemScope  WeekDayCard .androidx.compose.foundation.lazy.LazyItemScope  dp .androidx.compose.foundation.lazy.LazyItemScope  filter .androidx.compose.foundation.lazy.LazyItemScope  	getFILTER .androidx.compose.foundation.lazy.LazyItemScope  	getFilter .androidx.compose.foundation.lazy.LazyItemScope  	getHEIGHT .androidx.compose.foundation.lazy.LazyItemScope  	getHeight .androidx.compose.foundation.lazy.LazyItemScope  height .androidx.compose.foundation.lazy.LazyItemScope  DateOptionCard .androidx.compose.foundation.lazy.LazyListScope  	LocalDate .androidx.compose.foundation.lazy.LazyListScope  Modifier .androidx.compose.foundation.lazy.LazyListScope  Spacer .androidx.compose.foundation.lazy.LazyListScope  TaskCard .androidx.compose.foundation.lazy.LazyListScope  WeekDayCard .androidx.compose.foundation.lazy.LazyListScope  dp .androidx.compose.foundation.lazy.LazyListScope  filter .androidx.compose.foundation.lazy.LazyListScope  	getFILTER .androidx.compose.foundation.lazy.LazyListScope  	getFilter .androidx.compose.foundation.lazy.LazyListScope  	getHEIGHT .androidx.compose.foundation.lazy.LazyListScope  	getHeight .androidx.compose.foundation.lazy.LazyListScope  getITEMS .androidx.compose.foundation.lazy.LazyListScope  getItems .androidx.compose.foundation.lazy.LazyListScope  height .androidx.compose.foundation.lazy.LazyListScope  item .androidx.compose.foundation.lazy.LazyListScope  items .androidx.compose.foundation.lazy.LazyListScope  RoundedCornerShape !androidx.compose.foundation.shape  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  Add ,androidx.compose.material.icons.Icons.Filled  
CalendarToday ,androidx.compose.material.icons.Icons.Filled  CalendarViewWeek ,androidx.compose.material.icons.Icons.Filled  Check ,androidx.compose.material.icons.Icons.Filled  ContentCopy ,androidx.compose.material.icons.Icons.Filled  Delete ,androidx.compose.material.icons.Icons.Filled  Edit ,androidx.compose.material.icons.Icons.Filled  KeyboardArrowLeft ,androidx.compose.material.icons.Icons.Filled  KeyboardArrowRight ,androidx.compose.material.icons.Icons.Filled  Add &androidx.compose.material.icons.filled  
CalendarToday &androidx.compose.material.icons.filled  CalendarViewWeek &androidx.compose.material.icons.filled  Check &androidx.compose.material.icons.filled  ContentCopy &androidx.compose.material.icons.filled  Delete &androidx.compose.material.icons.filled  Edit &androidx.compose.material.icons.filled  KeyboardArrowLeft &androidx.compose.material.icons.filled  KeyboardArrowRight &androidx.compose.material.icons.filled  	Alignment androidx.compose.material3  Arrangement androidx.compose.material3  Box androidx.compose.material3  Button androidx.compose.material3  Card androidx.compose.material3  CardBackground androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  
CardElevation androidx.compose.material3  Checkbox androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  
Composable androidx.compose.material3  DateOptionCard androidx.compose.material3  DateTimeFormatter androidx.compose.material3  
DayViewScreen androidx.compose.material3  DividerColor androidx.compose.material3  DropdownMenuItem androidx.compose.material3  
ErrorColor androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  ExposedDropdownMenuBox androidx.compose.material3  ExposedDropdownMenuBoxScope androidx.compose.material3  ExposedDropdownMenuDefaults androidx.compose.material3  
FontWeight androidx.compose.material3  GradientColors androidx.compose.material3  HealthCategoryColor androidx.compose.material3  HighPriorityColor androidx.compose.material3  HorizontalDivider androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  Icons androidx.compose.material3  
LazyColumn androidx.compose.material3  	LocalDate androidx.compose.material3  	LocalTime androidx.compose.material3  Locale androidx.compose.material3  LowPriorityColor androidx.compose.material3  
MaterialTheme androidx.compose.material3  MediumPriorityColor androidx.compose.material3  Modifier androidx.compose.material3  
NavigationBar androidx.compose.material3  NavigationBarItem androidx.compose.material3  OtherCategoryColor androidx.compose.material3  OutlinedButton androidx.compose.material3  OutlinedTextField androidx.compose.material3  PersonalCategoryColor androidx.compose.material3  PrimaryBlue androidx.compose.material3  RoundedCornerShape androidx.compose.material3  Row androidx.compose.material3  Scaffold androidx.compose.material3  Screen androidx.compose.material3  Spacer androidx.compose.material3  StudyCategoryColor androidx.compose.material3  Surface androidx.compose.material3  System androidx.compose.material3  Task androidx.compose.material3  TaskCard androidx.compose.material3  TaskCategory androidx.compose.material3  TaskManagerApp androidx.compose.material3  TaskManagerTheme androidx.compose.material3  TaskPriority androidx.compose.material3  Text androidx.compose.material3  	TextAlign androidx.compose.material3  
TextButton androidx.compose.material3  TextOverflow androidx.compose.material3  
TextSecondary androidx.compose.material3  	TextStyle androidx.compose.material3  
Typography androidx.compose.material3  WeekDayCard androidx.compose.material3  WeekTaskItem androidx.compose.material3  WeekViewScreen androidx.compose.material3  WorkCategoryColor androidx.compose.material3  androidx androidx.compose.material3  any androidx.compose.material3  
background androidx.compose.material3  clip androidx.compose.material3  collectAsState androidx.compose.material3  com androidx.compose.material3  
composable androidx.compose.material3  currentBackStackEntryAsState androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  filter androidx.compose.material3  findStartDestination androidx.compose.material3  forEach androidx.compose.material3  getCategoryColor androidx.compose.material3  getPriorityColor androidx.compose.material3  getValue androidx.compose.material3  height androidx.compose.material3  heightIn androidx.compose.material3  isBlank androidx.compose.material3  
isNotBlank androidx.compose.material3  
isNotEmpty androidx.compose.material3  
isNullOrBlank androidx.compose.material3  items androidx.compose.material3  let androidx.compose.material3  lightColorScheme androidx.compose.material3  listOf androidx.compose.material3  map androidx.compose.material3  mutableStateOf androidx.compose.material3  padding androidx.compose.material3  provideDelegate androidx.compose.material3  random androidx.compose.material3  remember androidx.compose.material3  rememberScrollState androidx.compose.material3  
setContent androidx.compose.material3  setValue androidx.compose.material3  size androidx.compose.material3  take androidx.compose.material3  takeIf androidx.compose.material3  toIntOrNull androidx.compose.material3  trim androidx.compose.material3  verticalScroll androidx.compose.material3  width androidx.compose.material3  
cardColors 'androidx.compose.material3.CardDefaults  
cardElevation 'androidx.compose.material3.CardDefaults  	onSurface &androidx.compose.material3.ColorScheme  onSurfaceVariant &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  surface &androidx.compose.material3.ColorScheme  DropdownMenuItem 6androidx.compose.material3.ExposedDropdownMenuBoxScope  ExposedDropdownMenu 6androidx.compose.material3.ExposedDropdownMenuBoxScope  ExposedDropdownMenuDefaults 6androidx.compose.material3.ExposedDropdownMenuBoxScope  Modifier 6androidx.compose.material3.ExposedDropdownMenuBoxScope  OutlinedTextField 6androidx.compose.material3.ExposedDropdownMenuBoxScope  TaskCategory 6androidx.compose.material3.ExposedDropdownMenuBoxScope  TaskPriority 6androidx.compose.material3.ExposedDropdownMenuBoxScope  Text 6androidx.compose.material3.ExposedDropdownMenuBoxScope  fillMaxWidth 6androidx.compose.material3.ExposedDropdownMenuBoxScope  forEach 6androidx.compose.material3.ExposedDropdownMenuBoxScope  getFILLMaxWidth 6androidx.compose.material3.ExposedDropdownMenuBoxScope  
getFOREach 6androidx.compose.material3.ExposedDropdownMenuBoxScope  getFillMaxWidth 6androidx.compose.material3.ExposedDropdownMenuBoxScope  
getForEach 6androidx.compose.material3.ExposedDropdownMenuBoxScope  
menuAnchor 6androidx.compose.material3.ExposedDropdownMenuBoxScope  TrailingIcon 6androidx.compose.material3.ExposedDropdownMenuDefaults  colorScheme (androidx.compose.material3.MaterialTheme  invoke (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  	bodyLarge %androidx.compose.material3.Typography  
bodyMedium %androidx.compose.material3.Typography  	bodySmall %androidx.compose.material3.Typography  
headlineSmall %androidx.compose.material3.Typography  
labelSmall %androidx.compose.material3.Typography  
titleLarge %androidx.compose.material3.Typography  titleMedium %androidx.compose.material3.Typography  	Alignment androidx.compose.runtime  Arrangement androidx.compose.runtime  Box androidx.compose.runtime  Button androidx.compose.runtime  Card androidx.compose.runtime  CardBackground androidx.compose.runtime  CardDefaults androidx.compose.runtime  Checkbox androidx.compose.runtime  CircularProgressIndicator androidx.compose.runtime  Column androidx.compose.runtime  
Composable androidx.compose.runtime  DateOptionCard androidx.compose.runtime  DateTimeFormatter androidx.compose.runtime  
DayViewScreen androidx.compose.runtime  DividerColor androidx.compose.runtime  DropdownMenuItem androidx.compose.runtime  
ErrorColor androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  ExposedDropdownMenuBox androidx.compose.runtime  ExposedDropdownMenuDefaults androidx.compose.runtime  
FontWeight androidx.compose.runtime  GradientColors androidx.compose.runtime  HealthCategoryColor androidx.compose.runtime  HighPriorityColor androidx.compose.runtime  HorizontalDivider androidx.compose.runtime  Icon androidx.compose.runtime  
IconButton androidx.compose.runtime  Icons androidx.compose.runtime  
LazyColumn androidx.compose.runtime  	LocalDate androidx.compose.runtime  	LocalTime androidx.compose.runtime  Locale androidx.compose.runtime  LowPriorityColor androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  MediumPriorityColor androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  
NavigationBar androidx.compose.runtime  NavigationBarItem androidx.compose.runtime  OtherCategoryColor androidx.compose.runtime  OutlinedButton androidx.compose.runtime  OutlinedTextField androidx.compose.runtime  PersonalCategoryColor androidx.compose.runtime  PrimaryBlue androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  RoundedCornerShape androidx.compose.runtime  Row androidx.compose.runtime  Scaffold androidx.compose.runtime  Screen androidx.compose.runtime  
SideEffect androidx.compose.runtime  Spacer androidx.compose.runtime  State androidx.compose.runtime  StudyCategoryColor androidx.compose.runtime  Surface androidx.compose.runtime  System androidx.compose.runtime  Task androidx.compose.runtime  TaskCard androidx.compose.runtime  TaskCategory androidx.compose.runtime  TaskManagerApp androidx.compose.runtime  TaskManagerTheme androidx.compose.runtime  TaskPriority androidx.compose.runtime  Text androidx.compose.runtime  	TextAlign androidx.compose.runtime  
TextButton androidx.compose.runtime  TextOverflow androidx.compose.runtime  
TextSecondary androidx.compose.runtime  	TextStyle androidx.compose.runtime  WeekDayCard androidx.compose.runtime  WeekTaskItem androidx.compose.runtime  WeekViewScreen androidx.compose.runtime  WorkCategoryColor androidx.compose.runtime  androidx androidx.compose.runtime  any androidx.compose.runtime  
background androidx.compose.runtime  clip androidx.compose.runtime  collectAsState androidx.compose.runtime  com androidx.compose.runtime  
composable androidx.compose.runtime  currentBackStackEntryAsState androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  filter androidx.compose.runtime  findStartDestination androidx.compose.runtime  forEach androidx.compose.runtime  getCategoryColor androidx.compose.runtime  getPriorityColor androidx.compose.runtime  getValue androidx.compose.runtime  height androidx.compose.runtime  heightIn androidx.compose.runtime  isBlank androidx.compose.runtime  
isNotBlank androidx.compose.runtime  
isNotEmpty androidx.compose.runtime  
isNullOrBlank androidx.compose.runtime  items androidx.compose.runtime  let androidx.compose.runtime  listOf androidx.compose.runtime  map androidx.compose.runtime  mutableStateOf androidx.compose.runtime  padding androidx.compose.runtime  provideDelegate androidx.compose.runtime  random androidx.compose.runtime  remember androidx.compose.runtime  rememberScrollState androidx.compose.runtime  
setContent androidx.compose.runtime  setValue androidx.compose.runtime  size androidx.compose.runtime  take androidx.compose.runtime  takeIf androidx.compose.runtime  toIntOrNull androidx.compose.runtime  trim androidx.compose.runtime  verticalScroll androidx.compose.runtime  width androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  getPROVIDEDelegate %androidx.compose.runtime.MutableState  getProvideDelegate %androidx.compose.runtime.MutableState  getSETValue %androidx.compose.runtime.MutableState  getSetValue %androidx.compose.runtime.MutableState  provideDelegate %androidx.compose.runtime.MutableState  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  getGETValue androidx.compose.runtime.State  getGetValue androidx.compose.runtime.State  getPROVIDEDelegate androidx.compose.runtime.State  getProvideDelegate androidx.compose.runtime.State  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  Top androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  Top 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  	clickable androidx.compose.ui.Modifier  clip androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  
getBACKGROUND androidx.compose.ui.Modifier  
getBackground androidx.compose.ui.Modifier  getCLICKABLE androidx.compose.ui.Modifier  getCLIP androidx.compose.ui.Modifier  getClickable androidx.compose.ui.Modifier  getClip androidx.compose.ui.Modifier  getFILLMaxWidth androidx.compose.ui.Modifier  getFillMaxWidth androidx.compose.ui.Modifier  
getPADDING androidx.compose.ui.Modifier  
getPadding androidx.compose.ui.Modifier  getVERTICALScroll androidx.compose.ui.Modifier  getVerticalScroll androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  heightIn androidx.compose.ui.Modifier  
menuAnchor androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  verticalScroll androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  getFILLMaxSize &androidx.compose.ui.Modifier.Companion  getFILLMaxWidth &androidx.compose.ui.Modifier.Companion  getFillMaxSize &androidx.compose.ui.Modifier.Companion  getFillMaxWidth &androidx.compose.ui.Modifier.Companion  	getHEIGHT &androidx.compose.ui.Modifier.Companion  getHEIGHTIn &androidx.compose.ui.Modifier.Companion  	getHeight &androidx.compose.ui.Modifier.Companion  getHeightIn &androidx.compose.ui.Modifier.Companion  
getMENUAnchor &androidx.compose.ui.Modifier.Companion  
getMenuAnchor &androidx.compose.ui.Modifier.Companion  
getPADDING &androidx.compose.ui.Modifier.Companion  
getPadding &androidx.compose.ui.Modifier.Companion  getSIZE &androidx.compose.ui.Modifier.Companion  getSize &androidx.compose.ui.Modifier.Companion  	getWEIGHT &androidx.compose.ui.Modifier.Companion  getWIDTH &androidx.compose.ui.Modifier.Companion  	getWeight &androidx.compose.ui.Modifier.Companion  getWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  heightIn &androidx.compose.ui.Modifier.Companion  
menuAnchor &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  clip androidx.compose.ui.draw  Brush androidx.compose.ui.graphics  Color androidx.compose.ui.graphics  toArgb androidx.compose.ui.graphics  verticalGradient "androidx.compose.ui.graphics.Brush  verticalGradient ,androidx.compose.ui.graphics.Brush.Companion  Gray "androidx.compose.ui.graphics.Color  copy "androidx.compose.ui.graphics.Color  	getTOArgb "androidx.compose.ui.graphics.Color  	getToArgb "androidx.compose.ui.graphics.Color  toArgb "androidx.compose.ui.graphics.Color  Gray ,androidx.compose.ui.graphics.Color.Companion  invoke ,androidx.compose.ui.graphics.Color.Companion  ImageVector #androidx.compose.ui.graphics.vector  LocalContext androidx.compose.ui.platform  	LocalView androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  invoke ,androidx.compose.ui.text.TextStyle.Companion  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  SemiBold (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  SemiBold 2androidx.compose.ui.text.font.FontWeight.Companion  	TextAlign androidx.compose.ui.text.style  TextOverflow androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  Ellipsis +androidx.compose.ui.text.style.TextOverflow  Ellipsis 5androidx.compose.ui.text.style.TextOverflow.Companion  Preview #androidx.compose.ui.tooling.preview  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  Dialog androidx.compose.ui.window  Bundle #androidx.core.app.ComponentActivity  TaskManagerApp #androidx.core.app.ComponentActivity  TaskManagerTheme #androidx.core.app.ComponentActivity  onCreate #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  WindowCompat androidx.core.view  getInsetsController androidx.core.view.WindowCompat  getISAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  getIsAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  isAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  setAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  AndroidViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  Application #androidx.lifecycle.AndroidViewModel  Boolean #androidx.lifecycle.AndroidViewModel  	Exception #androidx.lifecycle.AndroidViewModel  Int #androidx.lifecycle.AndroidViewModel  List #androidx.lifecycle.AndroidViewModel  	LocalDate #androidx.lifecycle.AndroidViewModel  Long #androidx.lifecycle.AndroidViewModel  MutableStateFlow #androidx.lifecycle.AndroidViewModel  SharingStarted #androidx.lifecycle.AndroidViewModel  	StateFlow #androidx.lifecycle.AndroidViewModel  String #androidx.lifecycle.AndroidViewModel  Task #androidx.lifecycle.AndroidViewModel  TaskDatabase #androidx.lifecycle.AndroidViewModel  TaskRepository #androidx.lifecycle.AndroidViewModel  Unit #androidx.lifecycle.AndroidViewModel  
_errorMessage #androidx.lifecycle.AndroidViewModel  
_isLoading #androidx.lifecycle.AndroidViewModel  asStateFlow #androidx.lifecycle.AndroidViewModel  
clearError #androidx.lifecycle.AndroidViewModel  copyTasksToDate #androidx.lifecycle.AndroidViewModel  
deleteTask #androidx.lifecycle.AndroidViewModel  	emptyList #androidx.lifecycle.AndroidViewModel  
flatMapLatest #androidx.lifecycle.AndroidViewModel  getWeekStart #androidx.lifecycle.AndroidViewModel  	goToToday #androidx.lifecycle.AndroidViewModel  
insertTask #androidx.lifecycle.AndroidViewModel  invoke #androidx.lifecycle.AndroidViewModel  launch #androidx.lifecycle.AndroidViewModel  navigateDate #androidx.lifecycle.AndroidViewModel  navigateWeek #androidx.lifecycle.AndroidViewModel  
repository #androidx.lifecycle.AndroidViewModel  setCurrentDate #androidx.lifecycle.AndroidViewModel  stateIn #androidx.lifecycle.AndroidViewModel  toggleTaskCompletion #androidx.lifecycle.AndroidViewModel  
updateTask #androidx.lifecycle.AndroidViewModel  viewModelScope #androidx.lifecycle.AndroidViewModel  Application androidx.lifecycle.ViewModel  Boolean androidx.lifecycle.ViewModel  	Exception androidx.lifecycle.ViewModel  Int androidx.lifecycle.ViewModel  List androidx.lifecycle.ViewModel  	LocalDate androidx.lifecycle.ViewModel  Long androidx.lifecycle.ViewModel  MutableStateFlow androidx.lifecycle.ViewModel  SharingStarted androidx.lifecycle.ViewModel  	StateFlow androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  Task androidx.lifecycle.ViewModel  TaskDatabase androidx.lifecycle.ViewModel  TaskRepository androidx.lifecycle.ViewModel  Unit androidx.lifecycle.ViewModel  
_errorMessage androidx.lifecycle.ViewModel  
_isLoading androidx.lifecycle.ViewModel  asStateFlow androidx.lifecycle.ViewModel  
clearError androidx.lifecycle.ViewModel  copyTasksToDate androidx.lifecycle.ViewModel  
deleteTask androidx.lifecycle.ViewModel  	emptyList androidx.lifecycle.ViewModel  
flatMapLatest androidx.lifecycle.ViewModel  getWeekStart androidx.lifecycle.ViewModel  	goToToday androidx.lifecycle.ViewModel  
insertTask androidx.lifecycle.ViewModel  invoke androidx.lifecycle.ViewModel  launch androidx.lifecycle.ViewModel  navigateDate androidx.lifecycle.ViewModel  navigateWeek androidx.lifecycle.ViewModel  
repository androidx.lifecycle.ViewModel  setCurrentDate androidx.lifecycle.ViewModel  stateIn androidx.lifecycle.ViewModel  toggleTaskCompletion androidx.lifecycle.ViewModel  
updateTask androidx.lifecycle.ViewModel  viewModelScope androidx.lifecycle.ViewModel  	viewModel $androidx.lifecycle.viewmodel.compose  NavBackStackEntry androidx.navigation  NavDestination androidx.navigation  NavGraph androidx.navigation  NavGraphBuilder androidx.navigation  NavHostController androidx.navigation  NavOptionsBuilder androidx.navigation  PopUpToBuilder androidx.navigation  destination %androidx.navigation.NavBackStackEntry  currentBackStackEntryAsState !androidx.navigation.NavController  navigate !androidx.navigation.NavController  	Companion "androidx.navigation.NavDestination  findStartDestination "androidx.navigation.NavDestination  getHIERARCHY "androidx.navigation.NavDestination  getHierarchy "androidx.navigation.NavDestination  	hierarchy "androidx.navigation.NavDestination  id "androidx.navigation.NavDestination  route "androidx.navigation.NavDestination  	hierarchy ,androidx.navigation.NavDestination.Companion  	Companion androidx.navigation.NavGraph  findStartDestination androidx.navigation.NavGraph  getFINDStartDestination androidx.navigation.NavGraph  getFindStartDestination androidx.navigation.NavGraph  findStartDestination &androidx.navigation.NavGraph.Companion  
DayViewScreen #androidx.navigation.NavGraphBuilder  Screen #androidx.navigation.NavGraphBuilder  WeekViewScreen #androidx.navigation.NavGraphBuilder  
composable #androidx.navigation.NavGraphBuilder  
getCOMPOSABLE #androidx.navigation.NavGraphBuilder  
getComposable #androidx.navigation.NavGraphBuilder  currentBackStackEntryAsState %androidx.navigation.NavHostController  getCURRENTBackStackEntryAsState %androidx.navigation.NavHostController  getCurrentBackStackEntryAsState %androidx.navigation.NavHostController  graph %androidx.navigation.NavHostController  navigate %androidx.navigation.NavHostController  findStartDestination %androidx.navigation.NavOptionsBuilder  getFINDStartDestination %androidx.navigation.NavOptionsBuilder  getFindStartDestination %androidx.navigation.NavOptionsBuilder  invoke %androidx.navigation.NavOptionsBuilder  launchSingleTop %androidx.navigation.NavOptionsBuilder  popUpTo %androidx.navigation.NavOptionsBuilder  restoreState %androidx.navigation.NavOptionsBuilder  	saveState "androidx.navigation.PopUpToBuilder  NavHost androidx.navigation.compose  
composable androidx.navigation.compose  currentBackStackEntryAsState androidx.navigation.compose  rememberNavController androidx.navigation.compose  Dao 
androidx.room  Database 
androidx.room  Delete 
androidx.room  Entity 
androidx.room  Insert 
androidx.room  OnConflictStrategy 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  Room 
androidx.room  RoomDatabase 
androidx.room  
TypeConverter 
androidx.room  TypeConverters 
androidx.room  Update 
androidx.room  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  databaseBuilder androidx.room.Room  Context androidx.room.RoomDatabase  Room androidx.room.RoomDatabase  TaskDao androidx.room.RoomDatabase  TaskDatabase androidx.room.RoomDatabase  Volatile androidx.room.RoomDatabase  java androidx.room.RoomDatabase  synchronized androidx.room.RoomDatabase  taskDao androidx.room.RoomDatabase  build "androidx.room.RoomDatabase.Builder  Room $androidx.room.RoomDatabase.Companion  TaskDatabase $androidx.room.RoomDatabase.Companion  getSYNCHRONIZED $androidx.room.RoomDatabase.Companion  getSynchronized $androidx.room.RoomDatabase.Companion  java $androidx.room.RoomDatabase.Companion  synchronized $androidx.room.RoomDatabase.Companion  
Composable com.taskmanager  
DayViewScreen com.taskmanager  ExperimentalMaterial3Api com.taskmanager  Icon com.taskmanager  MainActivity com.taskmanager  
NavigationBar com.taskmanager  NavigationBarItem com.taskmanager  OptIn com.taskmanager  Scaffold com.taskmanager  Screen com.taskmanager  String com.taskmanager  TaskManagerApp com.taskmanager  TaskManagerAppPreview com.taskmanager  TaskManagerTheme com.taskmanager  Text com.taskmanager  WeekViewScreen com.taskmanager  androidx com.taskmanager  any com.taskmanager  
composable com.taskmanager  currentBackStackEntryAsState com.taskmanager  fillMaxSize com.taskmanager  findStartDestination com.taskmanager  forEach com.taskmanager  getValue com.taskmanager  listOf com.taskmanager  padding com.taskmanager  provideDelegate com.taskmanager  
setContent com.taskmanager  Bundle com.taskmanager.MainActivity  TaskManagerApp com.taskmanager.MainActivity  TaskManagerTheme com.taskmanager.MainActivity  
getSETContent com.taskmanager.MainActivity  
getSetContent com.taskmanager.MainActivity  
setContent com.taskmanager.MainActivity  
CalendarToday com.taskmanager.Screen  CalendarViewWeek com.taskmanager.Screen  DayView com.taskmanager.Screen  Icons com.taskmanager.Screen  ImageVector com.taskmanager.Screen  Screen com.taskmanager.Screen  String com.taskmanager.Screen  WeekView com.taskmanager.Screen  icon com.taskmanager.Screen  route com.taskmanager.Screen  title com.taskmanager.Screen  
CalendarToday com.taskmanager.Screen.DayView  Icons com.taskmanager.Screen.DayView  route com.taskmanager.Screen.DayView  CalendarViewWeek com.taskmanager.Screen.WeekView  Icons com.taskmanager.Screen.WeekView  route com.taskmanager.Screen.WeekView  Boolean com.taskmanager.data.database  
Converters com.taskmanager.data.database  Dao com.taskmanager.data.database  Delete com.taskmanager.data.database  Insert com.taskmanager.data.database  Int com.taskmanager.data.database  List com.taskmanager.data.database  	LocalDate com.taskmanager.data.database  	LocalTime com.taskmanager.data.database  OnConflictStrategy com.taskmanager.data.database  Query com.taskmanager.data.database  Room com.taskmanager.data.database  String com.taskmanager.data.database  Task com.taskmanager.data.database  TaskCategory com.taskmanager.data.database  TaskDao com.taskmanager.data.database  TaskDatabase com.taskmanager.data.database  TaskPriority com.taskmanager.data.database  Update com.taskmanager.data.database  Volatile com.taskmanager.data.database  java com.taskmanager.data.database  let com.taskmanager.data.database  synchronized com.taskmanager.data.database  	LocalDate (com.taskmanager.data.database.Converters  	LocalTime (com.taskmanager.data.database.Converters  String (com.taskmanager.data.database.Converters  TaskCategory (com.taskmanager.data.database.Converters  TaskPriority (com.taskmanager.data.database.Converters  
TypeConverter (com.taskmanager.data.database.Converters  getLET (com.taskmanager.data.database.Converters  getLet (com.taskmanager.data.database.Converters  let (com.taskmanager.data.database.Converters  Boolean %com.taskmanager.data.database.TaskDao  Delete %com.taskmanager.data.database.TaskDao  Flow %com.taskmanager.data.database.TaskDao  Insert %com.taskmanager.data.database.TaskDao  Int %com.taskmanager.data.database.TaskDao  List %com.taskmanager.data.database.TaskDao  	LocalDate %com.taskmanager.data.database.TaskDao  OnConflictStrategy %com.taskmanager.data.database.TaskDao  Query %com.taskmanager.data.database.TaskDao  String %com.taskmanager.data.database.TaskDao  Task %com.taskmanager.data.database.TaskDao  Update %com.taskmanager.data.database.TaskDao  
deleteTask %com.taskmanager.data.database.TaskDao  deleteTaskById %com.taskmanager.data.database.TaskDao  getAllTasks %com.taskmanager.data.database.TaskDao  getTaskById %com.taskmanager.data.database.TaskDao  getTaskCountByDate %com.taskmanager.data.database.TaskDao  getTasksByDate %com.taskmanager.data.database.TaskDao  getTasksByDateRange %com.taskmanager.data.database.TaskDao  getTasksByDateSync %com.taskmanager.data.database.TaskDao  
insertTask %com.taskmanager.data.database.TaskDao  insertTasks %com.taskmanager.data.database.TaskDao  
updateTask %com.taskmanager.data.database.TaskDao  updateTaskCompletion %com.taskmanager.data.database.TaskDao  	Companion *com.taskmanager.data.database.TaskDatabase  Context *com.taskmanager.data.database.TaskDatabase  Room *com.taskmanager.data.database.TaskDatabase  TaskDao *com.taskmanager.data.database.TaskDatabase  TaskDatabase *com.taskmanager.data.database.TaskDatabase  Volatile *com.taskmanager.data.database.TaskDatabase  getDatabase *com.taskmanager.data.database.TaskDatabase  java *com.taskmanager.data.database.TaskDatabase  synchronized *com.taskmanager.data.database.TaskDatabase  taskDao *com.taskmanager.data.database.TaskDatabase  Context 4com.taskmanager.data.database.TaskDatabase.Companion  INSTANCE 4com.taskmanager.data.database.TaskDatabase.Companion  Room 4com.taskmanager.data.database.TaskDatabase.Companion  TaskDao 4com.taskmanager.data.database.TaskDatabase.Companion  TaskDatabase 4com.taskmanager.data.database.TaskDatabase.Companion  Volatile 4com.taskmanager.data.database.TaskDatabase.Companion  getDatabase 4com.taskmanager.data.database.TaskDatabase.Companion  getSYNCHRONIZED 4com.taskmanager.data.database.TaskDatabase.Companion  getSynchronized 4com.taskmanager.data.database.TaskDatabase.Companion  java 4com.taskmanager.data.database.TaskDatabase.Companion  synchronized 4com.taskmanager.data.database.TaskDatabase.Companion  Boolean com.taskmanager.data.model  Int com.taskmanager.data.model  MEDIUM com.taskmanager.data.model  OTHER com.taskmanager.data.model  String com.taskmanager.data.model  Task com.taskmanager.data.model  TaskCategory com.taskmanager.data.model  TaskPriority com.taskmanager.data.model  find com.taskmanager.data.model  values com.taskmanager.data.model  Boolean com.taskmanager.data.model.Task  Int com.taskmanager.data.model.Task  	LocalDate com.taskmanager.data.model.Task  	LocalTime com.taskmanager.data.model.Task  
PrimaryKey com.taskmanager.data.model.Task  String com.taskmanager.data.model.Task  TaskCategory com.taskmanager.data.model.Task  TaskPriority com.taskmanager.data.model.Task  category com.taskmanager.data.model.Task  	completed com.taskmanager.data.model.Task  copy com.taskmanager.data.model.Task  date com.taskmanager.data.model.Task  description com.taskmanager.data.model.Task  duration com.taskmanager.data.model.Task  equals com.taskmanager.data.model.Task  id com.taskmanager.data.model.Task  priority com.taskmanager.data.model.Task  time com.taskmanager.data.model.Task  title com.taskmanager.data.model.Task  HEALTH 'com.taskmanager.data.model.TaskCategory  OTHER 'com.taskmanager.data.model.TaskCategory  PERSONAL 'com.taskmanager.data.model.TaskCategory  STUDY 'com.taskmanager.data.model.TaskCategory  String 'com.taskmanager.data.model.TaskCategory  TaskCategory 'com.taskmanager.data.model.TaskCategory  WORK 'com.taskmanager.data.model.TaskCategory  displayName 'com.taskmanager.data.model.TaskCategory  find 'com.taskmanager.data.model.TaskCategory  	fromValue 'com.taskmanager.data.model.TaskCategory  value 'com.taskmanager.data.model.TaskCategory  values 'com.taskmanager.data.model.TaskCategory  HEALTH 1com.taskmanager.data.model.TaskCategory.Companion  OTHER 1com.taskmanager.data.model.TaskCategory.Companion  PERSONAL 1com.taskmanager.data.model.TaskCategory.Companion  STUDY 1com.taskmanager.data.model.TaskCategory.Companion  String 1com.taskmanager.data.model.TaskCategory.Companion  TaskCategory 1com.taskmanager.data.model.TaskCategory.Companion  WORK 1com.taskmanager.data.model.TaskCategory.Companion  find 1com.taskmanager.data.model.TaskCategory.Companion  	fromValue 1com.taskmanager.data.model.TaskCategory.Companion  getFIND 1com.taskmanager.data.model.TaskCategory.Companion  getFind 1com.taskmanager.data.model.TaskCategory.Companion  	getVALUES 1com.taskmanager.data.model.TaskCategory.Companion  	getValues 1com.taskmanager.data.model.TaskCategory.Companion  values 1com.taskmanager.data.model.TaskCategory.Companion  HIGH 'com.taskmanager.data.model.TaskPriority  LOW 'com.taskmanager.data.model.TaskPriority  MEDIUM 'com.taskmanager.data.model.TaskPriority  String 'com.taskmanager.data.model.TaskPriority  TaskPriority 'com.taskmanager.data.model.TaskPriority  displayName 'com.taskmanager.data.model.TaskPriority  find 'com.taskmanager.data.model.TaskPriority  	fromValue 'com.taskmanager.data.model.TaskPriority  value 'com.taskmanager.data.model.TaskPriority  values 'com.taskmanager.data.model.TaskPriority  HIGH 1com.taskmanager.data.model.TaskPriority.Companion  LOW 1com.taskmanager.data.model.TaskPriority.Companion  MEDIUM 1com.taskmanager.data.model.TaskPriority.Companion  String 1com.taskmanager.data.model.TaskPriority.Companion  TaskPriority 1com.taskmanager.data.model.TaskPriority.Companion  find 1com.taskmanager.data.model.TaskPriority.Companion  	fromValue 1com.taskmanager.data.model.TaskPriority.Companion  getFIND 1com.taskmanager.data.model.TaskPriority.Companion  getFind 1com.taskmanager.data.model.TaskPriority.Companion  	getVALUES 1com.taskmanager.data.model.TaskPriority.Companion  	getValues 1com.taskmanager.data.model.TaskPriority.Companion  values 1com.taskmanager.data.model.TaskPriority.Companion  Boolean com.taskmanager.data.repository  Int com.taskmanager.data.repository  List com.taskmanager.data.repository  String com.taskmanager.data.repository  System com.taskmanager.data.repository  TaskRepository com.taskmanager.data.repository  
isNotEmpty com.taskmanager.data.repository  map com.taskmanager.data.repository  random com.taskmanager.data.repository  Boolean .com.taskmanager.data.repository.TaskRepository  Flow .com.taskmanager.data.repository.TaskRepository  Int .com.taskmanager.data.repository.TaskRepository  List .com.taskmanager.data.repository.TaskRepository  	LocalDate .com.taskmanager.data.repository.TaskRepository  String .com.taskmanager.data.repository.TaskRepository  System .com.taskmanager.data.repository.TaskRepository  Task .com.taskmanager.data.repository.TaskRepository  TaskDao .com.taskmanager.data.repository.TaskRepository  copyTasksToDate .com.taskmanager.data.repository.TaskRepository  
deleteTask .com.taskmanager.data.repository.TaskRepository  generateTaskId .com.taskmanager.data.repository.TaskRepository  
getISNotEmpty .com.taskmanager.data.repository.TaskRepository  
getIsNotEmpty .com.taskmanager.data.repository.TaskRepository  getMAP .com.taskmanager.data.repository.TaskRepository  getMap .com.taskmanager.data.repository.TaskRepository  	getRANDOM .com.taskmanager.data.repository.TaskRepository  	getRandom .com.taskmanager.data.repository.TaskRepository  getTaskById .com.taskmanager.data.repository.TaskRepository  getTasksByDate .com.taskmanager.data.repository.TaskRepository  getTasksByDateRange .com.taskmanager.data.repository.TaskRepository  
insertTask .com.taskmanager.data.repository.TaskRepository  
isNotEmpty .com.taskmanager.data.repository.TaskRepository  map .com.taskmanager.data.repository.TaskRepository  random .com.taskmanager.data.repository.TaskRepository  taskDao .com.taskmanager.data.repository.TaskRepository  
updateTask .com.taskmanager.data.repository.TaskRepository  updateTaskCompletion .com.taskmanager.data.repository.TaskRepository  
AddTaskDialog com.taskmanager.ui.components  	Alignment com.taskmanager.ui.components  Arrangement com.taskmanager.ui.components  Boolean com.taskmanager.ui.components  Box com.taskmanager.ui.components  Button com.taskmanager.ui.components  Card com.taskmanager.ui.components  CardBackground com.taskmanager.ui.components  CardDefaults com.taskmanager.ui.components  Checkbox com.taskmanager.ui.components  Column com.taskmanager.ui.components  
Composable com.taskmanager.ui.components  CopyTasksDialog com.taskmanager.ui.components  DateOptionCard com.taskmanager.ui.components  DateTimeFormatter com.taskmanager.ui.components  DividerColor com.taskmanager.ui.components  DropdownMenuItem com.taskmanager.ui.components  
ErrorColor com.taskmanager.ui.components  ExperimentalMaterial3Api com.taskmanager.ui.components  ExposedDropdownMenuBox com.taskmanager.ui.components  ExposedDropdownMenuDefaults com.taskmanager.ui.components  
FontWeight com.taskmanager.ui.components  HealthCategoryColor com.taskmanager.ui.components  HighPriorityColor com.taskmanager.ui.components  HorizontalDivider com.taskmanager.ui.components  Icon com.taskmanager.ui.components  
IconButton com.taskmanager.ui.components  Icons com.taskmanager.ui.components  Int com.taskmanager.ui.components  
LazyColumn com.taskmanager.ui.components  	LocalTime com.taskmanager.ui.components  Locale com.taskmanager.ui.components  LowPriorityColor com.taskmanager.ui.components  
MaterialTheme com.taskmanager.ui.components  MediumPriorityColor com.taskmanager.ui.components  Modifier com.taskmanager.ui.components  OptIn com.taskmanager.ui.components  OtherCategoryColor com.taskmanager.ui.components  OutlinedButton com.taskmanager.ui.components  OutlinedTextField com.taskmanager.ui.components  PersonalCategoryColor com.taskmanager.ui.components  PrimaryBlue com.taskmanager.ui.components  RoundedCornerShape com.taskmanager.ui.components  Row com.taskmanager.ui.components  Spacer com.taskmanager.ui.components  StudyCategoryColor com.taskmanager.ui.components  Surface com.taskmanager.ui.components  System com.taskmanager.ui.components  Task com.taskmanager.ui.components  TaskCard com.taskmanager.ui.components  TaskCategory com.taskmanager.ui.components  TaskPriority com.taskmanager.ui.components  Text com.taskmanager.ui.components  	TextAlign com.taskmanager.ui.components  TextOverflow com.taskmanager.ui.components  
TextSecondary com.taskmanager.ui.components  	TextStyle com.taskmanager.ui.components  Unit com.taskmanager.ui.components  WorkCategoryColor com.taskmanager.ui.components  
background com.taskmanager.ui.components  clip com.taskmanager.ui.components  fillMaxWidth com.taskmanager.ui.components  forEach com.taskmanager.ui.components  getCategoryColor com.taskmanager.ui.components  getPriorityColor com.taskmanager.ui.components  getValue com.taskmanager.ui.components  height com.taskmanager.ui.components  heightIn com.taskmanager.ui.components  isBlank com.taskmanager.ui.components  
isNotBlank com.taskmanager.ui.components  
isNullOrBlank com.taskmanager.ui.components  items com.taskmanager.ui.components  let com.taskmanager.ui.components  map com.taskmanager.ui.components  mutableStateOf com.taskmanager.ui.components  padding com.taskmanager.ui.components  provideDelegate com.taskmanager.ui.components  random com.taskmanager.ui.components  remember com.taskmanager.ui.components  rememberScrollState com.taskmanager.ui.components  setValue com.taskmanager.ui.components  size com.taskmanager.ui.components  takeIf com.taskmanager.ui.components  toIntOrNull com.taskmanager.ui.components  trim com.taskmanager.ui.components  verticalScroll com.taskmanager.ui.components  	Alignment com.taskmanager.ui.screens  Arrangement com.taskmanager.ui.screens  Boolean com.taskmanager.ui.screens  Box com.taskmanager.ui.screens  Button com.taskmanager.ui.screens  Card com.taskmanager.ui.screens  CardBackground com.taskmanager.ui.screens  CardDefaults com.taskmanager.ui.screens  CircularProgressIndicator com.taskmanager.ui.screens  Column com.taskmanager.ui.screens  
Composable com.taskmanager.ui.screens  DateTimeFormatter com.taskmanager.ui.screens  
DayViewScreen com.taskmanager.ui.screens  ExperimentalMaterial3Api com.taskmanager.ui.screens  
FontWeight com.taskmanager.ui.screens  GradientColors com.taskmanager.ui.screens  HealthCategoryColor com.taskmanager.ui.screens  HighPriorityColor com.taskmanager.ui.screens  Icon com.taskmanager.ui.screens  
IconButton com.taskmanager.ui.screens  Icons com.taskmanager.ui.screens  
LazyColumn com.taskmanager.ui.screens  List com.taskmanager.ui.screens  	LocalDate com.taskmanager.ui.screens  Locale com.taskmanager.ui.screens  LowPriorityColor com.taskmanager.ui.screens  
MaterialTheme com.taskmanager.ui.screens  MediumPriorityColor com.taskmanager.ui.screens  Modifier com.taskmanager.ui.screens  OptIn com.taskmanager.ui.screens  OtherCategoryColor com.taskmanager.ui.screens  OutlinedButton com.taskmanager.ui.screens  PersonalCategoryColor com.taskmanager.ui.screens  PrimaryBlue com.taskmanager.ui.screens  RoundedCornerShape com.taskmanager.ui.screens  Row com.taskmanager.ui.screens  Spacer com.taskmanager.ui.screens  StudyCategoryColor com.taskmanager.ui.screens  Surface com.taskmanager.ui.screens  TaskCard com.taskmanager.ui.screens  Text com.taskmanager.ui.screens  	TextAlign com.taskmanager.ui.screens  
TextButton com.taskmanager.ui.screens  
TextSecondary com.taskmanager.ui.screens  	TextStyle com.taskmanager.ui.screens  Unit com.taskmanager.ui.screens  WeekDayCard com.taskmanager.ui.screens  WeekTaskItem com.taskmanager.ui.screens  WeekViewScreen com.taskmanager.ui.screens  WorkCategoryColor com.taskmanager.ui.screens  androidx com.taskmanager.ui.screens  
background com.taskmanager.ui.screens  collectAsState com.taskmanager.ui.screens  com com.taskmanager.ui.screens  fillMaxSize com.taskmanager.ui.screens  fillMaxWidth com.taskmanager.ui.screens  filter com.taskmanager.ui.screens  forEach com.taskmanager.ui.screens  getCategoryColor com.taskmanager.ui.screens  getPriorityColor com.taskmanager.ui.screens  getValue com.taskmanager.ui.screens  height com.taskmanager.ui.screens  
isNotEmpty com.taskmanager.ui.screens  items com.taskmanager.ui.screens  map com.taskmanager.ui.screens  mutableStateOf com.taskmanager.ui.screens  padding com.taskmanager.ui.screens  provideDelegate com.taskmanager.ui.screens  remember com.taskmanager.ui.screens  setValue com.taskmanager.ui.screens  size com.taskmanager.ui.screens  take com.taskmanager.ui.screens  width com.taskmanager.ui.screens  	Alignment com.taskmanager.ui.theme  Arrangement com.taskmanager.ui.theme  BackgroundGradientEnd com.taskmanager.ui.theme  BackgroundGradientStart com.taskmanager.ui.theme  Boolean com.taskmanager.ui.theme  Box com.taskmanager.ui.theme  Build com.taskmanager.ui.theme  Button com.taskmanager.ui.theme  Card com.taskmanager.ui.theme  CardBackground com.taskmanager.ui.theme  CardBackgroundDark com.taskmanager.ui.theme  CardDefaults com.taskmanager.ui.theme  Checkbox com.taskmanager.ui.theme  CircularProgressIndicator com.taskmanager.ui.theme  Column com.taskmanager.ui.theme  
Composable com.taskmanager.ui.theme  DarkColorScheme com.taskmanager.ui.theme  DateTimeFormatter com.taskmanager.ui.theme  DividerColor com.taskmanager.ui.theme  
ErrorColor com.taskmanager.ui.theme  ExperimentalMaterial3Api com.taskmanager.ui.theme  
FontWeight com.taskmanager.ui.theme  GradientColors com.taskmanager.ui.theme  HealthCategoryColor com.taskmanager.ui.theme  HighPriorityColor com.taskmanager.ui.theme  HorizontalDivider com.taskmanager.ui.theme  Icon com.taskmanager.ui.theme  
IconButton com.taskmanager.ui.theme  Icons com.taskmanager.ui.theme  
LazyColumn com.taskmanager.ui.theme  LightColorScheme com.taskmanager.ui.theme  	LocalDate com.taskmanager.ui.theme  Locale com.taskmanager.ui.theme  LowPriorityColor com.taskmanager.ui.theme  
MaterialTheme com.taskmanager.ui.theme  MediumPriorityColor com.taskmanager.ui.theme  Modifier com.taskmanager.ui.theme  OtherCategoryColor com.taskmanager.ui.theme  PersonalCategoryColor com.taskmanager.ui.theme  Pink40 com.taskmanager.ui.theme  Pink80 com.taskmanager.ui.theme  PrimaryBlue com.taskmanager.ui.theme  PrimaryBlueEnd com.taskmanager.ui.theme  Purple40 com.taskmanager.ui.theme  Purple80 com.taskmanager.ui.theme  PurpleGrey40 com.taskmanager.ui.theme  PurpleGrey80 com.taskmanager.ui.theme  RoundedCornerShape com.taskmanager.ui.theme  Row com.taskmanager.ui.theme  Spacer com.taskmanager.ui.theme  StudyCategoryColor com.taskmanager.ui.theme  SuccessColor com.taskmanager.ui.theme  Surface com.taskmanager.ui.theme  TaskManagerTheme com.taskmanager.ui.theme  Text com.taskmanager.ui.theme  	TextAlign com.taskmanager.ui.theme  
TextButton com.taskmanager.ui.theme  TextOverflow com.taskmanager.ui.theme  
TextSecondary com.taskmanager.ui.theme  	TextStyle com.taskmanager.ui.theme  
Typography com.taskmanager.ui.theme  Unit com.taskmanager.ui.theme  WarningColor com.taskmanager.ui.theme  WeekDayCard com.taskmanager.ui.theme  WeekTaskItem com.taskmanager.ui.theme  WindowCompat com.taskmanager.ui.theme  WorkCategoryColor com.taskmanager.ui.theme  androidx com.taskmanager.ui.theme  
background com.taskmanager.ui.theme  clip com.taskmanager.ui.theme  collectAsState com.taskmanager.ui.theme  com com.taskmanager.ui.theme  fillMaxSize com.taskmanager.ui.theme  fillMaxWidth com.taskmanager.ui.theme  filter com.taskmanager.ui.theme  forEach com.taskmanager.ui.theme  getCategoryColor com.taskmanager.ui.theme  getPriorityColor com.taskmanager.ui.theme  getValue com.taskmanager.ui.theme  height com.taskmanager.ui.theme  
isNotEmpty com.taskmanager.ui.theme  
isNullOrBlank com.taskmanager.ui.theme  items com.taskmanager.ui.theme  listOf com.taskmanager.ui.theme  map com.taskmanager.ui.theme  mutableStateOf com.taskmanager.ui.theme  padding com.taskmanager.ui.theme  provideDelegate com.taskmanager.ui.theme  remember com.taskmanager.ui.theme  setValue com.taskmanager.ui.theme  size com.taskmanager.ui.theme  take com.taskmanager.ui.theme  width com.taskmanager.ui.theme  Boolean com.taskmanager.viewmodel  	Exception com.taskmanager.viewmodel  Int com.taskmanager.viewmodel  List com.taskmanager.viewmodel  	LocalDate com.taskmanager.viewmodel  Long com.taskmanager.viewmodel  MutableStateFlow com.taskmanager.viewmodel  SharingStarted com.taskmanager.viewmodel  	StateFlow com.taskmanager.viewmodel  String com.taskmanager.viewmodel  TaskDatabase com.taskmanager.viewmodel  TaskRepository com.taskmanager.viewmodel  
TaskViewModel com.taskmanager.viewmodel  Unit com.taskmanager.viewmodel  
_errorMessage com.taskmanager.viewmodel  
_isLoading com.taskmanager.viewmodel  asStateFlow com.taskmanager.viewmodel  
clearError com.taskmanager.viewmodel  	emptyList com.taskmanager.viewmodel  
flatMapLatest com.taskmanager.viewmodel  launch com.taskmanager.viewmodel  
repository com.taskmanager.viewmodel  stateIn com.taskmanager.viewmodel  viewModelScope com.taskmanager.viewmodel  Application 'com.taskmanager.viewmodel.TaskViewModel  Boolean 'com.taskmanager.viewmodel.TaskViewModel  	Exception 'com.taskmanager.viewmodel.TaskViewModel  Int 'com.taskmanager.viewmodel.TaskViewModel  List 'com.taskmanager.viewmodel.TaskViewModel  	LocalDate 'com.taskmanager.viewmodel.TaskViewModel  Long 'com.taskmanager.viewmodel.TaskViewModel  MutableStateFlow 'com.taskmanager.viewmodel.TaskViewModel  SharingStarted 'com.taskmanager.viewmodel.TaskViewModel  	StateFlow 'com.taskmanager.viewmodel.TaskViewModel  String 'com.taskmanager.viewmodel.TaskViewModel  Task 'com.taskmanager.viewmodel.TaskViewModel  TaskDatabase 'com.taskmanager.viewmodel.TaskViewModel  TaskRepository 'com.taskmanager.viewmodel.TaskViewModel  Unit 'com.taskmanager.viewmodel.TaskViewModel  _currentDate 'com.taskmanager.viewmodel.TaskViewModel  
_errorMessage 'com.taskmanager.viewmodel.TaskViewModel  
_isLoading 'com.taskmanager.viewmodel.TaskViewModel  
_selectedTask 'com.taskmanager.viewmodel.TaskViewModel  asStateFlow 'com.taskmanager.viewmodel.TaskViewModel  
clearError 'com.taskmanager.viewmodel.TaskViewModel  copyTasksToDate 'com.taskmanager.viewmodel.TaskViewModel  currentDate 'com.taskmanager.viewmodel.TaskViewModel  currentDayTasks 'com.taskmanager.viewmodel.TaskViewModel  currentWeekTasks 'com.taskmanager.viewmodel.TaskViewModel  
deleteTask 'com.taskmanager.viewmodel.TaskViewModel  	emptyList 'com.taskmanager.viewmodel.TaskViewModel  
flatMapLatest 'com.taskmanager.viewmodel.TaskViewModel  getASStateFlow 'com.taskmanager.viewmodel.TaskViewModel  getAsStateFlow 'com.taskmanager.viewmodel.TaskViewModel  getEMPTYList 'com.taskmanager.viewmodel.TaskViewModel  getEmptyList 'com.taskmanager.viewmodel.TaskViewModel  getFLATMapLatest 'com.taskmanager.viewmodel.TaskViewModel  getFlatMapLatest 'com.taskmanager.viewmodel.TaskViewModel  	getLAUNCH 'com.taskmanager.viewmodel.TaskViewModel  	getLaunch 'com.taskmanager.viewmodel.TaskViewModel  
getSTATEIn 'com.taskmanager.viewmodel.TaskViewModel  
getStateIn 'com.taskmanager.viewmodel.TaskViewModel  getVIEWModelScope 'com.taskmanager.viewmodel.TaskViewModel  getViewModelScope 'com.taskmanager.viewmodel.TaskViewModel  getWeekStart 'com.taskmanager.viewmodel.TaskViewModel  	goToToday 'com.taskmanager.viewmodel.TaskViewModel  
insertTask 'com.taskmanager.viewmodel.TaskViewModel  invoke 'com.taskmanager.viewmodel.TaskViewModel  	isLoading 'com.taskmanager.viewmodel.TaskViewModel  launch 'com.taskmanager.viewmodel.TaskViewModel  navigateDate 'com.taskmanager.viewmodel.TaskViewModel  navigateWeek 'com.taskmanager.viewmodel.TaskViewModel  
repository 'com.taskmanager.viewmodel.TaskViewModel  setCurrentDate 'com.taskmanager.viewmodel.TaskViewModel  stateIn 'com.taskmanager.viewmodel.TaskViewModel  toggleTaskCompletion 'com.taskmanager.viewmodel.TaskViewModel  
updateTask 'com.taskmanager.viewmodel.TaskViewModel  viewModelScope 'com.taskmanager.viewmodel.TaskViewModel  	Alignment 	java.lang  Arrangement 	java.lang  Box 	java.lang  Build 	java.lang  Button 	java.lang  Card 	java.lang  CardDefaults 	java.lang  Checkbox 	java.lang  CircularProgressIndicator 	java.lang  Class 	java.lang  Column 	java.lang  
Converters 	java.lang  DateOptionCard 	java.lang  DateTimeFormatter 	java.lang  
DayViewScreen 	java.lang  DividerColor 	java.lang  DropdownMenuItem 	java.lang  
ErrorColor 	java.lang  	Exception 	java.lang  ExperimentalMaterial3Api 	java.lang  ExposedDropdownMenuBox 	java.lang  ExposedDropdownMenuDefaults 	java.lang  
FontWeight 	java.lang  HorizontalDivider 	java.lang  Icon 	java.lang  
IconButton 	java.lang  Icons 	java.lang  
LazyColumn 	java.lang  	LocalDate 	java.lang  	LocalTime 	java.lang  Locale 	java.lang  MEDIUM 	java.lang  
MaterialTheme 	java.lang  Modifier 	java.lang  MutableStateFlow 	java.lang  OTHER 	java.lang  OnConflictStrategy 	java.lang  OutlinedButton 	java.lang  OutlinedTextField 	java.lang  PrimaryBlue 	java.lang  Room 	java.lang  RoundedCornerShape 	java.lang  Row 	java.lang  Screen 	java.lang  SharingStarted 	java.lang  Spacer 	java.lang  Surface 	java.lang  System 	java.lang  Task 	java.lang  TaskCard 	java.lang  TaskCategory 	java.lang  TaskDatabase 	java.lang  TaskManagerApp 	java.lang  TaskManagerTheme 	java.lang  TaskPriority 	java.lang  TaskRepository 	java.lang  Text 	java.lang  	TextAlign 	java.lang  
TextButton 	java.lang  TextOverflow 	java.lang  
TextSecondary 	java.lang  	TextStyle 	java.lang  WeekDayCard 	java.lang  WeekTaskItem 	java.lang  WeekViewScreen 	java.lang  WindowCompat 	java.lang  
_errorMessage 	java.lang  
_isLoading 	java.lang  androidx 	java.lang  any 	java.lang  asStateFlow 	java.lang  
background 	java.lang  
clearError 	java.lang  clip 	java.lang  com 	java.lang  currentBackStackEntryAsState 	java.lang  	emptyList 	java.lang  fillMaxSize 	java.lang  fillMaxWidth 	java.lang  filter 	java.lang  find 	java.lang  findStartDestination 	java.lang  
flatMapLatest 	java.lang  forEach 	java.lang  getCategoryColor 	java.lang  getPriorityColor 	java.lang  getValue 	java.lang  height 	java.lang  heightIn 	java.lang  isBlank 	java.lang  
isNotBlank 	java.lang  
isNotEmpty 	java.lang  
isNullOrBlank 	java.lang  java 	java.lang  launch 	java.lang  let 	java.lang  listOf 	java.lang  map 	java.lang  mutableStateOf 	java.lang  padding 	java.lang  provideDelegate 	java.lang  random 	java.lang  remember 	java.lang  rememberScrollState 	java.lang  
repository 	java.lang  setValue 	java.lang  size 	java.lang  stateIn 	java.lang  synchronized 	java.lang  take 	java.lang  takeIf 	java.lang  toIntOrNull 	java.lang  trim 	java.lang  values 	java.lang  verticalScroll 	java.lang  width 	java.lang  message java.lang.Exception  currentTimeMillis java.lang.System  	LocalDate 	java.time  	LocalTime 	java.time  getDisplayName java.time.DayOfWeek  getVALUE java.time.DayOfWeek  getValue java.time.DayOfWeek  setValue java.time.DayOfWeek  value java.time.DayOfWeek  
dayOfMonth java.time.LocalDate  	dayOfWeek java.time.LocalDate  equals java.time.LocalDate  format java.time.LocalDate  
getDAYOfMonth java.time.LocalDate  getDAYOfWeek java.time.LocalDate  
getDayOfMonth java.time.LocalDate  getDayOfWeek java.time.LocalDate  getLET java.time.LocalDate  getLet java.time.LocalDate  let java.time.LocalDate  	minusDays java.time.LocalDate  now java.time.LocalDate  parse java.time.LocalDate  plusDays java.time.LocalDate  	plusWeeks java.time.LocalDate  
setDayOfMonth java.time.LocalDate  setDayOfWeek java.time.LocalDate  toString java.time.LocalDate  format java.time.LocalTime  of java.time.LocalTime  parse java.time.LocalTime  toString java.time.LocalTime  DateTimeFormatter java.time.format  	TextStyle java.time.format  	ofPattern "java.time.format.DateTimeFormatter  FULL java.time.format.TextStyle  SHORT java.time.format.TextStyle  	Alignment 	java.util  Arrangement 	java.util  Box 	java.util  Button 	java.util  Card 	java.util  CardBackground 	java.util  CardDefaults 	java.util  CircularProgressIndicator 	java.util  Column 	java.util  
Composable 	java.util  DateOptionCard 	java.util  DateTimeFormatter 	java.util  ExperimentalMaterial3Api 	java.util  
FontWeight 	java.util  GradientColors 	java.util  HealthCategoryColor 	java.util  HighPriorityColor 	java.util  Icon 	java.util  
IconButton 	java.util  Icons 	java.util  
LazyColumn 	java.util  	LocalDate 	java.util  Locale 	java.util  LowPriorityColor 	java.util  
MaterialTheme 	java.util  MediumPriorityColor 	java.util  Modifier 	java.util  OtherCategoryColor 	java.util  OutlinedButton 	java.util  PersonalCategoryColor 	java.util  PrimaryBlue 	java.util  RoundedCornerShape 	java.util  Row 	java.util  Spacer 	java.util  StudyCategoryColor 	java.util  Surface 	java.util  TaskCard 	java.util  Text 	java.util  	TextAlign 	java.util  
TextButton 	java.util  
TextSecondary 	java.util  	TextStyle 	java.util  WeekDayCard 	java.util  WeekTaskItem 	java.util  WorkCategoryColor 	java.util  androidx 	java.util  
background 	java.util  collectAsState 	java.util  com 	java.util  fillMaxSize 	java.util  fillMaxWidth 	java.util  filter 	java.util  forEach 	java.util  getPriorityColor 	java.util  getValue 	java.util  height 	java.util  heightIn 	java.util  
isNotEmpty 	java.util  items 	java.util  let 	java.util  map 	java.util  mutableStateOf 	java.util  padding 	java.util  provideDelegate 	java.util  remember 	java.util  setValue 	java.util  size 	java.util  take 	java.util  width 	java.util  CHINESE java.util.Locale  	Alignment kotlin  Arrangement kotlin  Array kotlin  Boolean kotlin  Box kotlin  Build kotlin  Button kotlin  Card kotlin  CardDefaults kotlin  Checkbox kotlin  CircularProgressIndicator kotlin  Column kotlin  
Converters kotlin  DateOptionCard kotlin  DateTimeFormatter kotlin  
DayViewScreen kotlin  DividerColor kotlin  Double kotlin  DropdownMenuItem kotlin  
ErrorColor kotlin  	Exception kotlin  ExperimentalMaterial3Api kotlin  ExposedDropdownMenuBox kotlin  ExposedDropdownMenuDefaults kotlin  Float kotlin  
FontWeight kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  HorizontalDivider kotlin  Icon kotlin  
IconButton kotlin  Icons kotlin  Int kotlin  
LazyColumn kotlin  	LocalDate kotlin  	LocalTime kotlin  Locale kotlin  Long kotlin  MEDIUM kotlin  
MaterialTheme kotlin  Modifier kotlin  MutableStateFlow kotlin  Nothing kotlin  OTHER kotlin  OnConflictStrategy kotlin  OptIn kotlin  OutlinedButton kotlin  OutlinedTextField kotlin  PrimaryBlue kotlin  Room kotlin  RoundedCornerShape kotlin  Row kotlin  Screen kotlin  SharingStarted kotlin  Spacer kotlin  String kotlin  Surface kotlin  System kotlin  Task kotlin  TaskCard kotlin  TaskCategory kotlin  TaskDatabase kotlin  TaskManagerApp kotlin  TaskManagerTheme kotlin  TaskPriority kotlin  TaskRepository kotlin  Text kotlin  	TextAlign kotlin  
TextButton kotlin  TextOverflow kotlin  
TextSecondary kotlin  	TextStyle kotlin  Unit kotlin  Volatile kotlin  WeekDayCard kotlin  WeekTaskItem kotlin  WeekViewScreen kotlin  WindowCompat kotlin  
_errorMessage kotlin  
_isLoading kotlin  androidx kotlin  any kotlin  arrayOf kotlin  asStateFlow kotlin  
background kotlin  
clearError kotlin  clip kotlin  com kotlin  currentBackStackEntryAsState kotlin  	emptyList kotlin  fillMaxSize kotlin  fillMaxWidth kotlin  filter kotlin  find kotlin  findStartDestination kotlin  
flatMapLatest kotlin  forEach kotlin  getCategoryColor kotlin  getPriorityColor kotlin  getValue kotlin  height kotlin  heightIn kotlin  isBlank kotlin  
isNotBlank kotlin  
isNotEmpty kotlin  
isNullOrBlank kotlin  java kotlin  launch kotlin  let kotlin  listOf kotlin  map kotlin  mutableStateOf kotlin  padding kotlin  provideDelegate kotlin  random kotlin  remember kotlin  rememberScrollState kotlin  
repository kotlin  setValue kotlin  size kotlin  stateIn kotlin  synchronized kotlin  take kotlin  takeIf kotlin  toIntOrNull kotlin  trim kotlin  values kotlin  verticalScroll kotlin  width kotlin  getFIND kotlin.Array  
getFOREach kotlin.Array  getFind kotlin.Array  
getForEach kotlin.Array  getSP 
kotlin.Double  getSp 
kotlin.Double  getFIND kotlin.Enum.Companion  getFind kotlin.Enum.Companion  getDP 
kotlin.Int  getDp 
kotlin.Int  getSP 
kotlin.Int  getSp 
kotlin.Int  
getISBlank 
kotlin.String  
getISNotBlank 
kotlin.String  getISNullOrBlank 
kotlin.String  
getIsBlank 
kotlin.String  
getIsNotBlank 
kotlin.String  getIsNullOrBlank 
kotlin.String  getLET 
kotlin.String  getLet 
kotlin.String  	getTAKEIf 
kotlin.String  getTOIntOrNull 
kotlin.String  getTRIM 
kotlin.String  	getTakeIf 
kotlin.String  getToIntOrNull 
kotlin.String  getTrim 
kotlin.String  isBlank 
kotlin.String  
isNotBlank 
kotlin.String  
isNullOrBlank 
kotlin.String  	Alignment kotlin.annotation  Arrangement kotlin.annotation  Box kotlin.annotation  Build kotlin.annotation  Button kotlin.annotation  Card kotlin.annotation  CardDefaults kotlin.annotation  Checkbox kotlin.annotation  CircularProgressIndicator kotlin.annotation  Column kotlin.annotation  
Converters kotlin.annotation  DateOptionCard kotlin.annotation  DateTimeFormatter kotlin.annotation  
DayViewScreen kotlin.annotation  DividerColor kotlin.annotation  DropdownMenuItem kotlin.annotation  
ErrorColor kotlin.annotation  	Exception kotlin.annotation  ExperimentalMaterial3Api kotlin.annotation  ExposedDropdownMenuBox kotlin.annotation  ExposedDropdownMenuDefaults kotlin.annotation  
FontWeight kotlin.annotation  HorizontalDivider kotlin.annotation  Icon kotlin.annotation  
IconButton kotlin.annotation  Icons kotlin.annotation  
LazyColumn kotlin.annotation  	LocalDate kotlin.annotation  	LocalTime kotlin.annotation  Locale kotlin.annotation  MEDIUM kotlin.annotation  
MaterialTheme kotlin.annotation  Modifier kotlin.annotation  MutableStateFlow kotlin.annotation  OTHER kotlin.annotation  OnConflictStrategy kotlin.annotation  OutlinedButton kotlin.annotation  OutlinedTextField kotlin.annotation  PrimaryBlue kotlin.annotation  Room kotlin.annotation  RoundedCornerShape kotlin.annotation  Row kotlin.annotation  Screen kotlin.annotation  SharingStarted kotlin.annotation  Spacer kotlin.annotation  Surface kotlin.annotation  System kotlin.annotation  Task kotlin.annotation  TaskCard kotlin.annotation  TaskCategory kotlin.annotation  TaskDatabase kotlin.annotation  TaskManagerApp kotlin.annotation  TaskManagerTheme kotlin.annotation  TaskPriority kotlin.annotation  TaskRepository kotlin.annotation  Text kotlin.annotation  	TextAlign kotlin.annotation  
TextButton kotlin.annotation  TextOverflow kotlin.annotation  
TextSecondary kotlin.annotation  	TextStyle kotlin.annotation  Volatile kotlin.annotation  WeekDayCard kotlin.annotation  WeekTaskItem kotlin.annotation  WeekViewScreen kotlin.annotation  WindowCompat kotlin.annotation  
_errorMessage kotlin.annotation  
_isLoading kotlin.annotation  androidx kotlin.annotation  any kotlin.annotation  asStateFlow kotlin.annotation  
background kotlin.annotation  
clearError kotlin.annotation  clip kotlin.annotation  com kotlin.annotation  currentBackStackEntryAsState kotlin.annotation  	emptyList kotlin.annotation  fillMaxSize kotlin.annotation  fillMaxWidth kotlin.annotation  filter kotlin.annotation  find kotlin.annotation  findStartDestination kotlin.annotation  
flatMapLatest kotlin.annotation  forEach kotlin.annotation  getCategoryColor kotlin.annotation  getPriorityColor kotlin.annotation  getValue kotlin.annotation  height kotlin.annotation  heightIn kotlin.annotation  isBlank kotlin.annotation  
isNotBlank kotlin.annotation  
isNotEmpty kotlin.annotation  
isNullOrBlank kotlin.annotation  java kotlin.annotation  launch kotlin.annotation  let kotlin.annotation  listOf kotlin.annotation  map kotlin.annotation  mutableStateOf kotlin.annotation  padding kotlin.annotation  provideDelegate kotlin.annotation  random kotlin.annotation  remember kotlin.annotation  rememberScrollState kotlin.annotation  
repository kotlin.annotation  setValue kotlin.annotation  size kotlin.annotation  stateIn kotlin.annotation  synchronized kotlin.annotation  take kotlin.annotation  takeIf kotlin.annotation  toIntOrNull kotlin.annotation  trim kotlin.annotation  values kotlin.annotation  verticalScroll kotlin.annotation  width kotlin.annotation  	Alignment kotlin.collections  Arrangement kotlin.collections  Box kotlin.collections  Build kotlin.collections  Button kotlin.collections  Card kotlin.collections  CardDefaults kotlin.collections  Checkbox kotlin.collections  CircularProgressIndicator kotlin.collections  Column kotlin.collections  
Converters kotlin.collections  DateOptionCard kotlin.collections  DateTimeFormatter kotlin.collections  
DayViewScreen kotlin.collections  DividerColor kotlin.collections  DropdownMenuItem kotlin.collections  
ErrorColor kotlin.collections  	Exception kotlin.collections  ExperimentalMaterial3Api kotlin.collections  ExposedDropdownMenuBox kotlin.collections  ExposedDropdownMenuDefaults kotlin.collections  
FontWeight kotlin.collections  HorizontalDivider kotlin.collections  Icon kotlin.collections  
IconButton kotlin.collections  Icons kotlin.collections  
LazyColumn kotlin.collections  List kotlin.collections  	LocalDate kotlin.collections  	LocalTime kotlin.collections  Locale kotlin.collections  MEDIUM kotlin.collections  
MaterialTheme kotlin.collections  Modifier kotlin.collections  MutableStateFlow kotlin.collections  OTHER kotlin.collections  OnConflictStrategy kotlin.collections  OutlinedButton kotlin.collections  OutlinedTextField kotlin.collections  PrimaryBlue kotlin.collections  Room kotlin.collections  RoundedCornerShape kotlin.collections  Row kotlin.collections  Screen kotlin.collections  SharingStarted kotlin.collections  Spacer kotlin.collections  Surface kotlin.collections  System kotlin.collections  Task kotlin.collections  TaskCard kotlin.collections  TaskCategory kotlin.collections  TaskDatabase kotlin.collections  TaskManagerApp kotlin.collections  TaskManagerTheme kotlin.collections  TaskPriority kotlin.collections  TaskRepository kotlin.collections  Text kotlin.collections  	TextAlign kotlin.collections  
TextButton kotlin.collections  TextOverflow kotlin.collections  
TextSecondary kotlin.collections  	TextStyle kotlin.collections  Volatile kotlin.collections  WeekDayCard kotlin.collections  WeekTaskItem kotlin.collections  WeekViewScreen kotlin.collections  WindowCompat kotlin.collections  
_errorMessage kotlin.collections  
_isLoading kotlin.collections  androidx kotlin.collections  any kotlin.collections  asStateFlow kotlin.collections  
background kotlin.collections  
clearError kotlin.collections  clip kotlin.collections  com kotlin.collections  currentBackStackEntryAsState kotlin.collections  	emptyList kotlin.collections  fillMaxSize kotlin.collections  fillMaxWidth kotlin.collections  filter kotlin.collections  find kotlin.collections  findStartDestination kotlin.collections  
flatMapLatest kotlin.collections  forEach kotlin.collections  getCategoryColor kotlin.collections  getPriorityColor kotlin.collections  getValue kotlin.collections  height kotlin.collections  heightIn kotlin.collections  isBlank kotlin.collections  
isNotBlank kotlin.collections  
isNotEmpty kotlin.collections  
isNullOrBlank kotlin.collections  java kotlin.collections  launch kotlin.collections  let kotlin.collections  listOf kotlin.collections  map kotlin.collections  mutableStateOf kotlin.collections  padding kotlin.collections  provideDelegate kotlin.collections  random kotlin.collections  remember kotlin.collections  rememberScrollState kotlin.collections  
repository kotlin.collections  setValue kotlin.collections  size kotlin.collections  stateIn kotlin.collections  synchronized kotlin.collections  take kotlin.collections  takeIf kotlin.collections  toIntOrNull kotlin.collections  trim kotlin.collections  values kotlin.collections  verticalScroll kotlin.collections  width kotlin.collections  	getFILTER kotlin.collections.List  	getFilter kotlin.collections.List  
getISNotEmpty kotlin.collections.List  
getIsNotEmpty kotlin.collections.List  getMAP kotlin.collections.List  getMap kotlin.collections.List  getTAKE kotlin.collections.List  getTake kotlin.collections.List  
isNotEmpty kotlin.collections.List  	Alignment kotlin.comparisons  Arrangement kotlin.comparisons  Box kotlin.comparisons  Build kotlin.comparisons  Button kotlin.comparisons  Card kotlin.comparisons  CardDefaults kotlin.comparisons  Checkbox kotlin.comparisons  CircularProgressIndicator kotlin.comparisons  Column kotlin.comparisons  
Converters kotlin.comparisons  DateOptionCard kotlin.comparisons  DateTimeFormatter kotlin.comparisons  
DayViewScreen kotlin.comparisons  DividerColor kotlin.comparisons  DropdownMenuItem kotlin.comparisons  
ErrorColor kotlin.comparisons  	Exception kotlin.comparisons  ExperimentalMaterial3Api kotlin.comparisons  ExposedDropdownMenuBox kotlin.comparisons  ExposedDropdownMenuDefaults kotlin.comparisons  
FontWeight kotlin.comparisons  HorizontalDivider kotlin.comparisons  Icon kotlin.comparisons  
IconButton kotlin.comparisons  Icons kotlin.comparisons  
LazyColumn kotlin.comparisons  	LocalDate kotlin.comparisons  	LocalTime kotlin.comparisons  Locale kotlin.comparisons  MEDIUM kotlin.comparisons  
MaterialTheme kotlin.comparisons  Modifier kotlin.comparisons  MutableStateFlow kotlin.comparisons  OTHER kotlin.comparisons  OnConflictStrategy kotlin.comparisons  OutlinedButton kotlin.comparisons  OutlinedTextField kotlin.comparisons  PrimaryBlue kotlin.comparisons  Room kotlin.comparisons  RoundedCornerShape kotlin.comparisons  Row kotlin.comparisons  Screen kotlin.comparisons  SharingStarted kotlin.comparisons  Spacer kotlin.comparisons  Surface kotlin.comparisons  System kotlin.comparisons  Task kotlin.comparisons  TaskCard kotlin.comparisons  TaskCategory kotlin.comparisons  TaskDatabase kotlin.comparisons  TaskManagerApp kotlin.comparisons  TaskManagerTheme kotlin.comparisons  TaskPriority kotlin.comparisons  TaskRepository kotlin.comparisons  Text kotlin.comparisons  	TextAlign kotlin.comparisons  
TextButton kotlin.comparisons  TextOverflow kotlin.comparisons  
TextSecondary kotlin.comparisons  	TextStyle kotlin.comparisons  Volatile kotlin.comparisons  WeekDayCard kotlin.comparisons  WeekTaskItem kotlin.comparisons  WeekViewScreen kotlin.comparisons  WindowCompat kotlin.comparisons  
_errorMessage kotlin.comparisons  
_isLoading kotlin.comparisons  androidx kotlin.comparisons  any kotlin.comparisons  asStateFlow kotlin.comparisons  
background kotlin.comparisons  
clearError kotlin.comparisons  clip kotlin.comparisons  com kotlin.comparisons  currentBackStackEntryAsState kotlin.comparisons  	emptyList kotlin.comparisons  fillMaxSize kotlin.comparisons  fillMaxWidth kotlin.comparisons  filter kotlin.comparisons  find kotlin.comparisons  findStartDestination kotlin.comparisons  
flatMapLatest kotlin.comparisons  forEach kotlin.comparisons  getCategoryColor kotlin.comparisons  getPriorityColor kotlin.comparisons  getValue kotlin.comparisons  height kotlin.comparisons  heightIn kotlin.comparisons  isBlank kotlin.comparisons  
isNotBlank kotlin.comparisons  
isNotEmpty kotlin.comparisons  
isNullOrBlank kotlin.comparisons  java kotlin.comparisons  launch kotlin.comparisons  let kotlin.comparisons  listOf kotlin.comparisons  map kotlin.comparisons  mutableStateOf kotlin.comparisons  padding kotlin.comparisons  provideDelegate kotlin.comparisons  random kotlin.comparisons  remember kotlin.comparisons  rememberScrollState kotlin.comparisons  
repository kotlin.comparisons  setValue kotlin.comparisons  size kotlin.comparisons  stateIn kotlin.comparisons  synchronized kotlin.comparisons  take kotlin.comparisons  takeIf kotlin.comparisons  toIntOrNull kotlin.comparisons  trim kotlin.comparisons  values kotlin.comparisons  verticalScroll kotlin.comparisons  width kotlin.comparisons  SuspendFunction1 kotlin.coroutines  	Alignment 	kotlin.io  Arrangement 	kotlin.io  Box 	kotlin.io  Build 	kotlin.io  Button 	kotlin.io  Card 	kotlin.io  CardDefaults 	kotlin.io  Checkbox 	kotlin.io  CircularProgressIndicator 	kotlin.io  Column 	kotlin.io  
Converters 	kotlin.io  DateOptionCard 	kotlin.io  DateTimeFormatter 	kotlin.io  
DayViewScreen 	kotlin.io  DividerColor 	kotlin.io  DropdownMenuItem 	kotlin.io  
ErrorColor 	kotlin.io  	Exception 	kotlin.io  ExperimentalMaterial3Api 	kotlin.io  ExposedDropdownMenuBox 	kotlin.io  ExposedDropdownMenuDefaults 	kotlin.io  
FontWeight 	kotlin.io  HorizontalDivider 	kotlin.io  Icon 	kotlin.io  
IconButton 	kotlin.io  Icons 	kotlin.io  
LazyColumn 	kotlin.io  	LocalDate 	kotlin.io  	LocalTime 	kotlin.io  Locale 	kotlin.io  MEDIUM 	kotlin.io  
MaterialTheme 	kotlin.io  Modifier 	kotlin.io  MutableStateFlow 	kotlin.io  OTHER 	kotlin.io  OnConflictStrategy 	kotlin.io  OutlinedButton 	kotlin.io  OutlinedTextField 	kotlin.io  PrimaryBlue 	kotlin.io  Room 	kotlin.io  RoundedCornerShape 	kotlin.io  Row 	kotlin.io  Screen 	kotlin.io  SharingStarted 	kotlin.io  Spacer 	kotlin.io  Surface 	kotlin.io  System 	kotlin.io  Task 	kotlin.io  TaskCard 	kotlin.io  TaskCategory 	kotlin.io  TaskDatabase 	kotlin.io  TaskManagerApp 	kotlin.io  TaskManagerTheme 	kotlin.io  TaskPriority 	kotlin.io  TaskRepository 	kotlin.io  Text 	kotlin.io  	TextAlign 	kotlin.io  
TextButton 	kotlin.io  TextOverflow 	kotlin.io  
TextSecondary 	kotlin.io  	TextStyle 	kotlin.io  Volatile 	kotlin.io  WeekDayCard 	kotlin.io  WeekTaskItem 	kotlin.io  WeekViewScreen 	kotlin.io  WindowCompat 	kotlin.io  
_errorMessage 	kotlin.io  
_isLoading 	kotlin.io  androidx 	kotlin.io  any 	kotlin.io  asStateFlow 	kotlin.io  
background 	kotlin.io  
clearError 	kotlin.io  clip 	kotlin.io  com 	kotlin.io  currentBackStackEntryAsState 	kotlin.io  	emptyList 	kotlin.io  fillMaxSize 	kotlin.io  fillMaxWidth 	kotlin.io  filter 	kotlin.io  find 	kotlin.io  findStartDestination 	kotlin.io  
flatMapLatest 	kotlin.io  forEach 	kotlin.io  getCategoryColor 	kotlin.io  getPriorityColor 	kotlin.io  getValue 	kotlin.io  height 	kotlin.io  heightIn 	kotlin.io  isBlank 	kotlin.io  
isNotBlank 	kotlin.io  
isNotEmpty 	kotlin.io  
isNullOrBlank 	kotlin.io  java 	kotlin.io  launch 	kotlin.io  let 	kotlin.io  listOf 	kotlin.io  map 	kotlin.io  mutableStateOf 	kotlin.io  padding 	kotlin.io  provideDelegate 	kotlin.io  random 	kotlin.io  remember 	kotlin.io  rememberScrollState 	kotlin.io  
repository 	kotlin.io  setValue 	kotlin.io  size 	kotlin.io  stateIn 	kotlin.io  synchronized 	kotlin.io  take 	kotlin.io  takeIf 	kotlin.io  toIntOrNull 	kotlin.io  trim 	kotlin.io  values 	kotlin.io  verticalScroll 	kotlin.io  width 	kotlin.io  	Alignment 
kotlin.jvm  Arrangement 
kotlin.jvm  Box 
kotlin.jvm  Build 
kotlin.jvm  Button 
kotlin.jvm  Card 
kotlin.jvm  CardDefaults 
kotlin.jvm  Checkbox 
kotlin.jvm  CircularProgressIndicator 
kotlin.jvm  Column 
kotlin.jvm  
Converters 
kotlin.jvm  DateOptionCard 
kotlin.jvm  DateTimeFormatter 
kotlin.jvm  
DayViewScreen 
kotlin.jvm  DividerColor 
kotlin.jvm  DropdownMenuItem 
kotlin.jvm  
ErrorColor 
kotlin.jvm  	Exception 
kotlin.jvm  ExperimentalMaterial3Api 
kotlin.jvm  ExposedDropdownMenuBox 
kotlin.jvm  ExposedDropdownMenuDefaults 
kotlin.jvm  
FontWeight 
kotlin.jvm  HorizontalDivider 
kotlin.jvm  Icon 
kotlin.jvm  
IconButton 
kotlin.jvm  Icons 
kotlin.jvm  
LazyColumn 
kotlin.jvm  	LocalDate 
kotlin.jvm  	LocalTime 
kotlin.jvm  Locale 
kotlin.jvm  MEDIUM 
kotlin.jvm  
MaterialTheme 
kotlin.jvm  Modifier 
kotlin.jvm  MutableStateFlow 
kotlin.jvm  OTHER 
kotlin.jvm  OnConflictStrategy 
kotlin.jvm  OutlinedButton 
kotlin.jvm  OutlinedTextField 
kotlin.jvm  PrimaryBlue 
kotlin.jvm  Room 
kotlin.jvm  RoundedCornerShape 
kotlin.jvm  Row 
kotlin.jvm  Screen 
kotlin.jvm  SharingStarted 
kotlin.jvm  Spacer 
kotlin.jvm  Surface 
kotlin.jvm  System 
kotlin.jvm  Task 
kotlin.jvm  TaskCard 
kotlin.jvm  TaskCategory 
kotlin.jvm  TaskDatabase 
kotlin.jvm  TaskManagerApp 
kotlin.jvm  TaskManagerTheme 
kotlin.jvm  TaskPriority 
kotlin.jvm  TaskRepository 
kotlin.jvm  Text 
kotlin.jvm  	TextAlign 
kotlin.jvm  
TextButton 
kotlin.jvm  TextOverflow 
kotlin.jvm  
TextSecondary 
kotlin.jvm  	TextStyle 
kotlin.jvm  Volatile 
kotlin.jvm  WeekDayCard 
kotlin.jvm  WeekTaskItem 
kotlin.jvm  WeekViewScreen 
kotlin.jvm  WindowCompat 
kotlin.jvm  
_errorMessage 
kotlin.jvm  
_isLoading 
kotlin.jvm  androidx 
kotlin.jvm  any 
kotlin.jvm  asStateFlow 
kotlin.jvm  
background 
kotlin.jvm  
clearError 
kotlin.jvm  clip 
kotlin.jvm  com 
kotlin.jvm  currentBackStackEntryAsState 
kotlin.jvm  	emptyList 
kotlin.jvm  fillMaxSize 
kotlin.jvm  fillMaxWidth 
kotlin.jvm  filter 
kotlin.jvm  find 
kotlin.jvm  findStartDestination 
kotlin.jvm  
flatMapLatest 
kotlin.jvm  forEach 
kotlin.jvm  getCategoryColor 
kotlin.jvm  getPriorityColor 
kotlin.jvm  getValue 
kotlin.jvm  height 
kotlin.jvm  heightIn 
kotlin.jvm  isBlank 
kotlin.jvm  
isNotBlank 
kotlin.jvm  
isNotEmpty 
kotlin.jvm  
isNullOrBlank 
kotlin.jvm  java 
kotlin.jvm  launch 
kotlin.jvm  let 
kotlin.jvm  listOf 
kotlin.jvm  map 
kotlin.jvm  mutableStateOf 
kotlin.jvm  padding 
kotlin.jvm  provideDelegate 
kotlin.jvm  random 
kotlin.jvm  remember 
kotlin.jvm  rememberScrollState 
kotlin.jvm  
repository 
kotlin.jvm  setValue 
kotlin.jvm  size 
kotlin.jvm  stateIn 
kotlin.jvm  synchronized 
kotlin.jvm  take 
kotlin.jvm  takeIf 
kotlin.jvm  toIntOrNull 
kotlin.jvm  trim 
kotlin.jvm  values 
kotlin.jvm  verticalScroll 
kotlin.jvm  width 
kotlin.jvm  	Alignment 
kotlin.ranges  Arrangement 
kotlin.ranges  Box 
kotlin.ranges  Build 
kotlin.ranges  Button 
kotlin.ranges  Card 
kotlin.ranges  CardDefaults 
kotlin.ranges  Checkbox 
kotlin.ranges  CircularProgressIndicator 
kotlin.ranges  Column 
kotlin.ranges  
Converters 
kotlin.ranges  DateOptionCard 
kotlin.ranges  DateTimeFormatter 
kotlin.ranges  
DayViewScreen 
kotlin.ranges  DividerColor 
kotlin.ranges  DropdownMenuItem 
kotlin.ranges  
ErrorColor 
kotlin.ranges  	Exception 
kotlin.ranges  ExperimentalMaterial3Api 
kotlin.ranges  ExposedDropdownMenuBox 
kotlin.ranges  ExposedDropdownMenuDefaults 
kotlin.ranges  
FontWeight 
kotlin.ranges  HorizontalDivider 
kotlin.ranges  Icon 
kotlin.ranges  
IconButton 
kotlin.ranges  Icons 
kotlin.ranges  IntRange 
kotlin.ranges  
LazyColumn 
kotlin.ranges  	LocalDate 
kotlin.ranges  	LocalTime 
kotlin.ranges  Locale 
kotlin.ranges  MEDIUM 
kotlin.ranges  
MaterialTheme 
kotlin.ranges  Modifier 
kotlin.ranges  MutableStateFlow 
kotlin.ranges  OTHER 
kotlin.ranges  OnConflictStrategy 
kotlin.ranges  OutlinedButton 
kotlin.ranges  OutlinedTextField 
kotlin.ranges  PrimaryBlue 
kotlin.ranges  Room 
kotlin.ranges  RoundedCornerShape 
kotlin.ranges  Row 
kotlin.ranges  Screen 
kotlin.ranges  SharingStarted 
kotlin.ranges  Spacer 
kotlin.ranges  Surface 
kotlin.ranges  System 
kotlin.ranges  Task 
kotlin.ranges  TaskCard 
kotlin.ranges  TaskCategory 
kotlin.ranges  TaskDatabase 
kotlin.ranges  TaskManagerApp 
kotlin.ranges  TaskManagerTheme 
kotlin.ranges  TaskPriority 
kotlin.ranges  TaskRepository 
kotlin.ranges  Text 
kotlin.ranges  	TextAlign 
kotlin.ranges  
TextButton 
kotlin.ranges  TextOverflow 
kotlin.ranges  
TextSecondary 
kotlin.ranges  	TextStyle 
kotlin.ranges  Volatile 
kotlin.ranges  WeekDayCard 
kotlin.ranges  WeekTaskItem 
kotlin.ranges  WeekViewScreen 
kotlin.ranges  WindowCompat 
kotlin.ranges  
_errorMessage 
kotlin.ranges  
_isLoading 
kotlin.ranges  androidx 
kotlin.ranges  any 
kotlin.ranges  asStateFlow 
kotlin.ranges  
background 
kotlin.ranges  
clearError 
kotlin.ranges  clip 
kotlin.ranges  com 
kotlin.ranges  currentBackStackEntryAsState 
kotlin.ranges  	emptyList 
kotlin.ranges  fillMaxSize 
kotlin.ranges  fillMaxWidth 
kotlin.ranges  filter 
kotlin.ranges  find 
kotlin.ranges  findStartDestination 
kotlin.ranges  
flatMapLatest 
kotlin.ranges  forEach 
kotlin.ranges  getCategoryColor 
kotlin.ranges  getPriorityColor 
kotlin.ranges  getValue 
kotlin.ranges  height 
kotlin.ranges  heightIn 
kotlin.ranges  isBlank 
kotlin.ranges  
isNotBlank 
kotlin.ranges  
isNotEmpty 
kotlin.ranges  
isNullOrBlank 
kotlin.ranges  java 
kotlin.ranges  launch 
kotlin.ranges  let 
kotlin.ranges  listOf 
kotlin.ranges  map 
kotlin.ranges  mutableStateOf 
kotlin.ranges  padding 
kotlin.ranges  provideDelegate 
kotlin.ranges  random 
kotlin.ranges  remember 
kotlin.ranges  rememberScrollState 
kotlin.ranges  
repository 
kotlin.ranges  setValue 
kotlin.ranges  size 
kotlin.ranges  stateIn 
kotlin.ranges  synchronized 
kotlin.ranges  take 
kotlin.ranges  takeIf 
kotlin.ranges  toIntOrNull 
kotlin.ranges  trim 
kotlin.ranges  values 
kotlin.ranges  verticalScroll 
kotlin.ranges  width 
kotlin.ranges  map kotlin.ranges.IntProgression  random kotlin.ranges.IntProgression  getMAP kotlin.ranges.IntRange  getMap kotlin.ranges.IntRange  	getRANDOM kotlin.ranges.IntRange  	getRandom kotlin.ranges.IntRange  map kotlin.ranges.IntRange  random kotlin.ranges.IntRange  KClass kotlin.reflect  getJAVA kotlin.reflect.KClass  getJava kotlin.reflect.KClass  java kotlin.reflect.KClass  	Alignment kotlin.sequences  Arrangement kotlin.sequences  Box kotlin.sequences  Build kotlin.sequences  Button kotlin.sequences  Card kotlin.sequences  CardDefaults kotlin.sequences  Checkbox kotlin.sequences  CircularProgressIndicator kotlin.sequences  Column kotlin.sequences  
Converters kotlin.sequences  DateOptionCard kotlin.sequences  DateTimeFormatter kotlin.sequences  
DayViewScreen kotlin.sequences  DividerColor kotlin.sequences  DropdownMenuItem kotlin.sequences  
ErrorColor kotlin.sequences  	Exception kotlin.sequences  ExperimentalMaterial3Api kotlin.sequences  ExposedDropdownMenuBox kotlin.sequences  ExposedDropdownMenuDefaults kotlin.sequences  
FontWeight kotlin.sequences  HorizontalDivider kotlin.sequences  Icon kotlin.sequences  
IconButton kotlin.sequences  Icons kotlin.sequences  
LazyColumn kotlin.sequences  	LocalDate kotlin.sequences  	LocalTime kotlin.sequences  Locale kotlin.sequences  MEDIUM kotlin.sequences  
MaterialTheme kotlin.sequences  Modifier kotlin.sequences  MutableStateFlow kotlin.sequences  OTHER kotlin.sequences  OnConflictStrategy kotlin.sequences  OutlinedButton kotlin.sequences  OutlinedTextField kotlin.sequences  PrimaryBlue kotlin.sequences  Room kotlin.sequences  RoundedCornerShape kotlin.sequences  Row kotlin.sequences  Screen kotlin.sequences  SharingStarted kotlin.sequences  Spacer kotlin.sequences  Surface kotlin.sequences  System kotlin.sequences  Task kotlin.sequences  TaskCard kotlin.sequences  TaskCategory kotlin.sequences  TaskDatabase kotlin.sequences  TaskManagerApp kotlin.sequences  TaskManagerTheme kotlin.sequences  TaskPriority kotlin.sequences  TaskRepository kotlin.sequences  Text kotlin.sequences  	TextAlign kotlin.sequences  
TextButton kotlin.sequences  TextOverflow kotlin.sequences  
TextSecondary kotlin.sequences  	TextStyle kotlin.sequences  Volatile kotlin.sequences  WeekDayCard kotlin.sequences  WeekTaskItem kotlin.sequences  WeekViewScreen kotlin.sequences  WindowCompat kotlin.sequences  
_errorMessage kotlin.sequences  
_isLoading kotlin.sequences  androidx kotlin.sequences  any kotlin.sequences  asStateFlow kotlin.sequences  
background kotlin.sequences  
clearError kotlin.sequences  clip kotlin.sequences  com kotlin.sequences  currentBackStackEntryAsState kotlin.sequences  	emptyList kotlin.sequences  fillMaxSize kotlin.sequences  fillMaxWidth kotlin.sequences  filter kotlin.sequences  find kotlin.sequences  findStartDestination kotlin.sequences  
flatMapLatest kotlin.sequences  forEach kotlin.sequences  getCategoryColor kotlin.sequences  getPriorityColor kotlin.sequences  getValue kotlin.sequences  height kotlin.sequences  heightIn kotlin.sequences  isBlank kotlin.sequences  
isNotBlank kotlin.sequences  
isNotEmpty kotlin.sequences  
isNullOrBlank kotlin.sequences  java kotlin.sequences  launch kotlin.sequences  let kotlin.sequences  listOf kotlin.sequences  map kotlin.sequences  mutableStateOf kotlin.sequences  padding kotlin.sequences  provideDelegate kotlin.sequences  random kotlin.sequences  remember kotlin.sequences  rememberScrollState kotlin.sequences  
repository kotlin.sequences  setValue kotlin.sequences  size kotlin.sequences  stateIn kotlin.sequences  synchronized kotlin.sequences  take kotlin.sequences  takeIf kotlin.sequences  toIntOrNull kotlin.sequences  trim kotlin.sequences  values kotlin.sequences  verticalScroll kotlin.sequences  width kotlin.sequences  any kotlin.sequences.Sequence  getANY kotlin.sequences.Sequence  getAny kotlin.sequences.Sequence  	Alignment kotlin.text  Arrangement kotlin.text  Box kotlin.text  Build kotlin.text  Button kotlin.text  Card kotlin.text  CardDefaults kotlin.text  Checkbox kotlin.text  CircularProgressIndicator kotlin.text  Column kotlin.text  
Converters kotlin.text  DateOptionCard kotlin.text  DateTimeFormatter kotlin.text  
DayViewScreen kotlin.text  DividerColor kotlin.text  DropdownMenuItem kotlin.text  
ErrorColor kotlin.text  	Exception kotlin.text  ExperimentalMaterial3Api kotlin.text  ExposedDropdownMenuBox kotlin.text  ExposedDropdownMenuDefaults kotlin.text  
FontWeight kotlin.text  HorizontalDivider kotlin.text  Icon kotlin.text  
IconButton kotlin.text  Icons kotlin.text  
LazyColumn kotlin.text  	LocalDate kotlin.text  	LocalTime kotlin.text  Locale kotlin.text  MEDIUM kotlin.text  
MaterialTheme kotlin.text  Modifier kotlin.text  MutableStateFlow kotlin.text  OTHER kotlin.text  OnConflictStrategy kotlin.text  OutlinedButton kotlin.text  OutlinedTextField kotlin.text  PrimaryBlue kotlin.text  Room kotlin.text  RoundedCornerShape kotlin.text  Row kotlin.text  Screen kotlin.text  SharingStarted kotlin.text  Spacer kotlin.text  Surface kotlin.text  System kotlin.text  Task kotlin.text  TaskCard kotlin.text  TaskCategory kotlin.text  TaskDatabase kotlin.text  TaskManagerApp kotlin.text  TaskManagerTheme kotlin.text  TaskPriority kotlin.text  TaskRepository kotlin.text  Text kotlin.text  	TextAlign kotlin.text  
TextButton kotlin.text  TextOverflow kotlin.text  
TextSecondary kotlin.text  	TextStyle kotlin.text  Volatile kotlin.text  WeekDayCard kotlin.text  WeekTaskItem kotlin.text  WeekViewScreen kotlin.text  WindowCompat kotlin.text  
_errorMessage kotlin.text  
_isLoading kotlin.text  androidx kotlin.text  any kotlin.text  asStateFlow kotlin.text  
background kotlin.text  
clearError kotlin.text  clip kotlin.text  com kotlin.text  currentBackStackEntryAsState kotlin.text  	emptyList kotlin.text  fillMaxSize kotlin.text  fillMaxWidth kotlin.text  filter kotlin.text  find kotlin.text  findStartDestination kotlin.text  
flatMapLatest kotlin.text  forEach kotlin.text  getCategoryColor kotlin.text  getPriorityColor kotlin.text  getValue kotlin.text  height kotlin.text  heightIn kotlin.text  isBlank kotlin.text  
isNotBlank kotlin.text  
isNotEmpty kotlin.text  
isNullOrBlank kotlin.text  java kotlin.text  launch kotlin.text  let kotlin.text  listOf kotlin.text  map kotlin.text  mutableStateOf kotlin.text  padding kotlin.text  provideDelegate kotlin.text  random kotlin.text  remember kotlin.text  rememberScrollState kotlin.text  
repository kotlin.text  setValue kotlin.text  size kotlin.text  stateIn kotlin.text  synchronized kotlin.text  take kotlin.text  takeIf kotlin.text  toIntOrNull kotlin.text  trim kotlin.text  values kotlin.text  verticalScroll kotlin.text  width kotlin.text  CoroutineScope kotlinx.coroutines  Job kotlinx.coroutines  launch kotlinx.coroutines  
_errorMessage !kotlinx.coroutines.CoroutineScope  
_isLoading !kotlinx.coroutines.CoroutineScope  
clearError !kotlinx.coroutines.CoroutineScope  
getCLEARError !kotlinx.coroutines.CoroutineScope  
getClearError !kotlinx.coroutines.CoroutineScope  	getLAUNCH !kotlinx.coroutines.CoroutineScope  	getLaunch !kotlinx.coroutines.CoroutineScope  
getREPOSITORY !kotlinx.coroutines.CoroutineScope  
getRepository !kotlinx.coroutines.CoroutineScope  get_errorMessage !kotlinx.coroutines.CoroutineScope  
get_isLoading !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  
repository !kotlinx.coroutines.CoroutineScope  	Exception kotlinx.coroutines.flow  Flow kotlinx.coroutines.flow  	LocalDate kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  SharingStarted kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  TaskDatabase kotlinx.coroutines.flow  TaskRepository kotlinx.coroutines.flow  
_errorMessage kotlinx.coroutines.flow  
_isLoading kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  
clearError kotlinx.coroutines.flow  	emptyList kotlinx.coroutines.flow  
flatMapLatest kotlinx.coroutines.flow  launch kotlinx.coroutines.flow  
repository kotlinx.coroutines.flow  stateIn kotlinx.coroutines.flow  viewModelScope kotlinx.coroutines.flow  
getSTATEIn kotlinx.coroutines.flow.Flow  
getStateIn kotlinx.coroutines.flow.Flow  stateIn kotlinx.coroutines.flow.Flow  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getASStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getAsStateFlow (kotlinx.coroutines.flow.MutableStateFlow  value (kotlinx.coroutines.flow.MutableStateFlow  WhileSubscribed &kotlinx.coroutines.flow.SharingStarted  WhileSubscribed 0kotlinx.coroutines.flow.SharingStarted.Companion  collectAsState !kotlinx.coroutines.flow.StateFlow  
flatMapLatest !kotlinx.coroutines.flow.StateFlow  getCOLLECTAsState !kotlinx.coroutines.flow.StateFlow  getCollectAsState !kotlinx.coroutines.flow.StateFlow  getFLATMapLatest !kotlinx.coroutines.flow.StateFlow  getFlatMapLatest !kotlinx.coroutines.flow.StateFlow  	Parcelize kotlinx.parcelize  DayScheduleView com.taskmanager.ui.components  TimePickerDialog com.taskmanager.ui.components  	startTime com.taskmanager.data.model.Task  endTime com.taskmanager.data.model.Task  durationMinutes com.taskmanager.data.model.Task  timeDisplay com.taskmanager.data.model.Task  Canvas androidx.compose.foundation  ButtonDefaults "androidx.compose.foundation.layout  Canvas "androidx.compose.foundation.layout  Color "androidx.compose.foundation.layout  DayScheduleView "androidx.compose.foundation.layout  HourSlot "androidx.compose.foundation.layout  Offset "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  TaskTimeSlot "androidx.compose.foundation.layout  
fillMaxHeight "androidx.compose.foundation.layout  format "androidx.compose.foundation.layout  invoke "androidx.compose.foundation.layout  java "androidx.compose.foundation.layout  offset "androidx.compose.foundation.layout  sortedBy "androidx.compose.foundation.layout  step "androidx.compose.foundation.layout  to "androidx.compose.foundation.layout  toList "androidx.compose.foundation.layout  SpaceEvenly .androidx.compose.foundation.layout.Arrangement  Canvas +androidx.compose.foundation.layout.BoxScope  Color +androidx.compose.foundation.layout.BoxScope  Offset +androidx.compose.foundation.layout.BoxScope  String +androidx.compose.foundation.layout.BoxScope  TaskTimeSlot +androidx.compose.foundation.layout.BoxScope  fillMaxSize +androidx.compose.foundation.layout.BoxScope  format +androidx.compose.foundation.layout.BoxScope  getFILLMaxSize +androidx.compose.foundation.layout.BoxScope  	getFORMAT +androidx.compose.foundation.layout.BoxScope  getFillMaxSize +androidx.compose.foundation.layout.BoxScope  	getFormat +androidx.compose.foundation.layout.BoxScope  
getPADDING +androidx.compose.foundation.layout.BoxScope  
getPadding +androidx.compose.foundation.layout.BoxScope  invoke +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  ButtonDefaults .androidx.compose.foundation.layout.ColumnScope  Color .androidx.compose.foundation.layout.ColumnScope  DayScheduleView .androidx.compose.foundation.layout.ColumnScope  	LocalTime .androidx.compose.foundation.layout.ColumnScope  String .androidx.compose.foundation.layout.ColumnScope  format .androidx.compose.foundation.layout.ColumnScope  	getFORMAT .androidx.compose.foundation.layout.ColumnScope  	getFormat .androidx.compose.foundation.layout.ColumnScope  getJAVA .androidx.compose.foundation.layout.ColumnScope  getJava .androidx.compose.foundation.layout.ColumnScope  	getLISTOf .androidx.compose.foundation.layout.ColumnScope  	getListOf .androidx.compose.foundation.layout.ColumnScope  getSTEP .androidx.compose.foundation.layout.ColumnScope  getStep .androidx.compose.foundation.layout.ColumnScope  getTO .androidx.compose.foundation.layout.ColumnScope  	getTOList .androidx.compose.foundation.layout.ColumnScope  getTo .androidx.compose.foundation.layout.ColumnScope  	getToList .androidx.compose.foundation.layout.ColumnScope  invoke .androidx.compose.foundation.layout.ColumnScope  java .androidx.compose.foundation.layout.ColumnScope  listOf .androidx.compose.foundation.layout.ColumnScope  step .androidx.compose.foundation.layout.ColumnScope  to .androidx.compose.foundation.layout.ColumnScope  toList .androidx.compose.foundation.layout.ColumnScope  ButtonDefaults +androidx.compose.foundation.layout.RowScope  Canvas +androidx.compose.foundation.layout.RowScope  Color +androidx.compose.foundation.layout.RowScope  
LazyColumn +androidx.compose.foundation.layout.RowScope  	LocalTime +androidx.compose.foundation.layout.RowScope  Offset +androidx.compose.foundation.layout.RowScope  OutlinedTextField +androidx.compose.foundation.layout.RowScope  String +androidx.compose.foundation.layout.RowScope  TaskTimeSlot +androidx.compose.foundation.layout.RowScope  
fillMaxHeight +androidx.compose.foundation.layout.RowScope  fillMaxSize +androidx.compose.foundation.layout.RowScope  format +androidx.compose.foundation.layout.RowScope  getFILLMaxHeight +androidx.compose.foundation.layout.RowScope  getFILLMaxSize +androidx.compose.foundation.layout.RowScope  	getFORMAT +androidx.compose.foundation.layout.RowScope  getFillMaxHeight +androidx.compose.foundation.layout.RowScope  getFillMaxSize +androidx.compose.foundation.layout.RowScope  	getFormat +androidx.compose.foundation.layout.RowScope  	getHEIGHT +androidx.compose.foundation.layout.RowScope  	getHeight +androidx.compose.foundation.layout.RowScope  getJAVA +androidx.compose.foundation.layout.RowScope  getJava +androidx.compose.foundation.layout.RowScope  	getLISTOf +androidx.compose.foundation.layout.RowScope  	getListOf +androidx.compose.foundation.layout.RowScope  getSTEP +androidx.compose.foundation.layout.RowScope  getStep +androidx.compose.foundation.layout.RowScope  getTO +androidx.compose.foundation.layout.RowScope  	getTOList +androidx.compose.foundation.layout.RowScope  getTo +androidx.compose.foundation.layout.RowScope  	getToList +androidx.compose.foundation.layout.RowScope  height +androidx.compose.foundation.layout.RowScope  invoke +androidx.compose.foundation.layout.RowScope  items +androidx.compose.foundation.layout.RowScope  java +androidx.compose.foundation.layout.RowScope  listOf +androidx.compose.foundation.layout.RowScope  step +androidx.compose.foundation.layout.RowScope  to +androidx.compose.foundation.layout.RowScope  toList +androidx.compose.foundation.layout.RowScope  ButtonDefaults .androidx.compose.foundation.lazy.LazyItemScope  
FontWeight .androidx.compose.foundation.lazy.LazyItemScope  HourSlot .androidx.compose.foundation.lazy.LazyItemScope  
MaterialTheme .androidx.compose.foundation.lazy.LazyItemScope  String .androidx.compose.foundation.lazy.LazyItemScope  Text .androidx.compose.foundation.lazy.LazyItemScope  
TextButton .androidx.compose.foundation.lazy.LazyItemScope  format .androidx.compose.foundation.lazy.LazyItemScope  	getFORMAT .androidx.compose.foundation.lazy.LazyItemScope  	getFormat .androidx.compose.foundation.lazy.LazyItemScope  ButtonDefaults .androidx.compose.foundation.lazy.LazyListScope  
FontWeight .androidx.compose.foundation.lazy.LazyListScope  HourSlot .androidx.compose.foundation.lazy.LazyListScope  
MaterialTheme .androidx.compose.foundation.lazy.LazyListScope  String .androidx.compose.foundation.lazy.LazyListScope  Text .androidx.compose.foundation.lazy.LazyListScope  
TextButton .androidx.compose.foundation.lazy.LazyListScope  format .androidx.compose.foundation.lazy.LazyListScope  	getFORMAT .androidx.compose.foundation.lazy.LazyListScope  	getFormat .androidx.compose.foundation.lazy.LazyListScope  getSTEP .androidx.compose.foundation.lazy.LazyListScope  getStep .androidx.compose.foundation.lazy.LazyListScope  	getTOList .androidx.compose.foundation.lazy.LazyListScope  	getToList .androidx.compose.foundation.lazy.LazyListScope  invoke .androidx.compose.foundation.lazy.LazyListScope  step .androidx.compose.foundation.lazy.LazyListScope  toList .androidx.compose.foundation.lazy.LazyListScope  ButtonColors androidx.compose.material3  ButtonDefaults androidx.compose.material3  Canvas androidx.compose.material3  Color androidx.compose.material3  DayScheduleView androidx.compose.material3  HourSlot androidx.compose.material3  Offset androidx.compose.material3  
PaddingValues androidx.compose.material3  String androidx.compose.material3  TaskTimeSlot androidx.compose.material3  
fillMaxHeight androidx.compose.material3  format androidx.compose.material3  invoke androidx.compose.material3  java androidx.compose.material3  offset androidx.compose.material3  sortedBy androidx.compose.material3  step androidx.compose.material3  to androidx.compose.material3  toList androidx.compose.material3  textButtonColors )androidx.compose.material3.ButtonDefaults  error &androidx.compose.material3.ColorScheme  outline &androidx.compose.material3.ColorScheme  
displayMedium %androidx.compose.material3.Typography  labelMedium %androidx.compose.material3.Typography  ButtonDefaults androidx.compose.runtime  Canvas androidx.compose.runtime  Color androidx.compose.runtime  DayScheduleView androidx.compose.runtime  HourSlot androidx.compose.runtime  Offset androidx.compose.runtime  
PaddingValues androidx.compose.runtime  String androidx.compose.runtime  TaskTimeSlot androidx.compose.runtime  
fillMaxHeight androidx.compose.runtime  format androidx.compose.runtime  invoke androidx.compose.runtime  java androidx.compose.runtime  offset androidx.compose.runtime  sortedBy androidx.compose.runtime  step androidx.compose.runtime  to androidx.compose.runtime  toList androidx.compose.runtime  TopStart androidx.compose.ui.Alignment  TopStart 'androidx.compose.ui.Alignment.Companion  
fillMaxHeight androidx.compose.ui.Modifier  getFILLMaxHeight androidx.compose.ui.Modifier  getFILLMaxSize androidx.compose.ui.Modifier  getFillMaxHeight androidx.compose.ui.Modifier  getFillMaxSize androidx.compose.ui.Modifier  	getHEIGHT androidx.compose.ui.Modifier  	getHeight androidx.compose.ui.Modifier  	getOFFSET androidx.compose.ui.Modifier  	getOffset androidx.compose.ui.Modifier  offset androidx.compose.ui.Modifier  Offset androidx.compose.ui.geometry  Size androidx.compose.ui.geometry  invoke -androidx.compose.ui.geometry.Offset.Companion  width !androidx.compose.ui.geometry.Size  White "androidx.compose.ui.graphics.Color  White ,androidx.compose.ui.graphics.Color.Companion  	DrawScope &androidx.compose.ui.graphics.drawscope  Color 0androidx.compose.ui.graphics.drawscope.DrawScope  Offset 0androidx.compose.ui.graphics.drawscope.DrawScope  dp 0androidx.compose.ui.graphics.drawscope.DrawScope  drawLine 0androidx.compose.ui.graphics.drawscope.DrawScope  invoke 0androidx.compose.ui.graphics.drawscope.DrawScope  size 0androidx.compose.ui.graphics.drawscope.DrawScope  toPx 0androidx.compose.ui.graphics.drawscope.DrawScope  getTOPx androidx.compose.ui.unit.Dp  getToPx androidx.compose.ui.unit.Dp  toPx androidx.compose.ui.unit.Dp  fallbackToDestructiveMigration "androidx.room.RoomDatabase.Builder  java com.taskmanager.data.model  getJAVA com.taskmanager.data.model.Task  getJava com.taskmanager.data.model.Task  java com.taskmanager.data.model.Task  ButtonDefaults com.taskmanager.ui.components  Canvas com.taskmanager.ui.components  Color com.taskmanager.ui.components  HourSlot com.taskmanager.ui.components  List com.taskmanager.ui.components  Offset com.taskmanager.ui.components  
PaddingValues com.taskmanager.ui.components  String com.taskmanager.ui.components  TaskTimeSlot com.taskmanager.ui.components  
TextButton com.taskmanager.ui.components  com com.taskmanager.ui.components  
fillMaxHeight com.taskmanager.ui.components  fillMaxSize com.taskmanager.ui.components  filter com.taskmanager.ui.components  format com.taskmanager.ui.components  invoke com.taskmanager.ui.components  java com.taskmanager.ui.components  listOf com.taskmanager.ui.components  offset com.taskmanager.ui.components  sortedBy com.taskmanager.ui.components  step com.taskmanager.ui.components  to com.taskmanager.ui.components  toList com.taskmanager.ui.components  width com.taskmanager.ui.components  DayScheduleView com.taskmanager.ui.screens  Canvas com.taskmanager.ui.theme  Color com.taskmanager.ui.theme  HourSlot com.taskmanager.ui.theme  Offset com.taskmanager.ui.theme  
PaddingValues com.taskmanager.ui.theme  String com.taskmanager.ui.theme  TaskTimeSlot com.taskmanager.ui.theme  
fillMaxHeight com.taskmanager.ui.theme  format com.taskmanager.ui.theme  offset com.taskmanager.ui.theme  sortedBy com.taskmanager.ui.theme  toList com.taskmanager.ui.theme  ButtonDefaults 	java.lang  Canvas 	java.lang  Color 	java.lang  DayScheduleView 	java.lang  HourSlot 	java.lang  Offset 	java.lang  String 	java.lang  TaskTimeSlot 	java.lang  
fillMaxHeight 	java.lang  format 	java.lang  invoke 	java.lang  sortedBy 	java.lang  step 	java.lang  to 	java.lang  toList 	java.lang  getHOUR java.time.LocalTime  getHour java.time.LocalTime  	getMINUTE java.time.LocalTime  	getMinute java.time.LocalTime  hour java.time.LocalTime  isAfter java.time.LocalTime  minute java.time.LocalTime  	plusHours java.time.LocalTime  setHour java.time.LocalTime  	setMinute java.time.LocalTime  DayScheduleView 	java.util  ButtonDefaults kotlin  Canvas kotlin  Color kotlin  DayScheduleView kotlin  HourSlot kotlin  Offset kotlin  Pair kotlin  TaskTimeSlot kotlin  
fillMaxHeight kotlin  format kotlin  invoke kotlin  sortedBy kotlin  step kotlin  to kotlin  toList kotlin  getDP kotlin.Float  getDp kotlin.Float  
component1 kotlin.Pair  
component2 kotlin.Pair  getTO 
kotlin.String  getTo 
kotlin.String  	getFORMAT kotlin.String.Companion  	getFormat kotlin.String.Companion  ButtonDefaults kotlin.annotation  Canvas kotlin.annotation  Color kotlin.annotation  DayScheduleView kotlin.annotation  HourSlot kotlin.annotation  Offset kotlin.annotation  String kotlin.annotation  TaskTimeSlot kotlin.annotation  
fillMaxHeight kotlin.annotation  format kotlin.annotation  invoke kotlin.annotation  sortedBy kotlin.annotation  step kotlin.annotation  to kotlin.annotation  toList kotlin.annotation  ButtonDefaults kotlin.collections  Canvas kotlin.collections  Color kotlin.collections  DayScheduleView kotlin.collections  HourSlot kotlin.collections  Offset kotlin.collections  String kotlin.collections  TaskTimeSlot kotlin.collections  
fillMaxHeight kotlin.collections  format kotlin.collections  invoke kotlin.collections  sortedBy kotlin.collections  step kotlin.collections  to kotlin.collections  toList kotlin.collections  getSORTEDBy kotlin.collections.List  getSortedBy kotlin.collections.List  ButtonDefaults kotlin.comparisons  Canvas kotlin.comparisons  Color kotlin.comparisons  DayScheduleView kotlin.comparisons  HourSlot kotlin.comparisons  Offset kotlin.comparisons  String kotlin.comparisons  TaskTimeSlot kotlin.comparisons  
fillMaxHeight kotlin.comparisons  format kotlin.comparisons  invoke kotlin.comparisons  sortedBy kotlin.comparisons  step kotlin.comparisons  to kotlin.comparisons  toList kotlin.comparisons  ButtonDefaults 	kotlin.io  Canvas 	kotlin.io  Color 	kotlin.io  DayScheduleView 	kotlin.io  HourSlot 	kotlin.io  Offset 	kotlin.io  String 	kotlin.io  TaskTimeSlot 	kotlin.io  
fillMaxHeight 	kotlin.io  format 	kotlin.io  invoke 	kotlin.io  sortedBy 	kotlin.io  step 	kotlin.io  to 	kotlin.io  toList 	kotlin.io  ButtonDefaults 
kotlin.jvm  Canvas 
kotlin.jvm  Color 
kotlin.jvm  DayScheduleView 
kotlin.jvm  HourSlot 
kotlin.jvm  Offset 
kotlin.jvm  String 
kotlin.jvm  TaskTimeSlot 
kotlin.jvm  
fillMaxHeight 
kotlin.jvm  format 
kotlin.jvm  invoke 
kotlin.jvm  sortedBy 
kotlin.jvm  step 
kotlin.jvm  to 
kotlin.jvm  toList 
kotlin.jvm  ButtonDefaults 
kotlin.ranges  Canvas 
kotlin.ranges  Color 
kotlin.ranges  DayScheduleView 
kotlin.ranges  HourSlot 
kotlin.ranges  IntProgression 
kotlin.ranges  Offset 
kotlin.ranges  String 
kotlin.ranges  TaskTimeSlot 
kotlin.ranges  
fillMaxHeight 
kotlin.ranges  format 
kotlin.ranges  invoke 
kotlin.ranges  sortedBy 
kotlin.ranges  step 
kotlin.ranges  to 
kotlin.ranges  toList 
kotlin.ranges  	getTOList kotlin.ranges.IntProgression  	getToList kotlin.ranges.IntProgression  step kotlin.ranges.IntProgression  toList kotlin.ranges.IntProgression  getSTEP kotlin.ranges.IntRange  getStep kotlin.ranges.IntRange  	getTOList kotlin.ranges.IntRange  	getToList kotlin.ranges.IntRange  step kotlin.ranges.IntRange  toList kotlin.ranges.IntRange  ButtonDefaults kotlin.sequences  Canvas kotlin.sequences  Color kotlin.sequences  DayScheduleView kotlin.sequences  HourSlot kotlin.sequences  Offset kotlin.sequences  String kotlin.sequences  TaskTimeSlot kotlin.sequences  
fillMaxHeight kotlin.sequences  format kotlin.sequences  invoke kotlin.sequences  sortedBy kotlin.sequences  step kotlin.sequences  to kotlin.sequences  toList kotlin.sequences  ButtonDefaults kotlin.text  Canvas kotlin.text  Color kotlin.text  DayScheduleView kotlin.text  HourSlot kotlin.text  Offset kotlin.text  String kotlin.text  TaskTimeSlot kotlin.text  
fillMaxHeight kotlin.text  format kotlin.text  invoke kotlin.text  sortedBy kotlin.text  step kotlin.text  to kotlin.text  toList kotlin.text  themeDao *com.taskmanager.data.database.TaskDatabase  ColorPickerDialog com.taskmanager.ui.components  PresetThemes com.taskmanager.data.model  
ThemeSettings com.taskmanager.Screen  ThemeDao com.taskmanager.data.database  GlobalTheme com.taskmanager.ui.theme  ThemeSettingsScreen com.taskmanager.ui.screens  
ThemeSettings com.taskmanager.data.model  ProvideCustomTheme com.taskmanager.ui.theme  ThemeManager com.taskmanager.ui.theme  GlobalTheme android.app.Activity  ProvideCustomTheme android.app.Activity  GlobalTheme android.content.Context  ProvideCustomTheme android.content.Context  GlobalTheme android.content.ContextWrapper  ProvideCustomTheme android.content.ContextWrapper  GlobalTheme  android.view.ContextThemeWrapper  ProvideCustomTheme  android.view.ContextThemeWrapper  GlobalTheme #androidx.activity.ComponentActivity  ProvideCustomTheme #androidx.activity.ComponentActivity  GlobalTheme /androidx.compose.animation.AnimatedContentScope  ThemeSettingsScreen /androidx.compose.animation.AnimatedContentScope  border androidx.compose.foundation  BackgroundColorSection "androidx.compose.foundation.layout  Brush "androidx.compose.foundation.layout  CategoryColorSection "androidx.compose.foundation.layout  CircleShape "androidx.compose.foundation.layout  	ColorItem "androidx.compose.foundation.layout  ColorSettingItem "androidx.compose.foundation.layout  GlobalTheme "androidx.compose.foundation.layout  	GridCells "androidx.compose.foundation.layout  LazyRow "androidx.compose.foundation.layout  LazyVerticalGrid "androidx.compose.foundation.layout  PresetThemeItem "androidx.compose.foundation.layout  PresetThemeSection "androidx.compose.foundation.layout  PresetThemes "androidx.compose.foundation.layout  PriorityColorSection "androidx.compose.foundation.layout  ProvideCustomTheme "androidx.compose.foundation.layout  ThemeSettingsScreen "androidx.compose.foundation.layout  	TopAppBar "androidx.compose.foundation.layout  TopAppBarDefaults "androidx.compose.foundation.layout  border "androidx.compose.foundation.layout  	luminance "androidx.compose.foundation.layout  Check +androidx.compose.foundation.layout.BoxScope  Icon +androidx.compose.foundation.layout.BoxScope  Icons +androidx.compose.foundation.layout.BoxScope  getLUMINANCE +androidx.compose.foundation.layout.BoxScope  getLuminance +androidx.compose.foundation.layout.BoxScope  getSIZE +androidx.compose.foundation.layout.BoxScope  getSize +androidx.compose.foundation.layout.BoxScope  	luminance +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  	ArrowBack .androidx.compose.foundation.layout.ColumnScope  BackgroundColorSection .androidx.compose.foundation.layout.ColumnScope  Brush .androidx.compose.foundation.layout.ColumnScope  CategoryColorSection .androidx.compose.foundation.layout.ColumnScope  CircleShape .androidx.compose.foundation.layout.ColumnScope  	ColorItem .androidx.compose.foundation.layout.ColumnScope  ColorSettingItem .androidx.compose.foundation.layout.ColumnScope  	GridCells .androidx.compose.foundation.layout.ColumnScope  LazyRow .androidx.compose.foundation.layout.ColumnScope  LazyVerticalGrid .androidx.compose.foundation.layout.ColumnScope  PresetThemeItem .androidx.compose.foundation.layout.ColumnScope  PresetThemeSection .androidx.compose.foundation.layout.ColumnScope  PresetThemes .androidx.compose.foundation.layout.ColumnScope  PriorityColorSection .androidx.compose.foundation.layout.ColumnScope  	TopAppBar .androidx.compose.foundation.layout.ColumnScope  TopAppBarDefaults .androidx.compose.foundation.layout.ColumnScope  border .androidx.compose.foundation.layout.ColumnScope  	getBORDER .androidx.compose.foundation.layout.ColumnScope  	getBorder .androidx.compose.foundation.layout.ColumnScope  CircleShape +androidx.compose.foundation.layout.RowScope  ColorSettingItem +androidx.compose.foundation.layout.RowScope  Palette +androidx.compose.foundation.layout.RowScope  Row +androidx.compose.foundation.layout.RowScope  border +androidx.compose.foundation.layout.RowScope  	getBORDER +androidx.compose.foundation.layout.RowScope  	getBorder +androidx.compose.foundation.layout.RowScope  LazyRow  androidx.compose.foundation.lazy  BackgroundColorSection .androidx.compose.foundation.lazy.LazyItemScope  Button .androidx.compose.foundation.lazy.LazyItemScope  Card .androidx.compose.foundation.lazy.LazyItemScope  CardDefaults .androidx.compose.foundation.lazy.LazyItemScope  CategoryColorSection .androidx.compose.foundation.lazy.LazyItemScope  Column .androidx.compose.foundation.lazy.LazyItemScope  PresetThemeItem .androidx.compose.foundation.lazy.LazyItemScope  PresetThemeSection .androidx.compose.foundation.lazy.LazyItemScope  PriorityColorSection .androidx.compose.foundation.lazy.LazyItemScope  fillMaxWidth .androidx.compose.foundation.lazy.LazyItemScope  getFILLMaxWidth .androidx.compose.foundation.lazy.LazyItemScope  getFillMaxWidth .androidx.compose.foundation.lazy.LazyItemScope  
getPADDING .androidx.compose.foundation.lazy.LazyItemScope  
getPadding .androidx.compose.foundation.lazy.LazyItemScope  padding .androidx.compose.foundation.lazy.LazyItemScope  BackgroundColorSection .androidx.compose.foundation.lazy.LazyListScope  Button .androidx.compose.foundation.lazy.LazyListScope  Card .androidx.compose.foundation.lazy.LazyListScope  CardDefaults .androidx.compose.foundation.lazy.LazyListScope  CategoryColorSection .androidx.compose.foundation.lazy.LazyListScope  Column .androidx.compose.foundation.lazy.LazyListScope  PresetThemeItem .androidx.compose.foundation.lazy.LazyListScope  PresetThemeSection .androidx.compose.foundation.lazy.LazyListScope  PriorityColorSection .androidx.compose.foundation.lazy.LazyListScope  fillMaxWidth .androidx.compose.foundation.lazy.LazyListScope  getFILLMaxWidth .androidx.compose.foundation.lazy.LazyListScope  getFillMaxWidth .androidx.compose.foundation.lazy.LazyListScope  
getPADDING .androidx.compose.foundation.lazy.LazyListScope  
getPadding .androidx.compose.foundation.lazy.LazyListScope  padding .androidx.compose.foundation.lazy.LazyListScope  	GridCells %androidx.compose.foundation.lazy.grid  LazyGridItemScope %androidx.compose.foundation.lazy.grid  
LazyGridScope %androidx.compose.foundation.lazy.grid  LazyVerticalGrid %androidx.compose.foundation.lazy.grid  items %androidx.compose.foundation.lazy.grid  Fixed /androidx.compose.foundation.lazy.grid.GridCells  	ColorItem 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  	ColorItem 3androidx.compose.foundation.lazy.grid.LazyGridScope  getITEMS 3androidx.compose.foundation.lazy.grid.LazyGridScope  getItems 3androidx.compose.foundation.lazy.grid.LazyGridScope  items 3androidx.compose.foundation.lazy.grid.LazyGridScope  CircleShape !androidx.compose.foundation.shape  	ArrowBack ,androidx.compose.material.icons.Icons.Filled  Palette ,androidx.compose.material.icons.Icons.Filled  Settings ,androidx.compose.material.icons.Icons.Filled  	ArrowBack &androidx.compose.material.icons.filled  Palette &androidx.compose.material.icons.filled  Settings &androidx.compose.material.icons.filled  BackgroundColorSection androidx.compose.material3  Brush androidx.compose.material3  CategoryColorSection androidx.compose.material3  CircleShape androidx.compose.material3  	ColorItem androidx.compose.material3  ColorSettingItem androidx.compose.material3  GlobalTheme androidx.compose.material3  	GridCells androidx.compose.material3  LazyRow androidx.compose.material3  LazyVerticalGrid androidx.compose.material3  PresetThemeItem androidx.compose.material3  PresetThemeSection androidx.compose.material3  PresetThemes androidx.compose.material3  PriorityColorSection androidx.compose.material3  ProvideCustomTheme androidx.compose.material3  ThemeSettingsScreen androidx.compose.material3  	TopAppBar androidx.compose.material3  TopAppBarColors androidx.compose.material3  TopAppBarDefaults androidx.compose.material3  border androidx.compose.material3  	luminance androidx.compose.material3  buttonColors )androidx.compose.material3.ButtonDefaults  topAppBarColors ,androidx.compose.material3.TopAppBarDefaults  BackgroundColorSection androidx.compose.runtime  Brush androidx.compose.runtime  CategoryColorSection androidx.compose.runtime  CircleShape androidx.compose.runtime  	ColorItem androidx.compose.runtime  ColorSettingItem androidx.compose.runtime  GlobalTheme androidx.compose.runtime  	GridCells androidx.compose.runtime  IllegalStateException androidx.compose.runtime  LazyRow androidx.compose.runtime  LazyVerticalGrid androidx.compose.runtime  MutableStateFlow androidx.compose.runtime  PresetThemeItem androidx.compose.runtime  PresetThemeSection androidx.compose.runtime  PresetThemes androidx.compose.runtime  PriorityColorSection androidx.compose.runtime  ProvideCustomTheme androidx.compose.runtime  	StateFlow androidx.compose.runtime  TaskDatabase androidx.compose.runtime  ThemeManager androidx.compose.runtime  ThemeSettingsScreen androidx.compose.runtime  	TopAppBar androidx.compose.runtime  TopAppBarDefaults androidx.compose.runtime  
_currentTheme androidx.compose.runtime  asStateFlow androidx.compose.runtime  border androidx.compose.runtime  launch androidx.compose.runtime  	luminance androidx.compose.runtime  themeDao androidx.compose.runtime  viewModelScope androidx.compose.runtime  border androidx.compose.ui.Modifier  	getBORDER androidx.compose.ui.Modifier  	getBorder androidx.compose.ui.Modifier  getSIZE androidx.compose.ui.Modifier  getSize androidx.compose.ui.Modifier  	clickable &androidx.compose.ui.Modifier.Companion  getCLICKABLE &androidx.compose.ui.Modifier.Companion  getClickable &androidx.compose.ui.Modifier.Companion  Black "androidx.compose.ui.graphics.Color  Blue "androidx.compose.ui.graphics.Color  Transparent "androidx.compose.ui.graphics.Color  blue "androidx.compose.ui.graphics.Color  equals "androidx.compose.ui.graphics.Color  getLUMINANCE "androidx.compose.ui.graphics.Color  getLuminance "androidx.compose.ui.graphics.Color  green "androidx.compose.ui.graphics.Color  	luminance "androidx.compose.ui.graphics.Color  red "androidx.compose.ui.graphics.Color  value "androidx.compose.ui.graphics.Color  Black ,androidx.compose.ui.graphics.Color.Companion  Blue ,androidx.compose.ui.graphics.Color.Companion  Transparent ,androidx.compose.ui.graphics.Color.Companion  GlobalTheme #androidx.core.app.ComponentActivity  ProvideCustomTheme #androidx.core.app.ComponentActivity  	ViewModel androidx.lifecycle  Color androidx.lifecycle.ViewModel  Context androidx.lifecycle.ViewModel  PresetThemes androidx.lifecycle.ViewModel  
ThemeSettings androidx.lifecycle.ViewModel  
_currentTheme androidx.lifecycle.ViewModel  applyPresetTheme androidx.lifecycle.ViewModel  com androidx.lifecycle.ViewModel  resetToDefault androidx.lifecycle.ViewModel  themeDao androidx.lifecycle.ViewModel  updateBackgroundColors androidx.lifecycle.ViewModel  updateCategoryColor androidx.lifecycle.ViewModel  updatePriorityColor androidx.lifecycle.ViewModel  updateTheme androidx.lifecycle.ViewModel  popBackStack !androidx.navigation.NavController  GlobalTheme #androidx.navigation.NavGraphBuilder  ThemeSettingsScreen #androidx.navigation.NavGraphBuilder  popBackStack %androidx.navigation.NavHostController  ThemeDao androidx.room.RoomDatabase  themeDao androidx.room.RoomDatabase  GlobalTheme com.taskmanager  ProvideCustomTheme com.taskmanager  ThemeSettingsScreen com.taskmanager  com com.taskmanager  GlobalTheme com.taskmanager.MainActivity  ProvideCustomTheme com.taskmanager.MainActivity  Settings com.taskmanager.Screen  Icons $com.taskmanager.Screen.ThemeSettings  Settings $com.taskmanager.Screen.ThemeSettings  route $com.taskmanager.Screen.ThemeSettings  
ThemeSettings com.taskmanager.data.database  ThemeDao *com.taskmanager.data.database.TaskDatabase  ThemeDao 4com.taskmanager.data.database.TaskDatabase.Companion  Flow &com.taskmanager.data.database.ThemeDao  Insert &com.taskmanager.data.database.ThemeDao  OnConflictStrategy &com.taskmanager.data.database.ThemeDao  Query &com.taskmanager.data.database.ThemeDao  
ThemeSettings &com.taskmanager.data.database.ThemeDao  Update &com.taskmanager.data.database.ThemeDao  getThemeSettings &com.taskmanager.data.database.ThemeDao  insertThemeSettings &com.taskmanager.data.database.ThemeDao  Color com.taskmanager.data.model  List com.taskmanager.data.model  listOf com.taskmanager.data.model  toArgb com.taskmanager.data.model  Color 'com.taskmanager.data.model.PresetThemes  
ThemeSettings 'com.taskmanager.data.model.PresetThemes  	darkTheme 'com.taskmanager.data.model.PresetThemes  defaultTheme 'com.taskmanager.data.model.PresetThemes  	getTOArgb 'com.taskmanager.data.model.PresetThemes  	getToArgb 'com.taskmanager.data.model.PresetThemes  
greenTheme 'com.taskmanager.data.model.PresetThemes  invoke 'com.taskmanager.data.model.PresetThemes  orangeTheme 'com.taskmanager.data.model.PresetThemes  purpleTheme 'com.taskmanager.data.model.PresetThemes  toArgb 'com.taskmanager.data.model.PresetThemes  Color (com.taskmanager.data.model.ThemeSettings  Int (com.taskmanager.data.model.ThemeSettings  List (com.taskmanager.data.model.ThemeSettings  
PrimaryKey (com.taskmanager.data.model.ThemeSettings  TaskCategory (com.taskmanager.data.model.ThemeSettings  TaskPriority (com.taskmanager.data.model.ThemeSettings  backgroundEndColor (com.taskmanager.data.model.ThemeSettings  backgroundEndColorCompose (com.taskmanager.data.model.ThemeSettings  backgroundStartColor (com.taskmanager.data.model.ThemeSettings  backgroundStartColorCompose (com.taskmanager.data.model.ThemeSettings  cardBackgroundColor (com.taskmanager.data.model.ThemeSettings  cardBackgroundColorCompose (com.taskmanager.data.model.ThemeSettings  cardBackgroundDarkColor (com.taskmanager.data.model.ThemeSettings  copy (com.taskmanager.data.model.ThemeSettings  getCategoryColor (com.taskmanager.data.model.ThemeSettings  	getLISTOf (com.taskmanager.data.model.ThemeSettings  	getListOf (com.taskmanager.data.model.ThemeSettings  getPriorityColor (com.taskmanager.data.model.ThemeSettings  gradientColors (com.taskmanager.data.model.ThemeSettings  healthCategoryColor (com.taskmanager.data.model.ThemeSettings  healthCategoryColorCompose (com.taskmanager.data.model.ThemeSettings  highPriorityColor (com.taskmanager.data.model.ThemeSettings  highPriorityColorCompose (com.taskmanager.data.model.ThemeSettings  invoke (com.taskmanager.data.model.ThemeSettings  listOf (com.taskmanager.data.model.ThemeSettings  lowPriorityColor (com.taskmanager.data.model.ThemeSettings  lowPriorityColorCompose (com.taskmanager.data.model.ThemeSettings  mediumPriorityColor (com.taskmanager.data.model.ThemeSettings  mediumPriorityColorCompose (com.taskmanager.data.model.ThemeSettings  onTaskTextColor (com.taskmanager.data.model.ThemeSettings  otherCategoryColor (com.taskmanager.data.model.ThemeSettings  otherCategoryColorCompose (com.taskmanager.data.model.ThemeSettings  personalCategoryColor (com.taskmanager.data.model.ThemeSettings  personalCategoryColorCompose (com.taskmanager.data.model.ThemeSettings  primaryColor (com.taskmanager.data.model.ThemeSettings  primaryTextColor (com.taskmanager.data.model.ThemeSettings  primaryVariantColor (com.taskmanager.data.model.ThemeSettings  secondaryTextColor (com.taskmanager.data.model.ThemeSettings  studyCategoryColor (com.taskmanager.data.model.ThemeSettings  studyCategoryColorCompose (com.taskmanager.data.model.ThemeSettings  toArgb (com.taskmanager.data.model.ThemeSettings  workCategoryColor (com.taskmanager.data.model.ThemeSettings  workCategoryColorCompose (com.taskmanager.data.model.ThemeSettings  CircleShape com.taskmanager.ui.components  	ColorItem com.taskmanager.ui.components  Float com.taskmanager.ui.components  	GridCells com.taskmanager.ui.components  LazyVerticalGrid com.taskmanager.ui.components  border com.taskmanager.ui.components  	luminance com.taskmanager.ui.components  BackgroundColorSection com.taskmanager.ui.screens  Brush com.taskmanager.ui.screens  ButtonDefaults com.taskmanager.ui.screens  CategoryColorSection com.taskmanager.ui.screens  CircleShape com.taskmanager.ui.screens  Color com.taskmanager.ui.screens  ColorSettingItem com.taskmanager.ui.screens  LazyRow com.taskmanager.ui.screens  PresetThemeItem com.taskmanager.ui.screens  PresetThemeSection com.taskmanager.ui.screens  PresetThemes com.taskmanager.ui.screens  PriorityColorSection com.taskmanager.ui.screens  String com.taskmanager.ui.screens  TaskCategory com.taskmanager.ui.screens  TaskPriority com.taskmanager.ui.screens  	TopAppBar com.taskmanager.ui.screens  TopAppBarDefaults com.taskmanager.ui.screens  border com.taskmanager.ui.screens  clip com.taskmanager.ui.screens  listOf com.taskmanager.ui.screens  to com.taskmanager.ui.screens  IllegalStateException com.taskmanager.ui.theme  MutableStateFlow com.taskmanager.ui.theme  PresetThemes com.taskmanager.ui.theme  	StateFlow com.taskmanager.ui.theme  TaskDatabase com.taskmanager.ui.theme  
_currentTheme com.taskmanager.ui.theme  asStateFlow com.taskmanager.ui.theme  launch com.taskmanager.ui.theme  themeDao com.taskmanager.ui.theme  viewModelScope com.taskmanager.ui.theme  Context $com.taskmanager.ui.theme.GlobalTheme  IllegalStateException $com.taskmanager.ui.theme.GlobalTheme  ThemeManager $com.taskmanager.ui.theme.GlobalTheme  
_themeManager $com.taskmanager.ui.theme.GlobalTheme  getThemeManager $com.taskmanager.ui.theme.GlobalTheme  
initialize $com.taskmanager.ui.theme.GlobalTheme  Color %com.taskmanager.ui.theme.ThemeManager  Context %com.taskmanager.ui.theme.ThemeManager  MutableStateFlow %com.taskmanager.ui.theme.ThemeManager  PresetThemes %com.taskmanager.ui.theme.ThemeManager  	StateFlow %com.taskmanager.ui.theme.ThemeManager  TaskDatabase %com.taskmanager.ui.theme.ThemeManager  
ThemeSettings %com.taskmanager.ui.theme.ThemeManager  
_currentTheme %com.taskmanager.ui.theme.ThemeManager  applyPresetTheme %com.taskmanager.ui.theme.ThemeManager  asStateFlow %com.taskmanager.ui.theme.ThemeManager  com %com.taskmanager.ui.theme.ThemeManager  currentTheme %com.taskmanager.ui.theme.ThemeManager  equals %com.taskmanager.ui.theme.ThemeManager  getASStateFlow %com.taskmanager.ui.theme.ThemeManager  getAsStateFlow %com.taskmanager.ui.theme.ThemeManager  getCOM %com.taskmanager.ui.theme.ThemeManager  getCom %com.taskmanager.ui.theme.ThemeManager  	getLAUNCH %com.taskmanager.ui.theme.ThemeManager  	getLaunch %com.taskmanager.ui.theme.ThemeManager  getVIEWModelScope %com.taskmanager.ui.theme.ThemeManager  getViewModelScope %com.taskmanager.ui.theme.ThemeManager  launch %com.taskmanager.ui.theme.ThemeManager  resetToDefault %com.taskmanager.ui.theme.ThemeManager  themeDao %com.taskmanager.ui.theme.ThemeManager  updateBackgroundColors %com.taskmanager.ui.theme.ThemeManager  updateCategoryColor %com.taskmanager.ui.theme.ThemeManager  updatePriorityColor %com.taskmanager.ui.theme.ThemeManager  updateTheme %com.taskmanager.ui.theme.ThemeManager  viewModelScope %com.taskmanager.ui.theme.ThemeManager  BackgroundColorSection 	java.lang  Brush 	java.lang  CategoryColorSection 	java.lang  CircleShape 	java.lang  	ColorItem 	java.lang  ColorSettingItem 	java.lang  GlobalTheme 	java.lang  	GridCells 	java.lang  IllegalStateException 	java.lang  LazyRow 	java.lang  LazyVerticalGrid 	java.lang  PresetThemeItem 	java.lang  PresetThemeSection 	java.lang  PresetThemes 	java.lang  PriorityColorSection 	java.lang  ProvideCustomTheme 	java.lang  ThemeManager 	java.lang  
ThemeSettings 	java.lang  ThemeSettingsScreen 	java.lang  	TopAppBar 	java.lang  TopAppBarDefaults 	java.lang  
_currentTheme 	java.lang  border 	java.lang  	luminance 	java.lang  themeDao 	java.lang  toArgb 	java.lang  BackgroundColorSection kotlin  Brush kotlin  CategoryColorSection kotlin  CircleShape kotlin  	ColorItem kotlin  ColorSettingItem kotlin  GlobalTheme kotlin  	GridCells kotlin  IllegalStateException kotlin  LazyRow kotlin  LazyVerticalGrid kotlin  PresetThemeItem kotlin  PresetThemeSection kotlin  PresetThemes kotlin  PriorityColorSection kotlin  ProvideCustomTheme kotlin  ThemeManager kotlin  
ThemeSettings kotlin  ThemeSettingsScreen kotlin  	TopAppBar kotlin  TopAppBarDefaults kotlin  
_currentTheme kotlin  border kotlin  	luminance kotlin  themeDao kotlin  toArgb kotlin  toInt kotlin.ULong  BackgroundColorSection kotlin.annotation  Brush kotlin.annotation  CategoryColorSection kotlin.annotation  CircleShape kotlin.annotation  	ColorItem kotlin.annotation  ColorSettingItem kotlin.annotation  GlobalTheme kotlin.annotation  	GridCells kotlin.annotation  IllegalStateException kotlin.annotation  LazyRow kotlin.annotation  LazyVerticalGrid kotlin.annotation  PresetThemeItem kotlin.annotation  PresetThemeSection kotlin.annotation  PresetThemes kotlin.annotation  PriorityColorSection kotlin.annotation  ProvideCustomTheme kotlin.annotation  ThemeManager kotlin.annotation  
ThemeSettings kotlin.annotation  ThemeSettingsScreen kotlin.annotation  	TopAppBar kotlin.annotation  TopAppBarDefaults kotlin.annotation  
_currentTheme kotlin.annotation  border kotlin.annotation  	luminance kotlin.annotation  themeDao kotlin.annotation  toArgb kotlin.annotation  BackgroundColorSection kotlin.collections  Brush kotlin.collections  CategoryColorSection kotlin.collections  CircleShape kotlin.collections  	ColorItem kotlin.collections  ColorSettingItem kotlin.collections  GlobalTheme kotlin.collections  	GridCells kotlin.collections  IllegalStateException kotlin.collections  LazyRow kotlin.collections  LazyVerticalGrid kotlin.collections  PresetThemeItem kotlin.collections  PresetThemeSection kotlin.collections  PresetThemes kotlin.collections  PriorityColorSection kotlin.collections  ProvideCustomTheme kotlin.collections  ThemeManager kotlin.collections  
ThemeSettings kotlin.collections  ThemeSettingsScreen kotlin.collections  	TopAppBar kotlin.collections  TopAppBarDefaults kotlin.collections  
_currentTheme kotlin.collections  border kotlin.collections  	luminance kotlin.collections  themeDao kotlin.collections  toArgb kotlin.collections  BackgroundColorSection kotlin.comparisons  Brush kotlin.comparisons  CategoryColorSection kotlin.comparisons  CircleShape kotlin.comparisons  	ColorItem kotlin.comparisons  ColorSettingItem kotlin.comparisons  GlobalTheme kotlin.comparisons  	GridCells kotlin.comparisons  IllegalStateException kotlin.comparisons  LazyRow kotlin.comparisons  LazyVerticalGrid kotlin.comparisons  PresetThemeItem kotlin.comparisons  PresetThemeSection kotlin.comparisons  PresetThemes kotlin.comparisons  PriorityColorSection kotlin.comparisons  ProvideCustomTheme kotlin.comparisons  ThemeManager kotlin.comparisons  
ThemeSettings kotlin.comparisons  ThemeSettingsScreen kotlin.comparisons  	TopAppBar kotlin.comparisons  TopAppBarDefaults kotlin.comparisons  
_currentTheme kotlin.comparisons  border kotlin.comparisons  	luminance kotlin.comparisons  themeDao kotlin.comparisons  toArgb kotlin.comparisons  BackgroundColorSection 	kotlin.io  Brush 	kotlin.io  CategoryColorSection 	kotlin.io  CircleShape 	kotlin.io  	ColorItem 	kotlin.io  ColorSettingItem 	kotlin.io  GlobalTheme 	kotlin.io  	GridCells 	kotlin.io  IllegalStateException 	kotlin.io  LazyRow 	kotlin.io  LazyVerticalGrid 	kotlin.io  PresetThemeItem 	kotlin.io  PresetThemeSection 	kotlin.io  PresetThemes 	kotlin.io  PriorityColorSection 	kotlin.io  ProvideCustomTheme 	kotlin.io  ThemeManager 	kotlin.io  
ThemeSettings 	kotlin.io  ThemeSettingsScreen 	kotlin.io  	TopAppBar 	kotlin.io  TopAppBarDefaults 	kotlin.io  
_currentTheme 	kotlin.io  border 	kotlin.io  	luminance 	kotlin.io  themeDao 	kotlin.io  toArgb 	kotlin.io  BackgroundColorSection 
kotlin.jvm  Brush 
kotlin.jvm  CategoryColorSection 
kotlin.jvm  CircleShape 
kotlin.jvm  	ColorItem 
kotlin.jvm  ColorSettingItem 
kotlin.jvm  GlobalTheme 
kotlin.jvm  	GridCells 
kotlin.jvm  IllegalStateException 
kotlin.jvm  LazyRow 
kotlin.jvm  LazyVerticalGrid 
kotlin.jvm  PresetThemeItem 
kotlin.jvm  PresetThemeSection 
kotlin.jvm  PresetThemes 
kotlin.jvm  PriorityColorSection 
kotlin.jvm  ProvideCustomTheme 
kotlin.jvm  ThemeManager 
kotlin.jvm  
ThemeSettings 
kotlin.jvm  ThemeSettingsScreen 
kotlin.jvm  	TopAppBar 
kotlin.jvm  TopAppBarDefaults 
kotlin.jvm  
_currentTheme 
kotlin.jvm  border 
kotlin.jvm  	luminance 
kotlin.jvm  themeDao 
kotlin.jvm  toArgb 
kotlin.jvm  BackgroundColorSection 
kotlin.ranges  Brush 
kotlin.ranges  CategoryColorSection 
kotlin.ranges  CircleShape 
kotlin.ranges  	ColorItem 
kotlin.ranges  ColorSettingItem 
kotlin.ranges  GlobalTheme 
kotlin.ranges  	GridCells 
kotlin.ranges  IllegalStateException 
kotlin.ranges  LazyRow 
kotlin.ranges  LazyVerticalGrid 
kotlin.ranges  PresetThemeItem 
kotlin.ranges  PresetThemeSection 
kotlin.ranges  PresetThemes 
kotlin.ranges  PriorityColorSection 
kotlin.ranges  ProvideCustomTheme 
kotlin.ranges  ThemeManager 
kotlin.ranges  
ThemeSettings 
kotlin.ranges  ThemeSettingsScreen 
kotlin.ranges  	TopAppBar 
kotlin.ranges  TopAppBarDefaults 
kotlin.ranges  
_currentTheme 
kotlin.ranges  border 
kotlin.ranges  	luminance 
kotlin.ranges  themeDao 
kotlin.ranges  toArgb 
kotlin.ranges  BackgroundColorSection kotlin.sequences  Brush kotlin.sequences  CategoryColorSection kotlin.sequences  CircleShape kotlin.sequences  	ColorItem kotlin.sequences  ColorSettingItem kotlin.sequences  GlobalTheme kotlin.sequences  	GridCells kotlin.sequences  IllegalStateException kotlin.sequences  LazyRow kotlin.sequences  LazyVerticalGrid kotlin.sequences  PresetThemeItem kotlin.sequences  PresetThemeSection kotlin.sequences  PresetThemes kotlin.sequences  PriorityColorSection kotlin.sequences  ProvideCustomTheme kotlin.sequences  ThemeManager kotlin.sequences  
ThemeSettings kotlin.sequences  ThemeSettingsScreen kotlin.sequences  	TopAppBar kotlin.sequences  TopAppBarDefaults kotlin.sequences  
_currentTheme kotlin.sequences  border kotlin.sequences  	luminance kotlin.sequences  themeDao kotlin.sequences  toArgb kotlin.sequences  BackgroundColorSection kotlin.text  Brush kotlin.text  CategoryColorSection kotlin.text  CircleShape kotlin.text  	ColorItem kotlin.text  ColorSettingItem kotlin.text  GlobalTheme kotlin.text  	GridCells kotlin.text  IllegalStateException kotlin.text  LazyRow kotlin.text  LazyVerticalGrid kotlin.text  PresetThemeItem kotlin.text  PresetThemeSection kotlin.text  PresetThemes kotlin.text  PriorityColorSection kotlin.text  ProvideCustomTheme kotlin.text  ThemeManager kotlin.text  
ThemeSettings kotlin.text  ThemeSettingsScreen kotlin.text  	TopAppBar kotlin.text  TopAppBarDefaults kotlin.text  
_currentTheme kotlin.text  border kotlin.text  	luminance kotlin.text  themeDao kotlin.text  toArgb kotlin.text  PresetThemes !kotlinx.coroutines.CoroutineScope  
_currentTheme !kotlinx.coroutines.CoroutineScope  getTHEMEDao !kotlinx.coroutines.CoroutineScope  getThemeDao !kotlinx.coroutines.CoroutineScope  get_currentTheme !kotlinx.coroutines.CoroutineScope  themeDao !kotlinx.coroutines.CoroutineScope  
Composable kotlinx.coroutines.flow  IllegalStateException kotlinx.coroutines.flow  PresetThemes kotlinx.coroutines.flow  ThemeManager kotlinx.coroutines.flow  
_currentTheme kotlinx.coroutines.flow  collectAsState kotlinx.coroutines.flow  com kotlinx.coroutines.flow  getValue kotlinx.coroutines.flow  provideDelegate kotlinx.coroutines.flow  themeDao kotlinx.coroutines.flow  collect kotlinx.coroutines.flow.Flow  <SAM-CONSTRUCTOR> %kotlinx.coroutines.flow.FlowCollector  ColorDebugDialog com.taskmanager.ui.components  
ColorTestCard com.taskmanager.ui.components  
ColorTestCard "androidx.compose.foundation.layout  
FontFamily "androidx.compose.foundation.layout  Integer "androidx.compose.foundation.layout  toArgb "androidx.compose.foundation.layout  	uppercase "androidx.compose.foundation.layout  
ColorTestCard .androidx.compose.foundation.layout.ColumnScope  
FontFamily .androidx.compose.foundation.layout.ColumnScope  Integer .androidx.compose.foundation.layout.ColumnScope  getLUMINANCE .androidx.compose.foundation.layout.ColumnScope  getLuminance .androidx.compose.foundation.layout.ColumnScope  	getTOArgb .androidx.compose.foundation.layout.ColumnScope  	getToArgb .androidx.compose.foundation.layout.ColumnScope  getUPPERCASE .androidx.compose.foundation.layout.ColumnScope  getUppercase .androidx.compose.foundation.layout.ColumnScope  	luminance .androidx.compose.foundation.layout.ColumnScope  toArgb .androidx.compose.foundation.layout.ColumnScope  	uppercase .androidx.compose.foundation.layout.ColumnScope  
ColorTestCard .androidx.compose.foundation.lazy.LazyItemScope  
ColorTestCard .androidx.compose.foundation.lazy.LazyListScope  
ColorTestCard androidx.compose.material3  
FontFamily androidx.compose.material3  Integer androidx.compose.material3  toArgb androidx.compose.material3  	uppercase androidx.compose.material3  
ColorTestCard androidx.compose.runtime  
FontFamily androidx.compose.runtime  Integer androidx.compose.runtime  toArgb androidx.compose.runtime  	uppercase androidx.compose.runtime  alpha "androidx.compose.ui.graphics.Color  GenericFontFamily androidx.compose.ui.text.font  	Monospace (androidx.compose.ui.text.font.FontFamily  	Monospace 2androidx.compose.ui.text.font.FontFamily.Companion  toArgb androidx.lifecycle.ViewModel  	Exception com.taskmanager.data.model  	Exception (com.taskmanager.data.model.ThemeSettings  safeColorFromInt (com.taskmanager.data.model.ThemeSettings  
FontFamily com.taskmanager.ui.components  Integer com.taskmanager.ui.components  toArgb com.taskmanager.ui.components  	uppercase com.taskmanager.ui.components  
ColorTestCard com.taskmanager.ui.screens  toArgb com.taskmanager.ui.theme  	getTOArgb %com.taskmanager.ui.theme.ThemeManager  	getToArgb %com.taskmanager.ui.theme.ThemeManager  toArgb %com.taskmanager.ui.theme.ThemeManager  
ColorTestCard 	java.lang  
FontFamily 	java.lang  Integer 	java.lang  	uppercase 	java.lang  toHexString java.lang.Integer  
ColorTestCard kotlin  
FontFamily kotlin  Integer kotlin  	uppercase kotlin  getUPPERCASE 
kotlin.String  getUppercase 
kotlin.String  
ColorTestCard kotlin.annotation  
FontFamily kotlin.annotation  Integer kotlin.annotation  	uppercase kotlin.annotation  
ColorTestCard kotlin.collections  
FontFamily kotlin.collections  Integer kotlin.collections  	uppercase kotlin.collections  
ColorTestCard kotlin.comparisons  
FontFamily kotlin.comparisons  Integer kotlin.comparisons  	uppercase kotlin.comparisons  
ColorTestCard 	kotlin.io  
FontFamily 	kotlin.io  Integer 	kotlin.io  	uppercase 	kotlin.io  
ColorTestCard 
kotlin.jvm  
FontFamily 
kotlin.jvm  Integer 
kotlin.jvm  	uppercase 
kotlin.jvm  
ColorTestCard 
kotlin.ranges  
FontFamily 
kotlin.ranges  Integer 
kotlin.ranges  	uppercase 
kotlin.ranges  
ColorTestCard kotlin.sequences  
FontFamily kotlin.sequences  Integer kotlin.sequences  	uppercase kotlin.sequences  
ColorTestCard kotlin.text  
FontFamily kotlin.text  Integer kotlin.text  	uppercase kotlin.text  toArgb kotlinx.coroutines.flow  onTaskTextColorCompose (com.taskmanager.data.model.ThemeSettings  primaryColorCompose (com.taskmanager.data.model.ThemeSettings  primaryTextColorCompose (com.taskmanager.data.model.ThemeSettings  secondaryTextColorCompose (com.taskmanager.data.model.ThemeSettings                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 