  Activity android.app  Bundle android.app.Activity  
MaterialTheme android.app.Activity  Modifier android.app.Activity  Surface android.app.Activity  TaskManagerApp android.app.Activity  TaskManagerTheme android.app.Activity  fillMaxSize android.app.Activity  	getWINDOW android.app.Activity  	getWindow android.app.Activity  onCreate android.app.Activity  
setContent android.app.Activity  	setWindow android.app.Activity  window android.app.Activity  Context android.content  Bundle android.content.Context  
MaterialTheme android.content.Context  Modifier android.content.Context  Surface android.content.Context  TaskManagerApp android.content.Context  TaskManagerTheme android.content.Context  fillMaxSize android.content.Context  onCreate android.content.Context  
setContent android.content.Context  Bundle android.content.ContextWrapper  
MaterialTheme android.content.ContextWrapper  Modifier android.content.ContextWrapper  Surface android.content.ContextWrapper  TaskManagerApp android.content.ContextWrapper  TaskManagerTheme android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  onCreate android.content.ContextWrapper  
setContent android.content.ContextWrapper  Build 
android.os  Bundle 
android.os  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  View android.view  Window android.view  Bundle  android.view.ContextThemeWrapper  
MaterialTheme  android.view.ContextThemeWrapper  Modifier  android.view.ContextThemeWrapper  Surface  android.view.ContextThemeWrapper  TaskManagerApp  android.view.ContextThemeWrapper  TaskManagerTheme  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  onCreate  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  context android.view.View  
getCONTEXT android.view.View  
getContext android.view.View  getISInEditMode android.view.View  getIsInEditMode android.view.View  isInEditMode android.view.View  
setContext android.view.View  
setInEditMode android.view.View  getSTATUSBarColor android.view.Window  getStatusBarColor android.view.Window  setStatusBarColor android.view.Window  statusBarColor android.view.Window  ComponentActivity androidx.activity  Bundle #androidx.activity.ComponentActivity  
MaterialTheme #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  Surface #androidx.activity.ComponentActivity  TaskManagerApp #androidx.activity.ComponentActivity  TaskManagerTheme #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  isSystemInDarkTheme androidx.compose.foundation  Arrangement "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  Surface "androidx.compose.foundation.layout  TaskManagerApp "androidx.compose.foundation.layout  TaskManagerTheme "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  
setContent "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  Button .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  	getHEIGHT .androidx.compose.foundation.layout.ColumnScope  	getHeight .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  Text +androidx.compose.foundation.layout.RowScope  Arrangement androidx.compose.material3  Button androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  
FontWeight androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  Spacer androidx.compose.material3  Surface androidx.compose.material3  TaskManagerApp androidx.compose.material3  TaskManagerTheme androidx.compose.material3  Text androidx.compose.material3  
Typography androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  fillMaxSize androidx.compose.material3  height androidx.compose.material3  lightColorScheme androidx.compose.material3  padding androidx.compose.material3  
setContent androidx.compose.material3  
background &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  invoke (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  	bodyLarge %androidx.compose.material3.Typography  
headlineLarge %androidx.compose.material3.Typography  
Composable androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  
SideEffect androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  current 3androidx.compose.runtime.ProvidableCompositionLocal  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  CenterHorizontally androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  fillMaxSize androidx.compose.ui.Modifier  
getPADDING androidx.compose.ui.Modifier  
getPadding androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  fillMaxSize &androidx.compose.ui.Modifier.Companion  getFILLMaxSize &androidx.compose.ui.Modifier.Companion  getFillMaxSize &androidx.compose.ui.Modifier.Companion  	getHEIGHT &androidx.compose.ui.Modifier.Companion  	getHeight &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  Color androidx.compose.ui.graphics  toArgb androidx.compose.ui.graphics  	getTOArgb "androidx.compose.ui.graphics.Color  	getToArgb "androidx.compose.ui.graphics.Color  toArgb "androidx.compose.ui.graphics.Color  invoke ,androidx.compose.ui.graphics.Color.Companion  LocalContext androidx.compose.ui.platform  	LocalView androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  invoke ,androidx.compose.ui.text.TextStyle.Companion  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  SemiBold (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  SemiBold 2androidx.compose.ui.text.font.FontWeight.Companion  Preview #androidx.compose.ui.tooling.preview  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  Bundle #androidx.core.app.ComponentActivity  
MaterialTheme #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  Surface #androidx.core.app.ComponentActivity  TaskManagerApp #androidx.core.app.ComponentActivity  TaskManagerTheme #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  onCreate #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  WindowCompat androidx.core.view  getInsetsController androidx.core.view.WindowCompat  getISAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  getIsAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  isAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  setAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  Arrangement com.taskmanager  Button com.taskmanager  Column com.taskmanager  
FontWeight com.taskmanager  MainActivity com.taskmanager  
MaterialTheme com.taskmanager  Modifier com.taskmanager  Spacer com.taskmanager  Surface com.taskmanager  TaskManagerApp com.taskmanager  TaskManagerAppPreview com.taskmanager  TaskManagerTheme com.taskmanager  Text com.taskmanager  fillMaxSize com.taskmanager  height com.taskmanager  padding com.taskmanager  
setContent com.taskmanager  Bundle com.taskmanager.MainActivity  
MaterialTheme com.taskmanager.MainActivity  Modifier com.taskmanager.MainActivity  Surface com.taskmanager.MainActivity  TaskManagerApp com.taskmanager.MainActivity  TaskManagerTheme com.taskmanager.MainActivity  fillMaxSize com.taskmanager.MainActivity  getFILLMaxSize com.taskmanager.MainActivity  getFillMaxSize com.taskmanager.MainActivity  
getSETContent com.taskmanager.MainActivity  
getSetContent com.taskmanager.MainActivity  
setContent com.taskmanager.MainActivity  BackgroundGradientEnd com.taskmanager.ui.theme  BackgroundGradientStart com.taskmanager.ui.theme  Boolean com.taskmanager.ui.theme  Build com.taskmanager.ui.theme  CardBackground com.taskmanager.ui.theme  DarkColorScheme com.taskmanager.ui.theme  DividerColor com.taskmanager.ui.theme  
ErrorColor com.taskmanager.ui.theme  HealthCategoryColor com.taskmanager.ui.theme  HighPriorityColor com.taskmanager.ui.theme  LightColorScheme com.taskmanager.ui.theme  LowPriorityColor com.taskmanager.ui.theme  MediumPriorityColor com.taskmanager.ui.theme  OtherCategoryColor com.taskmanager.ui.theme  PersonalCategoryColor com.taskmanager.ui.theme  Pink40 com.taskmanager.ui.theme  Pink80 com.taskmanager.ui.theme  PrimaryBlue com.taskmanager.ui.theme  PrimaryBlueEnd com.taskmanager.ui.theme  Purple40 com.taskmanager.ui.theme  Purple80 com.taskmanager.ui.theme  PurpleGrey40 com.taskmanager.ui.theme  PurpleGrey80 com.taskmanager.ui.theme  StudyCategoryColor com.taskmanager.ui.theme  SuccessColor com.taskmanager.ui.theme  TaskManagerTheme com.taskmanager.ui.theme  
TextSecondary com.taskmanager.ui.theme  
Typography com.taskmanager.ui.theme  Unit com.taskmanager.ui.theme  WarningColor com.taskmanager.ui.theme  WindowCompat com.taskmanager.ui.theme  WorkCategoryColor com.taskmanager.ui.theme  Build 	java.lang  Button 	java.lang  
FontWeight 	java.lang  
MaterialTheme 	java.lang  Modifier 	java.lang  Spacer 	java.lang  Surface 	java.lang  TaskManagerApp 	java.lang  TaskManagerTheme 	java.lang  Text 	java.lang  WindowCompat 	java.lang  fillMaxSize 	java.lang  height 	java.lang  Boolean kotlin  Build kotlin  Button kotlin  Double kotlin  
FontWeight kotlin  	Function0 kotlin  	Function1 kotlin  Int kotlin  
MaterialTheme kotlin  Modifier kotlin  Spacer kotlin  String kotlin  Surface kotlin  TaskManagerApp kotlin  TaskManagerTheme kotlin  Text kotlin  Unit kotlin  WindowCompat kotlin  fillMaxSize kotlin  height kotlin  getSP 
kotlin.Double  getSp 
kotlin.Double  getDP 
kotlin.Int  getDp 
kotlin.Int  getSP 
kotlin.Int  getSp 
kotlin.Int  Build kotlin.annotation  Button kotlin.annotation  
FontWeight kotlin.annotation  
MaterialTheme kotlin.annotation  Modifier kotlin.annotation  Spacer kotlin.annotation  Surface kotlin.annotation  TaskManagerApp kotlin.annotation  TaskManagerTheme kotlin.annotation  Text kotlin.annotation  WindowCompat kotlin.annotation  fillMaxSize kotlin.annotation  height kotlin.annotation  Build kotlin.collections  Button kotlin.collections  
FontWeight kotlin.collections  
MaterialTheme kotlin.collections  Modifier kotlin.collections  Spacer kotlin.collections  Surface kotlin.collections  TaskManagerApp kotlin.collections  TaskManagerTheme kotlin.collections  Text kotlin.collections  WindowCompat kotlin.collections  fillMaxSize kotlin.collections  height kotlin.collections  Build kotlin.comparisons  Button kotlin.comparisons  
FontWeight kotlin.comparisons  
MaterialTheme kotlin.comparisons  Modifier kotlin.comparisons  Spacer kotlin.comparisons  Surface kotlin.comparisons  TaskManagerApp kotlin.comparisons  TaskManagerTheme kotlin.comparisons  Text kotlin.comparisons  WindowCompat kotlin.comparisons  fillMaxSize kotlin.comparisons  height kotlin.comparisons  Build 	kotlin.io  Button 	kotlin.io  
FontWeight 	kotlin.io  
MaterialTheme 	kotlin.io  Modifier 	kotlin.io  Spacer 	kotlin.io  Surface 	kotlin.io  TaskManagerApp 	kotlin.io  TaskManagerTheme 	kotlin.io  Text 	kotlin.io  WindowCompat 	kotlin.io  fillMaxSize 	kotlin.io  height 	kotlin.io  Build 
kotlin.jvm  Button 
kotlin.jvm  
FontWeight 
kotlin.jvm  
MaterialTheme 
kotlin.jvm  Modifier 
kotlin.jvm  Spacer 
kotlin.jvm  Surface 
kotlin.jvm  TaskManagerApp 
kotlin.jvm  TaskManagerTheme 
kotlin.jvm  Text 
kotlin.jvm  WindowCompat 
kotlin.jvm  fillMaxSize 
kotlin.jvm  height 
kotlin.jvm  Build 
kotlin.ranges  Button 
kotlin.ranges  
FontWeight 
kotlin.ranges  
MaterialTheme 
kotlin.ranges  Modifier 
kotlin.ranges  Spacer 
kotlin.ranges  Surface 
kotlin.ranges  TaskManagerApp 
kotlin.ranges  TaskManagerTheme 
kotlin.ranges  Text 
kotlin.ranges  WindowCompat 
kotlin.ranges  fillMaxSize 
kotlin.ranges  height 
kotlin.ranges  Build kotlin.sequences  Button kotlin.sequences  
FontWeight kotlin.sequences  
MaterialTheme kotlin.sequences  Modifier kotlin.sequences  Spacer kotlin.sequences  Surface kotlin.sequences  TaskManagerApp kotlin.sequences  TaskManagerTheme kotlin.sequences  Text kotlin.sequences  WindowCompat kotlin.sequences  fillMaxSize kotlin.sequences  height kotlin.sequences  Build kotlin.text  Button kotlin.text  
FontWeight kotlin.text  
MaterialTheme kotlin.text  Modifier kotlin.text  Spacer kotlin.text  Surface kotlin.text  TaskManagerApp kotlin.text  TaskManagerTheme kotlin.text  Text kotlin.text  WindowCompat kotlin.text  fillMaxSize kotlin.text  height kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                