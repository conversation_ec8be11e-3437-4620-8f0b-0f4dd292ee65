<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能任务清单</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- 头部导航 -->
        <header class="header">
            <div class="header-content">
                <h1 class="app-title">
                    <i class="fas fa-tasks"></i>
                    智能任务清单
                </h1>
                <div class="view-controls">
                    <button class="view-btn active" data-view="day">
                        <i class="fas fa-calendar-day"></i>
                        日视图
                    </button>
                    <button class="view-btn" data-view="week">
                        <i class="fas fa-calendar-week"></i>
                        周视图
                    </button>
                </div>
                <div class="date-navigation">
                    <button class="nav-btn" id="prevBtn">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <span class="current-date" id="currentDate"></span>
                    <button class="nav-btn" id="nextBtn">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                    <button class="nav-btn today-btn" id="todayBtn">今天</button>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 日视图 -->
            <div class="view-container day-view active" id="dayView">
                <div class="day-header">
                    <h2 class="day-title" id="dayTitle"></h2>
                    <div class="day-actions">
                        <button class="action-btn" id="addTaskBtn">
                            <i class="fas fa-plus"></i>
                            添加任务
                        </button>
                        <button class="action-btn secondary" id="copyDayBtn">
                            <i class="fas fa-copy"></i>
                            复制到其他天
                        </button>
                    </div>
                </div>
                <div class="timeline-container">
                    <div class="timeline" id="dayTimeline">
                        <!-- 时间轴将通过JavaScript生成 -->
                    </div>
                </div>
            </div>

            <!-- 周视图 -->
            <div class="view-container week-view" id="weekView">
                <div class="week-header">
                    <h2 class="week-title" id="weekTitle"></h2>
                    <button class="action-btn" id="addWeekTaskBtn">
                        <i class="fas fa-plus"></i>
                        添加任务
                    </button>
                </div>
                <div class="week-grid" id="weekGrid">
                    <!-- 周视图网格将通过JavaScript生成 -->
                </div>
            </div>
        </main>

        <!-- 任务表单模态框 -->
        <div class="modal" id="taskModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="modalTitle">添加任务</h3>
                    <button class="close-btn" id="closeModal">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form class="task-form" id="taskForm">
                    <div class="form-group">
                        <label for="taskTitle">任务标题</label>
                        <input type="text" id="taskTitle" required placeholder="输入任务标题">
                    </div>
                    <div class="form-group">
                        <label for="taskDescription">任务描述</label>
                        <textarea id="taskDescription" placeholder="输入任务描述（可选）"></textarea>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="taskDate">日期</label>
                            <input type="date" id="taskDate" required>
                        </div>
                        <div class="form-group">
                            <label for="taskTime">时间</label>
                            <input type="time" id="taskTime" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="taskDuration">持续时间（分钟）</label>
                            <input type="number" id="taskDuration" min="15" step="15" value="60">
                        </div>
                        <div class="form-group">
                            <label for="taskPriority">优先级</label>
                            <select id="taskPriority">
                                <option value="low">低</option>
                                <option value="medium" selected>中</option>
                                <option value="high">高</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="taskCategory">分类</label>
                        <select id="taskCategory">
                            <option value="work">工作</option>
                            <option value="personal">个人</option>
                            <option value="study">学习</option>
                            <option value="health">健康</option>
                            <option value="other">其他</option>
                        </select>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn secondary" id="cancelBtn">取消</button>
                        <button type="submit" class="btn primary">保存任务</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 复制任务模态框 -->
        <div class="modal" id="copyModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>复制任务到其他天</h3>
                    <button class="close-btn" id="closeCopyModal">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="copy-content">
                    <p>选择要复制到的日期：</p>
                    <div class="date-selector" id="dateSelector">
                        <!-- 日期选择器将通过JavaScript生成 -->
                    </div>
                    <div class="copy-actions">
                        <button type="button" class="btn secondary" id="cancelCopyBtn">取消</button>
                        <button type="button" class="btn primary" id="confirmCopyBtn">确认复制</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
