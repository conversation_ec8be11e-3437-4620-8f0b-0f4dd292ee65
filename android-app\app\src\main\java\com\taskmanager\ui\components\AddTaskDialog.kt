package com.taskmanager.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import com.taskmanager.data.model.Task
import com.taskmanager.data.model.TaskCategory
import com.taskmanager.data.model.TaskPriority
import java.time.LocalDate
import java.time.LocalTime

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddTaskDialog(
    currentDate: LocalDate,
    task: Task? = null, // 如果不为null则为编辑模式
    onDismiss: () -> Unit,
    onConfirm: (Task) -> Unit
) {
    val isEditing = task != null
    
    // 表单状态
    var title by remember { mutableStateOf(task?.title ?: "") }
    var description by remember { mutableStateOf(task?.description ?: "") }
    var selectedDate by remember { mutableStateOf(task?.date ?: currentDate) }
    var selectedTime by remember { mutableStateOf(task?.time ?: LocalTime.of(9, 0)) }
    var duration by remember { mutableStateOf(task?.duration?.toString() ?: "60") }
    var priority by remember { mutableStateOf(task?.priority ?: TaskPriority.MEDIUM) }
    var category by remember { mutableStateOf(task?.category ?: TaskCategory.WORK) }
    
    // 错误状态
    var titleError by remember { mutableStateOf(false) }
    var durationError by remember { mutableStateOf(false) }
    
    Dialog(onDismiss = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier
                    .padding(16.dp)
                    .verticalScroll(rememberScrollState())
            ) {
                // 标题
                Text(
                    text = if (isEditing) "编辑任务" else "添加任务",
                    style = MaterialTheme.typography.headlineSmall,
                    modifier = Modifier.padding(bottom = 16.dp)
                )
                
                // 任务标题
                OutlinedTextField(
                    value = title,
                    onValueChange = { 
                        title = it
                        titleError = false
                    },
                    label = { Text("任务标题 *") },
                    isError = titleError,
                    supportingText = if (titleError) {
                        { Text("请输入任务标题") }
                    } else null,
                    modifier = Modifier.fillMaxWidth()
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 任务描述
                OutlinedTextField(
                    value = description,
                    onValueChange = { description = it },
                    label = { Text("任务描述") },
                    modifier = Modifier.fillMaxWidth(),
                    minLines = 2,
                    maxLines = 3
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 持续时间
                OutlinedTextField(
                    value = duration,
                    onValueChange = { 
                        duration = it
                        durationError = false
                    },
                    label = { Text("持续时间（分钟）*") },
                    isError = durationError,
                    supportingText = if (durationError) {
                        { Text("请输入有效的持续时间") }
                    } else null,
                    modifier = Modifier.fillMaxWidth()
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 优先级选择
                var priorityExpanded by remember { mutableStateOf(false) }
                ExposedDropdownMenuBox(
                    expanded = priorityExpanded,
                    onExpandedChange = { priorityExpanded = !priorityExpanded }
                ) {
                    OutlinedTextField(
                        value = priority.displayName,
                        onValueChange = { },
                        readOnly = true,
                        label = { Text("优先级") },
                        trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = priorityExpanded) },
                        modifier = Modifier
                            .menuAnchor()
                            .fillMaxWidth()
                    )
                    ExposedDropdownMenu(
                        expanded = priorityExpanded,
                        onDismissRequest = { priorityExpanded = false }
                    ) {
                        TaskPriority.values().forEach { priorityOption ->
                            DropdownMenuItem(
                                text = { Text(priorityOption.displayName) },
                                onClick = {
                                    priority = priorityOption
                                    priorityExpanded = false
                                }
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 分类选择
                var categoryExpanded by remember { mutableStateOf(false) }
                ExposedDropdownMenuBox(
                    expanded = categoryExpanded,
                    onExpandedChange = { categoryExpanded = !categoryExpanded }
                ) {
                    OutlinedTextField(
                        value = category.displayName,
                        onValueChange = { },
                        readOnly = true,
                        label = { Text("分类") },
                        trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = categoryExpanded) },
                        modifier = Modifier
                            .menuAnchor()
                            .fillMaxWidth()
                    )
                    ExposedDropdownMenu(
                        expanded = categoryExpanded,
                        onDismissRequest = { categoryExpanded = false }
                    ) {
                        TaskCategory.values().forEach { categoryOption ->
                            DropdownMenuItem(
                                text = { Text(categoryOption.displayName) },
                                onClick = {
                                    category = categoryOption
                                    categoryExpanded = false
                                }
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    OutlinedButton(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("取消")
                    }
                    
                    Button(
                        onClick = {
                            // 验证表单
                            titleError = title.isBlank()
                            val durationInt = duration.toIntOrNull()
                            durationError = durationInt == null || durationInt <= 0
                            
                            if (!titleError && !durationError) {
                                val newTask = Task(
                                    id = task?.id ?: "${System.currentTimeMillis()}_${(1000..9999).random()}",
                                    title = title.trim(),
                                    description = description.trim().takeIf { it.isNotBlank() },
                                    date = selectedDate,
                                    time = selectedTime,
                                    duration = durationInt!!,
                                    priority = priority,
                                    category = category,
                                    completed = task?.completed ?: false
                                )
                                onConfirm(newTask)
                            }
                        },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(if (isEditing) "保存" else "添加")
                    }
                }
            }
        }
    }
}
