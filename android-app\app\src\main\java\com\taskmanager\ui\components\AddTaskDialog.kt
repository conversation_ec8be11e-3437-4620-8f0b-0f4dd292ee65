package com.taskmanager.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import com.taskmanager.data.model.Task
import com.taskmanager.data.model.TaskCategory
import com.taskmanager.data.model.TaskPriority
import java.time.LocalDate
import java.time.LocalTime

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddTaskDialog(
    currentDate: LocalDate,
    task: Task? = null, // 如果不为null则为编辑模式
    onDismiss: () -> Unit,
    onConfirm: (Task) -> Unit
) {
    val isEditing = task != null
    
    // 表单状态
    var title by remember { mutableStateOf(task?.title ?: "") }
    var description by remember { mutableStateOf(task?.description ?: "") }
    var selectedDate by remember { mutableStateOf(task?.date ?: currentDate) }
    var startTime by remember { mutableStateOf(task?.startTime ?: LocalTime.of(9, 0)) }
    var endTime by remember { mutableStateOf(task?.endTime ?: LocalTime.of(10, 0)) }
    var priority by remember { mutableStateOf(task?.priority ?: TaskPriority.MEDIUM) }
    var category by remember { mutableStateOf(task?.category ?: TaskCategory.WORK) }
    
    // 错误状态
    var titleError by remember { mutableStateOf(false) }
    var timeError by remember { mutableStateOf(false) }

    // 时间选择器状态
    var showStartTimePicker by remember { mutableStateOf(false) }
    var showEndTimePicker by remember { mutableStateOf(false) }
    
    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier
                    .padding(16.dp)
                    .verticalScroll(rememberScrollState())
            ) {
                // 标题
                Text(
                    text = if (isEditing) "编辑任务" else "添加任务",
                    style = MaterialTheme.typography.headlineSmall,
                    modifier = Modifier.padding(bottom = 16.dp)
                )
                
                // 任务标题
                OutlinedTextField(
                    value = title,
                    onValueChange = { 
                        title = it
                        titleError = false
                    },
                    label = { Text("任务标题 *") },
                    isError = titleError,
                    supportingText = if (titleError) {
                        { Text("请输入任务标题") }
                    } else null,
                    modifier = Modifier.fillMaxWidth()
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 任务描述
                OutlinedTextField(
                    value = description,
                    onValueChange = { description = it },
                    label = { Text("任务描述") },
                    modifier = Modifier.fillMaxWidth(),
                    minLines = 2,
                    maxLines = 3
                )
                
                Spacer(modifier = Modifier.height(8.dp))

                // 开始时间
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    OutlinedTextField(
                        value = startTime.format(java.time.format.DateTimeFormatter.ofPattern("HH:mm")),
                        onValueChange = { },
                        label = { Text("开始时间 *") },
                        readOnly = true,
                        isError = timeError,
                        modifier = Modifier.weight(1f),
                        trailingIcon = {
                            TextButton(onClick = {
                                showStartTimePicker = true
                            }) {
                                Text("选择")
                            }
                        }
                    )

                    OutlinedTextField(
                        value = endTime.format(java.time.format.DateTimeFormatter.ofPattern("HH:mm")),
                        onValueChange = { },
                        label = { Text("结束时间 *") },
                        readOnly = true,
                        isError = timeError,
                        modifier = Modifier.weight(1f),
                        trailingIcon = {
                            TextButton(onClick = {
                                showEndTimePicker = true
                            }) {
                                Text("选择")
                            }
                        }
                    )
                }

                if (timeError) {
                    Text(
                        text = "结束时间必须晚于开始时间",
                        color = MaterialTheme.colorScheme.error,
                        style = MaterialTheme.typography.bodySmall,
                        modifier = Modifier.padding(start = 16.dp, top = 4.dp)
                    )
                }

                // 显示持续时间
                val durationMinutes = if (endTime.isAfter(startTime)) {
                    val startMinutes = startTime.hour * 60 + startTime.minute
                    val endMinutes = endTime.hour * 60 + endTime.minute
                    endMinutes - startMinutes
                } else 0

                Text(
                    text = "持续时间：${durationMinutes}分钟",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.padding(start = 16.dp, top = 4.dp)
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 优先级选择
                var priorityExpanded by remember { mutableStateOf(false) }
                ExposedDropdownMenuBox(
                    expanded = priorityExpanded,
                    onExpandedChange = { priorityExpanded = !priorityExpanded }
                ) {
                    OutlinedTextField(
                        value = priority.displayName,
                        onValueChange = { },
                        readOnly = true,
                        label = { Text("优先级") },
                        trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = priorityExpanded) },
                        modifier = Modifier
                            .menuAnchor()
                            .fillMaxWidth()
                    )
                    ExposedDropdownMenu(
                        expanded = priorityExpanded,
                        onDismissRequest = { priorityExpanded = false }
                    ) {
                        TaskPriority.values().forEach { priorityOption ->
                            DropdownMenuItem(
                                text = { Text(priorityOption.displayName) },
                                onClick = {
                                    priority = priorityOption
                                    priorityExpanded = false
                                }
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 分类选择
                var categoryExpanded by remember { mutableStateOf(false) }
                ExposedDropdownMenuBox(
                    expanded = categoryExpanded,
                    onExpandedChange = { categoryExpanded = !categoryExpanded }
                ) {
                    OutlinedTextField(
                        value = category.displayName,
                        onValueChange = { },
                        readOnly = true,
                        label = { Text("分类") },
                        trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = categoryExpanded) },
                        modifier = Modifier
                            .menuAnchor()
                            .fillMaxWidth()
                    )
                    ExposedDropdownMenu(
                        expanded = categoryExpanded,
                        onDismissRequest = { categoryExpanded = false }
                    ) {
                        TaskCategory.values().forEach { categoryOption ->
                            DropdownMenuItem(
                                text = { Text(categoryOption.displayName) },
                                onClick = {
                                    category = categoryOption
                                    categoryExpanded = false
                                }
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    OutlinedButton(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("取消")
                    }
                    
                    Button(
                        onClick = {
                            // 验证表单
                            titleError = title.isBlank()
                            timeError = !endTime.isAfter(startTime)

                            if (!titleError && !timeError) {
                                val newTask = Task(
                                    id = task?.id ?: "${System.currentTimeMillis()}_${(1000..9999).random()}",
                                    title = title.trim(),
                                    description = description.trim().takeIf { it.isNotBlank() },
                                    date = selectedDate,
                                    startTime = startTime,
                                    endTime = endTime,
                                    priority = priority,
                                    category = category,
                                    completed = task?.completed ?: false
                                )
                                onConfirm(newTask)
                            }
                        },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(if (isEditing) "保存" else "添加")
                    }
                }
            }
        }
    }

    // 开始时间选择器
    if (showStartTimePicker) {
        TimePickerDialog(
            initialTime = startTime,
            onDismiss = { showStartTimePicker = false },
            onTimeSelected = { time ->
                startTime = time
                // 如果结束时间早于开始时间，自动调整结束时间
                if (!endTime.isAfter(startTime)) {
                    endTime = startTime.plusHours(1)
                }
                showStartTimePicker = false
                timeError = false
            }
        )
    }

    // 结束时间选择器
    if (showEndTimePicker) {
        TimePickerDialog(
            initialTime = endTime,
            onDismiss = { showEndTimePicker = false },
            onTimeSelected = { time ->
                endTime = time
                showEndTimePicker = false
                timeError = false
            }
        )
    }
}
